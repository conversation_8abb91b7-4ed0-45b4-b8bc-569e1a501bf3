import React, { createContext, useContext, useEffect, useState } from 'react';
import { FluentProvider, Theme } from '@fluentui/react-components';
import { useTheme } from '../hooks/useTheme';
import { globalEventManager, createSafeEventListener } from '../utils/eventManager';

/**
 * 主题上下文接口
 */
interface ThemeContextValue {
  theme: Theme;
  themeMode: string;
  isDark: boolean;
  isLight: boolean;
  isSystem: boolean;
  isUpdating: boolean;
  updateThemeMode: (mode: any) => void;
  toggleTheme: () => void;
  getThemeInfo: () => any;
}

/**
 * 主题上下文
 */
const ThemeContext = createContext<ThemeContextValue | undefined>(undefined);

/**
 * 优化的主题提供者组件
 * 
 * 特点：
 * - 移除强制key更新机制
 * - 使用防抖优化性能
 * - 统一事件管理
 * - 智能重渲染控制
 */
interface ThemeProviderProps {
  children: React.ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const themeHook = useTheme();
  const [isTransitioning, setIsTransitioning] = useState(false);

  /**
   * 监听主题变化事件
   */
  useEffect(() => {
    const handleThemeChange = createSafeEventListener((event: any) => {
      const { detail } = event;
      if (detail && detail.mode && detail.theme) {
        setIsTransitioning(true);
        
        // 重置过渡状态
        setTimeout(() => {
          setIsTransitioning(false);
        }, 150);
      }
    });

    globalEventManager.addListener(
      'theme-provider',
      window,
      'theme-changed',
      handleThemeChange
    );

    return () => {
      globalEventManager.removeListeners('theme-provider');
    };
  }, []);

  // 扩展的上下文值
  const contextValue: ThemeContextValue = {
    ...themeHook,
    isUpdating: themeHook.isUpdating || isTransitioning,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      <FluentProvider 
        theme={themeHook.theme}
        style={{
          transition: isTransitioning ? 'all 0.2s ease-in-out' : undefined,
        }}
      >
        {children}
      </FluentProvider>
    </ThemeContext.Provider>
  );
};

/**
 * 使用主题上下文的Hook
 */
export const useThemeContext = (): ThemeContextValue => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useThemeContext must be used within a ThemeProvider');
  }
  return context;
};

/**
 * 高阶组件：为组件提供主题上下文
 */
export function withTheme<P extends object>(
  Component: React.ComponentType<P>
): React.ComponentType<P> {
  const WithThemeComponent = (props: P) => {
    const theme = useThemeContext();
    return <Component {...props} theme={theme} />;
  };

  WithThemeComponent.displayName = `withTheme(${Component.displayName || Component.name})`;
  return WithThemeComponent;
}