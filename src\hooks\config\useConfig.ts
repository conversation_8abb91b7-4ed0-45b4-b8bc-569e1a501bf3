import { useEffect } from 'react';
import { useConfigState } from './useConfigState';
import { useAppSettings } from './useAppSettings';
import { useConfigList } from './useConfigList';
import { useConfigEditor } from './useConfigEditor';
import { useConfigOperations } from './useConfigOperations';

/**
 * 配置管理主Hook - 组合模式
 * 聚合所有配置相关的功能，提供统一的接口
 * 
 * 架构设计：
 * - 采用组合模式，将复杂逻辑拆分为职责单一的子Hook
 * - 每个子Hook专注于特定的业务领域
 * - 通过共享状态管理保持数据一致性
 * - 使用依赖注入避免循环依赖
 */
export function useConfig() {
  // 核心状态管理
  const configState = useConfigState();
  const { state, dispatch, showNotification } = configState;

  // 应用设置管理
  const appSettings = useAppSettings({ dispatch, showNotification });
  
  // 配置列表管理  
  const configList = useConfigList({
    state,
    dispatch,
    showNotification,
    selectConfig: configState.selectConfiguration,
    clearCurrentConfig: configState.clearCurrentConfig,
  });
  
  // 配置编辑器
  const configEditor = useConfigEditor({
    state,
    dispatch,
    showNotification,
  });
  
  // CRUD操作
  const configOperations = useConfigOperations({
    state,
    dispatch,
    showNotification,
    loadConfigurations: configList.loadConfigurations,
  });

  // 初始化加载
  useEffect(() => {
    appSettings.loadAppConfig();
    configList.loadConfigurations();
  }, [appSettings.loadAppConfig, configList.loadConfigurations]);

  // 返回完整的配置管理接口
  return {
    // 完整状态
    state,
    
    // 应用设置管理
    loadAppConfig: appSettings.loadAppConfig,
    saveAppConfig: appSettings.saveAppConfig,
    selectTargetDirectory: appSettings.selectTargetDirectory,
    
    // 配置列表管理
    loadConfigurations: configList.loadConfigurations,
    selectConfiguration: configList.selectConfiguration,
    clearCurrentConfig: configList.clearCurrentConfig,
    getSelectedConfigInfo: configList.getSelectedConfigInfo,
    isConfigExists: configList.isConfigExists,
    getConfigCount: configList.getConfigCount,
    
    // 配置编辑器
    readConfiguration: configEditor.readConfiguration,
    updateCurrentConfigContent: configEditor.updateCurrentConfigContent,
    validateJson: configEditor.validateJson,
    formatJson: configEditor.formatJson,
    isConfigurationModified: configEditor.isConfigurationModified,
    getValidationStatus: configEditor.getValidationStatus,
    
    // CRUD操作
    createConfiguration: configOperations.createConfiguration,
    updateConfiguration: configOperations.updateConfiguration,
    renameConfiguration: configOperations.renameConfiguration,
    deleteConfiguration: configOperations.deleteConfiguration,
    activateConfiguration: configOperations.activateConfiguration,
    batchDeleteConfigurations: configOperations.batchDeleteConfigurations,
    duplicateConfiguration: configOperations.duplicateConfiguration,
    
    // UI状态管理
    setCreatingNew: configState.setCreatingNew,
    setRenaming: configState.setRenaming,
    showDeleteConfirm: configState.showDeleteConfirm,
    hideDeleteConfirm: configState.hideDeleteConfirm,
  };
}