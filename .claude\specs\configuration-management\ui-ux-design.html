<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>claude-switch配置管理器原型</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', 'Microsoft YaHei', sans-serif;
            background-color: #f3f2f1;
        }
        .fluent-card {
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.05), 0 2px 8px rgba(0,0,0,0.08);
            transition: all 0.2s ease-in-out;
        }
        .fluent-button {
            border-radius: 4px;
            padding: 8px 16px;
            font-weight: 600;
            transition: background-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
            border: 1px solid transparent;
        }
        .fluent-button-primary {
            background-color: #0078d4;
            color: white;
        }
        .fluent-button-primary:hover {
            background-color: #106ebe;
            box-shadow: 0 0 0 2px rgba(0, 120, 212, 0.3);
        }
        .fluent-button-secondary {
            background-color: #ffffff;
            color: #323130;
            border: 1px solid #8a8886;
        }
        .fluent-button-secondary:hover {
            background-color: #f3f2f1;
        }
        .fluent-button-danger {
            background-color: #d83b01;
            color: white;
        }
        .fluent-button-danger:hover {
            background-color: #a82f00;
        }
        .config-item:hover .actions {
            opacity: 1;
        }
        .modal-backdrop {
            background-color: rgba(0,0,0,0.4);
        }
        .config-item[data-state="active"] {
            border-left: 4px solid #0078d4;
        }
        .config-item[data-state="invalid"] {
             border-left: 4px solid #d83b01;
        }
    </style>
</head>
<body class="text-gray-800">

    <div id="app" class="flex flex-col h-screen">
        <!-- Header -->
        <header class="bg-white shadow-sm p-4 flex justify-between items-center z-10 border-b border-gray-200">
            <h1 class="text-xl font-bold text-gray-800">ClaudeSwitch配置切换工具</h1>
            <div id="status-display" class="text-sm">
                <!-- Status will be rendered here -->
            </div>
        </header>

        <!-- Main Content -->
        <main class="flex-grow flex overflow-hidden">
            <!-- Left Panel: Configuration List -->
            <div class="w-1/3 xl:w-1/4 bg-gray-50 border-r border-gray-200 flex flex-col">
                <div class="p-4 border-b border-gray-200 flex justify-between items-center">
                    <h2 class="text-lg font-semibold">配置库</h2>
                    <button id="create-new-btn" class="fluent-button fluent-button-primary text-sm py-1 px-3">
                        + 新建
                    </button>
                </div>
                <div id="config-list" class="flex-grow overflow-y-auto p-2 space-y-2">
                    <!-- Config items will be rendered here -->
                </div>
            </div>

            <!-- Right Panel: Editor and Settings -->
            <div class="w-2/3 xl:w-3/4 flex flex-col p-6">
                <div id="welcome-view" class="flex-grow flex flex-col items-center justify-center text-center text-gray-500">
                     <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-cog mb-4"><path d="M4 22h14a2 2 0 0 0 2-2V7.5L14.5 2H6a2 2 0 0 0-2 2v3"/><path d="M14 2v6h6"/><path d="M12 12a2 2 0 1 0 4 0 2 2 0 1 0-4 0Z"/><path d="M12 18a2 2 0 1 0 4 0 2 2 0 1 0-4 0Z"/><path d="M6 15a2 2 0 1 0 4 0 2 2 0 1 0-4 0Z"/></svg>
                    <h3 class="text-xl font-semibold">欢迎使用配置管理器</h3>
                    <p class="mt-2">从左侧选择一个配置进行编辑，或创建一个新配置。</p>
                </div>
                <div id="editor-view" class="hidden flex-grow flex flex-col fluent-card p-4">
                    <div class="flex justify-between items-center mb-2">
                        <h3 class="text-lg font-semibold">编辑: <span id="editing-filename" class="font-mono"></span></h3>
                        <div id="json-status" class="text-sm font-semibold"></div>
                    </div>
                    <textarea id="json-editor" class="w-full flex-grow p-3 font-mono text-sm bg-gray-50 rounded-md border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:outline-none resize-none"></textarea>
                    <div class="mt-4 flex justify-end space-x-3">
                        <button id="save-btn" class="fluent-button fluent-button-secondary">保存</button>
                        <button id="activate-btn" class="fluent-button fluent-button-primary">激活此配置</button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Modals -->
    <div id="modal-container" class="hidden fixed inset-0 z-50 flex items-center justify-center modal-backdrop">
        <!-- Modal content will be injected here -->
    </div>
    
    <!-- Toast Notification -->
    <div id="toast-container" class="hidden fixed top-5 right-5 z-50 bg-red-500 text-white py-2 px-4 rounded-lg shadow-lg">
        <!-- Toast message -->
    </div>


    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // --- MOCK DATA ---
            // Simulates the file system
            let mockFileSystem = {
                targetDirectory: null, // 'C:/Users/<USER>/MyApp/settings'
                configurations: {
                    'development.json': { content: JSON.stringify({ api_url: 'http://dev.api.com', debug_mode: true }, null, 2), valid: true },
                    'production.json': { content: JSON.stringify({ api_url: 'https://api.com', debug_mode: false }, null, 2), valid: true },
                    'staging.json': { content: JSON.stringify({ api_url: 'http://staging.api.com', debug_mode: true }, null, 2), valid: true },
                    'user-test.json': { content: '{ "api_url": "http://test.api.com", "debug_mode": false, }', valid: false }, // Invalid JSON
                },
                activeConfiguration: null, // 'production.json'
                targetSettingsExists: false, // Simulates if settings.json exists in target dir
            };

            // --- DOM ELEMENTS ---
            const app = {
                statusDisplay: document.getElementById('status-display'),
                configList: document.getElementById('config-list'),
                welcomeView: document.getElementById('welcome-view'),
                editorView: document.getElementById('editor-view'),
                editingFilename: document.getElementById('editing-filename'),
                jsonEditor: document.getElementById('json-editor'),
                jsonStatus: document.getElementById('json-status'),
                saveBtn: document.getElementById('save-btn'),
                activateBtn: document.getElementById('activate-btn'),
                createNewBtn: document.getElementById('create-new-btn'),
                modalContainer: document.getElementById('modal-container'),
                toastContainer: document.getElementById('toast-container'),
            };

            let currentEditingFile = null;
            let hasUnsavedChanges = false;

            // --- CORE FUNCTIONS ---

            function renderStatus() {
                const { targetDirectory, activeConfiguration } = mockFileSystem;
                let statusHtml = '';
                if (!targetDirectory) {
                    statusHtml = `
                        <div class="flex items-center space-x-2">
                            <span class="text-red-600 font-semibold">目标目录未设置</span>
                            <button id="select-dir-btn" class="fluent-button fluent-button-primary text-sm py-1 px-3">选择目录</button>
                        </div>
                    `;
                } else {
                    statusHtml = `
                        <div class="flex items-center space-x-3">
                            <div>
                                <span class="font-semibold">目标目录:</span>
                                <span class="font-mono bg-gray-100 px-2 py-1 rounded">${targetDirectory}</span>
                            </div>
                            <div>
                                <span class="font-semibold">当前激活:</span>
                                <span class="font-mono bg-blue-100 text-blue-800 px-2 py-1 rounded">${activeConfiguration || '无'}</span>
                            </div>
                            <button id="change-dir-btn" class="fluent-button fluent-button-secondary text-sm py-1 px-3">修改</button>
                        </div>
                    `;
                }
                app.statusDisplay.innerHTML = statusHtml;
            }

            function renderConfigList() {
                app.configList.innerHTML = '';
                const filenames = Object.keys(mockFileSystem.configurations).sort();
                filenames.forEach(filename => {
                    const config = mockFileSystem.configurations[filename];
                    const item = document.createElement('div');
                    item.className = 'config-item fluent-card flex justify-between items-center p-3 cursor-pointer border-l-4 border-transparent';
                    item.dataset.filename = filename;
                    
                    let state = '';
                    if (filename === mockFileSystem.activeConfiguration) {
                        state = 'active';
                    } else if (!config.valid) {
                        state = 'invalid';
                    }
                    item.dataset.state = state;

                    item.innerHTML = `
                        <div class="flex items-center">
                             <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-3 text-gray-500"><path d="M21.44 11.05l-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66l-9.2 9.19a2 2 0 0 1-2.83-2.83l8.49-8.48"/></svg>
                            <div>
                                <span class="font-mono font-medium">${filename}</span>
                                ${!config.valid ? '<span class="ml-2 text-xs font-bold text-red-600 bg-red-100 px-2 py-0.5 rounded-full">无效JSON</span>' : ''}
                            </div>
                        </div>
                        <div class="actions opacity-0 transition-opacity flex space-x-2">
                            <button data-action="rename" title="重命名" class="p-1 hover:bg-gray-200 rounded"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 20h9"/><path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"/></svg></button>
                            <button data-action="delete" title="删除" class="p-1 hover:bg-gray-200 rounded text-red-600"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="3 6 5 6 21 6"/><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"/><line x1="10" y1="11" x2="10" y2="17"/><line x1="14" y1="11" x2="14" y2="17"/></svg></button>
                        </div>
                    `;
                    app.configList.appendChild(item);
                });
            }

            function openEditor(filename) {
                if (hasUnsavedChanges && !confirm('您有未保存的更改，确定要放弃吗？')) {
                    return;
                }
                currentEditingFile = filename;
                const config = mockFileSystem.configurations[filename];
                app.welcomeView.classList.add('hidden');
                app.editorView.classList.remove('hidden');
                app.editorView.classList.add('flex');
                
                app.editingFilename.textContent = filename;
                app.jsonEditor.value = config.content;
                validateJson(config.content);
                updateSaveButtonState(false);
            }

            function closeEditor() {
                currentEditingFile = null;
                app.welcomeView.classList.remove('hidden');
                app.editorView.classList.add('hidden');
                app.editorView.classList.remove('flex');
            }

            function validateJson(text) {
                try {
                    JSON.parse(text);
                    app.jsonStatus.textContent = 'JSON 有效';
                    app.jsonStatus.className = 'text-sm font-semibold text-green-600';
                    return true;
                } catch (e) {
                    app.jsonStatus.textContent = 'JSON 格式无效';
                    app.jsonStatus.className = 'text-sm font-semibold text-red-600';
                    return false;
                }
            }

            function updateSaveButtonState(unsaved) {
                hasUnsavedChanges = unsaved;
                app.saveBtn.disabled = !unsaved;
                app.saveBtn.classList.toggle('opacity-50', !unsaved);
                app.saveBtn.classList.toggle('cursor-not-allowed', !unsaved);
            }

            function saveChanges() {
                if (!currentEditingFile || !hasUnsavedChanges) return;
                const content = app.jsonEditor.value;
                const isValid = validateJson(content);
                mockFileSystem.configurations[currentEditingFile] = { content, valid: isValid };
                updateSaveButtonState(false);
                renderConfigList();
                showToast('保存成功!', 'success');
            }
            
            function showToast(message, type = 'error') {
                app.toastContainer.textContent = message;
                app.toastContainer.className = `fixed top-5 right-5 z-50 text-white py-2 px-4 rounded-lg shadow-lg ${type === 'success' ? 'bg-green-500' : 'bg-red-500'}`;
                app.toastContainer.classList.remove('hidden');
                setTimeout(() => {
                    app.toastContainer.classList.add('hidden');
                }, 3000);
            }

            function showModal(content) {
                app.modalContainer.innerHTML = content;
                app.modalContainer.classList.remove('hidden');
            }

            function closeModal() {
                app.modalContainer.classList.add('hidden');
                app.modalContainer.innerHTML = '';
            }

            function handleCreateNew() {
                const modalContent = `
                    <div class="fluent-card w-full max-w-md p-6">
                        <h3 class="text-xl font-semibold mb-4">创建新配置</h3>
                        <label for="new-filename" class="block text-sm font-medium text-gray-700">文件名 (以 .json 结尾)</label>
                        <input type="text" id="new-filename" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="e.g., my-config.json">
                        <div id="modal-error" class="text-red-600 text-sm mt-2"></div>
                        <div class="mt-6 flex justify-end space-x-3">
                            <button id="modal-cancel" class="fluent-button fluent-button-secondary">取消</button>
                            <button id="modal-confirm" class="fluent-button fluent-button-primary">创建</button>
                        </div>
                    </div>
                `;
                showModal(modalContent);

                const confirmBtn = document.getElementById('modal-confirm');
                const cancelBtn = document.getElementById('modal-cancel');
                const input = document.getElementById('new-filename');
                const errorDiv = document.getElementById('modal-error');

                cancelBtn.onclick = closeModal;
                confirmBtn.onclick = () => {
                    const newName = input.value.trim();
                    if (!newName) {
                        errorDiv.textContent = '文件名不能为空。';
                        return;
                    }
                    if (!newName.endsWith('.json')) {
                        errorDiv.textContent = '文件名必须以 .json 结尾。';
                        return;
                    }
                    if (mockFileSystem.configurations[newName]) {
                        errorDiv.textContent = '文件名已存在。';
                        return;
                    }

                    mockFileSystem.configurations[newName] = { content: '{\n\t\n}', valid: true };
                    renderConfigList();
                    openEditor(newName);
                    closeModal();
                };
            }
            
            function handleRename(filename) {
                const modalContent = `
                    <div class="fluent-card w-full max-w-md p-6">
                        <h3 class="text-xl font-semibold mb-4">重命名配置</h3>
                        <p class="mb-4">重命名 <span class="font-mono bg-gray-100 px-1 rounded">${filename}</span>:</p>
                        <input type="text" id="rename-filename" value="${filename}" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        <div id="modal-error" class="text-red-600 text-sm mt-2"></div>
                        <div class="mt-6 flex justify-end space-x-3">
                            <button id="modal-cancel" class="fluent-button fluent-button-secondary">取消</button>
                            <button id="modal-confirm" class="fluent-button fluent-button-primary">重命名</button>
                        </div>
                    </div>
                `;
                showModal(modalContent);
                
                const confirmBtn = document.getElementById('modal-confirm');
                const cancelBtn = document.getElementById('modal-cancel');
                const input = document.getElementById('rename-filename');
                const errorDiv = document.getElementById('modal-error');

                cancelBtn.onclick = closeModal;
                confirmBtn.onclick = () => {
                    const newName = input.value.trim();
                    if (!newName || !newName.endsWith('.json') || (newName !== filename && mockFileSystem.configurations[newName])) {
                        errorDiv.textContent = '无效或重复的文件名。';
                        return;
                    }
                    
                    if (newName !== filename) {
                        const configData = mockFileSystem.configurations[filename];
                        delete mockFileSystem.configurations[filename];
                        mockFileSystem.configurations[newName] = configData;
                        
                        if (mockFileSystem.activeConfiguration === filename) {
                            mockFileSystem.activeConfiguration = newName;
                        }
                        if (currentEditingFile === filename) {
                            currentEditingFile = newName;
                            app.editingFilename.textContent = newName;
                        }
                        renderConfigList();
                        renderStatus();
                    }
                    closeModal();
                };
            }

            function handleDelete(filename) {
                const modalContent = `
                    <div class="fluent-card w-full max-w-md p-6">
                        <h3 class="text-xl font-semibold mb-4 text-red-600">确认删除</h3>
                        <p>您确定要删除 <span class="font-mono bg-gray-100 px-1 rounded">${filename}</span> 吗？此操作无法撤销。</p>
                        <div class="mt-6 flex justify-end space-x-3">
                            <button id="modal-cancel" class="fluent-button fluent-button-secondary">取消</button>
                            <button id="modal-confirm" class="fluent-button fluent-button-danger">删除</button>
                        </div>
                    </div>
                `;
                showModal(modalContent);

                document.getElementById('modal-cancel').onclick = closeModal;
                document.getElementById('modal-confirm').onclick = () => {
                    delete mockFileSystem.configurations[filename];
                    if (mockFileSystem.activeConfiguration === filename) {
                        mockFileSystem.activeConfiguration = null;
                    }
                    if (currentEditingFile === filename) {
                        closeEditor();
                    }
                    renderConfigList();
                    renderStatus();
                    closeModal();
                };
            }
            
            function handleActivate(filename) {
                if (!mockFileSystem.targetDirectory) {
                    showToast('请先设置目标目录！');
                    return;
                }
                 if (!mockFileSystem.configurations[filename].valid) {
                    showToast('无法激活无效的JSON配置。');
                    return;
                }
                
                const activateLogic = () => {
                    // Simulate permission error
                    const hasPermission = Math.random() > 0.1; // 90% chance of success
                    if (hasPermission) {
                        mockFileSystem.activeConfiguration = filename;
                        mockFileSystem.targetSettingsExists = true;
                        renderConfigList();
                        renderStatus();
                        showToast(`配置 ${filename} 已激活!`, 'success');
                    } else {
                        showToast('激活失败！请尝试以管理员模式运行。');
                    }
                    closeModal();
                };

                if (mockFileSystem.targetSettingsExists && mockFileSystem.activeConfiguration !== filename) {
                    const modalContent = `
                        <div class="fluent-card w-full max-w-lg p-6">
                            <h3 class="text-xl font-semibold mb-4">目标文件已存在</h3>
                            <p>目标目录中已存在 <span class="font-mono bg-gray-100 px-1 rounded">settings.json</span> 文件。您想如何操作？</p>
                            <div class="mt-6 flex justify-end space-x-3">
                                <button id="modal-cancel" class="fluent-button fluent-button-secondary">取消</button>
                                <button id="modal-backup" class="fluent-button fluent-button-secondary">备份并覆盖</button>
                                <button id="modal-overwrite" class="fluent-button fluent-button-primary">直接覆盖</button>
                            </div>
                        </div>
                    `;
                    showModal(modalContent);
                    document.getElementById('modal-cancel').onclick = closeModal;
                    document.getElementById('modal-overwrite').onclick = activateLogic;
                    document.getElementById('modal-backup').onclick = () => {
                        // Simulate backup
                        const backupName = `settings.backup-${Date.now()}.json`;
                        mockFileSystem.configurations[backupName] = { content: '// Backed up content', valid: true };
                        renderConfigList();
                        activateLogic();
                    };
                } else {
                    activateLogic();
                }
            }


            // --- EVENT LISTENERS ---
            app.configList.addEventListener('click', (e) => {
                const item = e.target.closest('.config-item');
                if (!item) return;

                const filename = item.dataset.filename;
                const action = e.target.closest('button')?.dataset.action;

                if (action) {
                    e.stopPropagation(); // Prevent opening editor when clicking a button
                    if (action === 'rename') handleRename(filename);
                    if (action === 'delete') handleDelete(filename);
                } else {
                    openEditor(filename);
                }
            });

            app.statusDisplay.addEventListener('click', (e) => {
                const buttonId = e.target.id;
                if (buttonId === 'select-dir-btn' || buttonId === 'change-dir-btn') {
                    const newPath = prompt('模拟文件选择器：\n请输入目标目录的路径:', mockFileSystem.targetDirectory || 'C:/Users/<USER>/MyApp/settings');
                    if (newPath) {
                        mockFileSystem.targetDirectory = newPath;
                        // Simulate checking if settings.json exists in the new path
                        mockFileSystem.targetSettingsExists = Math.random() > 0.5;
                        renderStatus();
                    }
                }
            });

            app.jsonEditor.addEventListener('keyup', () => {
                validateJson(app.jsonEditor.value);
                updateSaveButtonState(true);
            });

            app.saveBtn.addEventListener('click', saveChanges);
            app.activateBtn.addEventListener('click', () => {
                if (currentEditingFile) {
                    if (hasUnsavedChanges) {
                        if (confirm('您有未保存的更改。是否先保存再激活？')) {
                            saveChanges();
                            handleActivate(currentEditingFile);
                        }
                    } else {
                       handleActivate(currentEditingFile);
                    }
                }
            });
            
            app.createNewBtn.addEventListener('click', handleCreateNew);

            // --- INITIALIZATION ---
            function init() {
                renderStatus();
                renderConfigList();
                // On first load, check if target directory is set
                if (!mockFileSystem.targetDirectory) {
                     showToast('欢迎！请首先选择您的目标目录。', 'success');
                }
            }

            init();
        });
    </script>
</body>
</html>
