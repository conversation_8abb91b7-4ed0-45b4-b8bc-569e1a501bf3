import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>u<PERSON><PERSON><PERSON>,
  <PERSON>u<PERSON>rigger,
  Text,
  Tooltip,
} from '@fluentui/react-components';
import {
  WeatherSunny24Regular,
  WeatherMoon24Regular,
  Desktop24Regular,
  Checkmark24Regular,
} from '@fluentui/react-icons';
import { useThemeContext } from '../contexts/ThemeContext';
import { ThemeMode } from '../theme';

interface ThemeToggleProps {
  showLabel?: boolean;
  size?: 'small' | 'medium' | 'large';
}

export const ThemeToggle: React.FC<ThemeToggleProps> = ({ 
  showLabel = false, 
  size = 'medium' 
}) => {
  const { themeMode, updateThemeMode, getThemeInfo, isUpdating } = useThemeContext();
  const themeInfo = getThemeInfo();

  const getThemeIcon = (mode: ThemeMode) => {
    switch (mode) {
      case 'light':
        return <WeatherSunny24Regular />;
      case 'dark':
        return <WeatherMoon24Regular />;
      case 'system':
        return <Desktop24Regular />;
      default:
        return <WeatherSunny24Regular />;
    }
  };

  const getThemeName = (mode: ThemeMode) => {
    switch (mode) {
      case 'light':
        return '浅色模式';
      case 'dark':
        return '深色模式';
      case 'system':
        return '跟随系统';
      default:
        return '浅色模式';
    }
  };

  const themeOptions: ThemeMode[] = ['light', 'dark', 'system'];

  const buttonContent = showLabel ? (
    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
      {getThemeIcon(themeMode as ThemeMode)}
      <Text size={200}>{themeInfo.displayName}</Text>
    </div>
  ) : (
    getThemeIcon(themeMode as ThemeMode)
  );

  return (
    <Menu>
      <MenuTrigger disableButtonEnhancement>
        <Tooltip 
          content={`当前主题: ${themeInfo.displayName}`} 
          relationship="label"
        >
          <Button
            appearance="subtle"
            size={size}
            icon={!showLabel ? getThemeIcon(themeMode as ThemeMode) : undefined}
            disabled={isUpdating}
            style={{
              opacity: isUpdating ? 0.7 : 1,
              transition: 'opacity 0.2s ease-in-out'
            }}
          >
            {showLabel && buttonContent}
          </Button>
        </Tooltip>
      </MenuTrigger>

      <MenuPopover>
        <MenuList>
          {themeOptions.map((mode) => (
            <MenuItem
              key={mode}
              icon={getThemeIcon(mode)}
              secondaryContent={mode === themeMode ? <Checkmark24Regular /> : undefined}
              onClick={() => updateThemeMode(mode)}
            >
              <div>
                <Text weight={mode === themeMode ? 'semibold' : 'regular'}>
                  {getThemeName(mode)}
                </Text>
                {mode === 'system' && (
                  <Text 
                    size={100} 
                    style={{ 
                      color: 'var(--colorNeutralForeground3)',
                      display: 'block',
                      marginTop: '2px'
                    }}
                  >
                    自动匹配系统设置
                  </Text>
                )}
              </div>
            </MenuItem>
          ))}
        </MenuList>
      </MenuPopover>
    </Menu>
  );
};