import { ThemeProvider } from './contexts/ThemeContext';
import { ConfigManagerContainer } from './components/ConfigManager/ConfigManagerContainer';
import "./App.css";

/**
 * 主应用组件
 * 
 * 优化要点：
 * - 移除了强制key更新机制
 * - 使用ThemeProvider统一管理主题
 * - 减少不必要的重渲染
 * - 使用重构后的ConfigManagerContainer组件，提高模块化程度
 */
function App() {
  return (
    <ThemeProvider>
      <div className="app">
        <ConfigManagerContainer />
      </div>
    </ThemeProvider>
  );
}

export default App;
