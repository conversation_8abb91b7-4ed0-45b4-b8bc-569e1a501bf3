mod config_manager;
mod file_operations;
mod validation;
mod error_handler;
mod logger;

use config_manager::{ConfigManager, AppConfig, ConfigInfo};
use validation::{JsonValidator, ValidationResult};
use error_handler::ErrorHandler;
use logger::Logger;

// 原有的问候命令，保持兼容性
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

// 应用配置管理命令
#[tauri::command]
async fn load_app_config() -> Result<AppConfig, String> {
    let manager = ConfigManager::new()?;
    manager.load_app_config()
}

#[tauri::command]
async fn save_app_config(config: AppConfig) -> Result<(), String> {
    let manager = ConfigManager::new()?;
    manager.save_app_config(&config)
}

#[tauri::command]
async fn select_target_directory(_window: tauri::Window) -> Result<String, String> {
    Logger::log_user_action("选择目标目录", None);
    
    // 注意：在实际应用中，目录选择应该通过前端JavaScript调用
    // tauri.dialog.open() API来实现，这里提供一个占位实现
    Logger::log_user_action("目录选择功能需要前端配合实现", None);
    Err(ErrorHandler::new_error("E100", "目录选择功能需要在前端实现 - 请使用tauri.dialog.open() API").to_string())
}

// 配置文件库管理命令
#[tauri::command]
async fn list_configurations() -> Result<Vec<ConfigInfo>, String> {
    let manager = ConfigManager::new()?;
    manager.list_configurations()
}

#[tauri::command]
async fn create_configuration(name: String, content: String) -> Result<(), String> {
    let manager = ConfigManager::new()?;
    manager.create_configuration(&name, &content)
}

#[tauri::command]
async fn read_configuration(name: String) -> Result<String, String> {
    let manager = ConfigManager::new()?;
    manager.read_configuration(&name)
}

#[tauri::command]
async fn update_configuration(name: String, content: String) -> Result<(), String> {
    let manager = ConfigManager::new()?;
    manager.update_configuration(&name, &content)
}

#[tauri::command]
async fn rename_configuration(old_name: String, new_name: String) -> Result<(), String> {
    let manager = ConfigManager::new()?;
    manager.rename_configuration(&old_name, &new_name)
}

#[tauri::command]
async fn delete_configuration(name: String) -> Result<(), String> {
    let manager = ConfigManager::new()?;
    manager.delete_configuration(&name)
}

#[tauri::command]
async fn validate_json(content: String) -> Result<ValidationResult, String> {
    Ok(JsonValidator::validate_json_format(&content))
}

// 配置激活系统命令
#[tauri::command]
async fn activate_configuration(name: String, target_directory: String) -> Result<(), String> {
    Logger::log_config_operation("激活配置", &name, Some(&target_directory));
    
    let manager = ConfigManager::new()?;
    
    // 如果启用了自动备份，先备份现有配置
    let app_config = manager.load_app_config()?;
    if app_config.app_settings.auto_backup {
        Logger::log_operation("自动备份", &format!("备份目标: {}", target_directory));
        // 尝试备份，如果失败也不阻止激活过程
        let _ = manager.backup_existing_config(&target_directory);
    }
    
    // 激活配置
    match manager.activate_configuration(&name, &target_directory) {
        Ok(_) => {
            Logger::log_config_operation("配置激活成功", &name, None);
        },
        Err(e) => {
            Logger::log_error_detail("配置激活失败", &e);
            return Err(e);
        }
    }
    
    // 更新最后激活的配置
    let mut updated_config = app_config;
    updated_config.last_active_config = Some(name.clone());
    updated_config.target_directory = target_directory;
    manager.save_app_config(&updated_config)?;
    
    Logger::log_config_operation("配置状态更新完成", &name, None);
    Ok(())
}

#[tauri::command]
async fn get_current_active_config() -> Result<Option<String>, String> {
    let manager = ConfigManager::new()?;
    manager.get_current_active_config()
}

#[tauri::command]
async fn backup_existing_config(target_file: String) -> Result<String, String> {
    let manager = ConfigManager::new()?;
    manager.backup_existing_config(&target_file)
}

// 文件系统操作命令
#[tauri::command]
async fn check_path_exists(path: String) -> Result<bool, String> {
    Ok(file_operations::FileOperations::path_exists(&path))
}

#[tauri::command]
async fn check_is_directory(path: String) -> Result<bool, String> {
    Ok(file_operations::FileOperations::is_directory(&path))
}

#[tauri::command]
async fn check_is_writable(path: String) -> Result<bool, String> {
    Ok(file_operations::FileOperations::is_writable(&path))
}

// JSON格式化命令
#[tauri::command]
async fn format_json(content: String) -> Result<String, String> {
    JsonValidator::format_json(&content)
}

#[tauri::command]
async fn minify_json(content: String) -> Result<String, String> {
    JsonValidator::minify_json(&content)
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    // 初始化日志系统
    Logger::init();
    Logger::info("ClaudeSwitch 应用启动");

    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_dialog::init())
        .invoke_handler(tauri::generate_handler![
            greet,
            // 应用配置管理
            load_app_config,
            save_app_config,
            select_target_directory,
            // 配置文件库管理
            list_configurations,
            create_configuration,
            read_configuration,
            update_configuration,
            rename_configuration,
            delete_configuration,
            validate_json,
            // 配置激活系统
            activate_configuration,
            get_current_active_config,
            backup_existing_config,
            // 文件系统操作
            check_path_exists,
            check_is_directory,
            check_is_writable,
            // JSON工具
            format_json,
            minify_json
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
