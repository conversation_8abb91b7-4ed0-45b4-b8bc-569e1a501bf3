# 配置管理功能 - 技术实施规格

## 问题描述

**业务问题**: 开发者需要一个桌面应用来管理和快速切换不同的 JSON 配置文件，提高开发效率。

**当前状态**: 现有 Tauri2 + React 项目仅包含基础模板代码，需要完整的配置管理功能。

**预期结果**: 
- 可视化界面管理配置文件库
- 一键激活配置到目标目录
- 完整的文件操作（创建、编辑、重命名、删除）
- JSON 格式验证和错误处理
- 目标目录自动检测和设置

## 解决方案概述

**方法**: 基于现有 Tauri2 + React 技术栈，构建完整的配置管理桌面应用，通过 Rust 后端处理文件系统操作，React 前端提供用户界面。

**核心变更**:
1. 扩展 Rust 后端添加文件操作 API
2. 重构 React 前端实现配置管理界面
3. 添加应用数据持久化机制
4. 实现配置文件库管理系统

**成功标准**:
- 所有配置操作通过 GUI 完成
- JSON 验证准确性 100%
- 配置激活成功率 ≥ 95%
- 用户友好的错误提示

## 技术实施

### 数据库/存储变更

**新增应用配置文件**:
```json
// data/config.json
{
  "targetDirectory": "C:\\path\\to\\target",
  "lastActiveConfig": "config1.json",
  "appSettings": {
    "autoBackup": true,
    "confirmDelete": true
  }
}
```

**配置文件目录结构**:
```
configurations/
├── config1.json
├── config2.json
└── backup/
    └── settings_backup_20250803.json
```

### 代码变更

**Rust 后端新增文件** (`src-tauri/src/`):
- **config_manager.rs**: 配置文件操作核心逻辑
- **file_operations.rs**: 文件系统操作封装
- **validation.rs**: JSON 验证工具

**Rust 后端修改文件**:
- **lib.rs**: 添加新的 Tauri 命令处理器
- **Cargo.toml**: 添加必要依赖

**React 前端新增文件** (`src/`):
- **components/ConfigManager.tsx**: 主配置管理组件
- **components/ConfigList.tsx**: 配置列表组件
- **components/ConfigEditor.tsx**: 配置编辑器组件
- **components/DirectorySelector.tsx**: 目录选择组件
- **hooks/useConfig.ts**: 配置状态管理 Hook
- **types/config.ts**: TypeScript 类型定义
- **utils/validation.ts**: 前端验证工具

**React 前端修改文件**:
- **App.tsx**: 替换为配置管理主界面
- **App.css**: 更新样式

### API 设计

**Tauri 命令接口**:

```rust
// 应用配置管理
#[tauri::command]
async fn load_app_config() -> Result<AppConfig, String>

#[tauri::command]
async fn save_app_config(config: AppConfig) -> Result<(), String>

#[tauri::command]  
async fn select_target_directory() -> Result<String, String>

// 配置文件库管理
#[tauri::command]
async fn list_configurations() -> Result<Vec<ConfigInfo>, String>

#[tauri::command]
async fn create_configuration(name: String, content: String) -> Result<(), String>

#[tauri::command]
async fn read_configuration(name: String) -> Result<String, String>

#[tauri::command]
async fn update_configuration(name: String, content: String) -> Result<(), String>

#[tauri::command]
async fn rename_configuration(old_name: String, new_name: String) -> Result<(), String>

#[tauri::command]
async fn delete_configuration(name: String) -> Result<(), String>

#[tauri::command]
async fn validate_json(content: String) -> Result<bool, String>

// 配置激活系统
#[tauri::command]
async fn activate_configuration(name: String) -> Result<(), String>

#[tauri::command]
async fn get_current_active_config() -> Result<String, String>

#[tauri::command]
async fn backup_existing_config() -> Result<String, String>
```

**数据结构定义**:

```rust
#[derive(Serialize, Deserialize)]
pub struct AppConfig {
    pub target_directory: String,
    pub last_active_config: Option<String>,
    pub app_settings: AppSettings,
}

#[derive(Serialize, Deserialize)]
pub struct AppSettings {
    pub auto_backup: bool,
    pub confirm_delete: bool,
}

#[derive(Serialize, Deserialize)]
pub struct ConfigInfo {
    pub name: String,
    pub is_valid: bool,
    pub last_modified: String,
    pub size: u64,
}
```

### 配置变更

**Cargo.toml 新增依赖**:
```toml
tokio = { version = "1", features = ["full"] }
dirs = "5"
uuid = { version = "1", features = ["v4"] }
chrono = { version = "0.4", features = ["serde"] }
```

**Tauri 权限配置** (src-tauri/capabilities/default.json):
```json
{
  "permissions": [
    "fs:allow-read-dir",
    "fs:allow-read-file", 
    "fs:allow-write-file",
    "fs:allow-create-dir",
    "fs:allow-remove-file",
    "dialog:allow-open"
  ]
}
```

**package.json 新增依赖**:
```json
{
  "dependencies": {
    "react-json-editor": "^1.3.0",
    "react-modal": "^3.16.1"
  }
}
```

## 实施序列

### 第 1 阶段：后端基础设施
1. **修改 Cargo.toml** - 添加所需依赖
2. **创建 src-tauri/src/config_manager.rs** - 核心配置管理逻辑
3. **创建 src-tauri/src/file_operations.rs** - 文件系统操作
4. **创建 src-tauri/src/validation.rs** - JSON 验证
5. **修改 src-tauri/src/lib.rs** - 注册新的 Tauri 命令

### 第 2 阶段：前端基础组件  
1. **创建 src/types/config.ts** - TypeScript 类型定义
2. **创建 src/hooks/useConfig.ts** - 配置状态管理
3. **创建 src/utils/validation.ts** - 前端验证工具
4. **创建 src/components/DirectorySelector.tsx** - 目录选择组件

### 第 3 阶段：核心功能组件
1. **创建 src/components/ConfigList.tsx** - 配置列表显示
2. **创建 src/components/ConfigEditor.tsx** - 配置编辑器
3. **创建 src/components/ConfigManager.tsx** - 主管理组件
4. **修改 src/App.tsx** - 集成所有组件

### 第 4 阶段：界面优化和错误处理
1. **修改 src/App.css** - 样式美化
2. **添加错误边界和用户提示**
3. **实现确认对话框**
4. **优化用户体验**

每个阶段都可以独立部署和测试。

## 验证计划

### 单元测试
**Rust 后端测试** (src-tauri/src/tests/):
- `test_config_manager.rs`: 配置管理逻辑测试
- `test_file_operations.rs`: 文件操作测试  
- `test_validation.rs`: JSON 验证测试

**测试场景**:
- JSON 格式验证准确性
- 文件创建、读取、写入、删除操作
- 配置激活流程
- 错误情况处理

### 集成测试
**端到端工作流测试**:
1. 应用启动和目标目录检测
2. 配置文件创建和编辑流程
3. 配置激活和备份流程
4. 重命名和删除操作
5. 错误恢复机制

### 业务逻辑验证
**用户场景测试**:
1. **新用户首次使用**: 目录选择引导正常工作
2. **配置管理**: 所有 CRUD 操作正确执行
3. **JSON 验证**: 格式错误正确标识和处理
4. **配置激活**: 目标文件正确替换，备份机制工作
5. **错误处理**: 权限不足时提示管理员模式运行

**性能验证**:
- 配置列表加载时间 < 500ms
- 配置激活操作时间 < 200ms  
- JSON 验证响应时间 < 100ms

**兼容性验证**:
- Windows 10/11 系统兼容性
- 不同驱动器和路径格式支持
- 大配置文件处理能力 (最大 10MB)

## 文件结构预览

```
ClaudeSwitch/
├── src/
│   ├── components/
│   │   ├── ConfigManager.tsx
│   │   ├── ConfigList.tsx  
│   │   ├── ConfigEditor.tsx
│   │   └── DirectorySelector.tsx
│   ├── hooks/
│   │   └── useConfig.ts
│   ├── types/
│   │   └── config.ts
│   ├── utils/
│   │   └── validation.ts
│   ├── App.tsx (修改)
│   └── App.css (修改)
├── src-tauri/
│   ├── src/
│   │   ├── config_manager.rs
│   │   ├── file_operations.rs
│   │   ├── validation.rs
│   │   ├── lib.rs (修改)
│   │   └── tests/
│   │       ├── test_config_manager.rs
│   │       ├── test_file_operations.rs
│   │       └── test_validation.rs
│   ├── Cargo.toml (修改)
│   └── capabilities/default.json (修改)
├── data/
│   └── config.json
├── configurations/
│   └── backup/
└── package.json (修改)
```

## 关键实施细节

### 错误处理策略
- **文件操作失败**: 显示具体错误信息和建议解决方案
- **JSON 格式错误**: 高亮错误位置，允许保存但标记无效
- **权限不足**: 提示以管理员身份运行
- **目标目录不存在**: 自动创建或提示用户选择

### 用户体验优化
- **加载状态指示器**: 所有异步操作显示进度
- **确认对话框**: 删除等危险操作需要确认
- **自动保存**: 配置编辑时自动保存草稿
- **快捷键支持**: Ctrl+S 保存，Ctrl+N 新建等

### 性能优化
- **配置列表懒加载**: 大量配置时分页加载
- **文件监控**: 监控配置目录变化自动刷新
- **缓存机制**: 缓存验证结果减少重复计算

此技术规格涵盖了从底层文件操作到用户界面的完整实施方案，每个组件都有明确的职责和接口定义，确保代码生成工具可以直接根据此规格实现完整的配置管理功能。