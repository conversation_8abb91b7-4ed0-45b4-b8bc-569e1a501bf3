/**
 * 配置管理Hook - 向后兼容重导出
 * 
 * 重构说明：
 * 原来的useConfig.ts(525行)已被重构为多个专门的Hook：
 * - useConfigState: 共享状态管理 (80行)
 * - useAppSettings: 应用设置管理 (80行) 
 * - useConfigList: 配置列表管理 (120行)
 * - useConfigEditor: 编辑器状态管理 (100行)
 * - useConfigOperations: CRUD操作 (150行)
 * - useConfig: 组合Hook (主入口，80行)
 * 
 * 这个文件现在作为向后兼容的重导出，保持现有组件的导入路径不变。
 */

export { useConfig } from './config/useConfig';

// 如果需要访问专门的Hook，可以使用以下导入：
// import { useAppSettings, useConfigList, useConfigEditor, useConfigOperations } from './config';