// 应用配置类型定义
export interface AppConfig {
  target_directory: string;
  last_active_config?: string;
  app_settings: AppSettings;
}

export interface AppSettings {
  auto_backup: boolean;
  confirm_delete: boolean;
}

// 配置文件信息类型
export interface ConfigInfo {
  name: string;
  is_valid: boolean;
  last_modified: string;
  size: number;
}

// JSON验证结果类型
export interface ValidationError {
  line: number;
  column: number;
  message: string;
  error_type: ValidationErrorType;
}

export type ValidationErrorType =
  | 'SyntaxError'
  | 'InvalidFormat'
  | 'MissingField'
  | 'InvalidValue'
  | 'TypeMismatch';

export interface ValidationResult {
  is_valid: boolean;
  errors: ValidationError[];
  warnings: string[];
  formatted_json?: string;
}

// 文件信息类型
export interface FileInfo {
  name: string;
  path: string;
  size: number;
  is_file: boolean;
  is_dir: boolean;
  modified: string;
}

// JSON统计信息类型
export interface JsonStats {
  total_size: number;
  max_depth: number;
  object_count: number;
  array_count: number;
  string_count: number;
  number_count: number;
  boolean_count: number;
  null_count: number;
}

// 组件状态类型
export interface ConfigManagerState {
  // 应用配置状态
  appConfig: AppConfig | null;
  isLoadingAppConfig: boolean;
  appConfigError: string | null;

  // 配置列表状态
  configurations: ConfigInfo[];
  isLoadingConfigurations: boolean;
  configurationsError: string | null;

  // 当前编辑的配置
  currentConfig: {
    name: string;
    content: string;
    isModified: boolean;
    validationResult: ValidationResult | null;
    sessionId?: string;
    loadedAt?: number;
  } | null;

  // UI状态
  selectedConfigName: string | null;
  isCreatingNew: boolean;
  isRenaming: boolean;
  showDeleteConfirm: boolean;
  deleteConfigName: string | null;

  // 操作状态
  isActivating: boolean;
  activationError: string | null;
  isSaving: boolean;
  savingError: string | null;
}

// 操作类型
export type ConfigAction =
  | { type: 'SET_APP_CONFIG'; payload: AppConfig }
  | { type: 'SET_APP_CONFIG_LOADING'; payload: boolean }
  | { type: 'SET_APP_CONFIG_ERROR'; payload: string | null }
  | { type: 'SET_CONFIGURATIONS'; payload: ConfigInfo[] }
  | { type: 'SET_CONFIGURATIONS_LOADING'; payload: boolean }
  | { type: 'SET_CONFIGURATIONS_ERROR'; payload: string | null }
  | { type: 'SET_CURRENT_CONFIG'; payload: { name: string; content: string; sessionId?: string; loadedAt?: number } }
  | { type: 'UPDATE_CURRENT_CONFIG_CONTENT'; payload: string }
  | { type: 'SET_CURRENT_CONFIG_VALIDATION'; payload: ValidationResult | null }
  | { type: 'MARK_MODIFIED'; payload: boolean }
  | { type: 'CLEAR_CURRENT_CONFIG' }
  | { type: 'SET_SELECTED_CONFIG'; payload: string | null }
  | { type: 'SET_CREATING_NEW'; payload: boolean }
  | { type: 'SET_RENAMING'; payload: boolean }
  | { type: 'SHOW_DELETE_CONFIRM'; payload: { show: boolean; configName?: string } }
  | { type: 'SET_ACTIVATING'; payload: boolean }
  | { type: 'SET_ACTIVATION_ERROR'; payload: string | null }
  | { type: 'SET_SAVING'; payload: boolean }
  | { type: 'SET_SAVING_ERROR'; payload: string | null };

// 对话框类型
export interface DialogConfig {
  title: string;
  message: string;
  type: 'info' | 'warning' | 'error' | 'confirm';
  buttons?: string[];
  onConfirm?: () => void;
  onCancel?: () => void;
}

// 通知类型
export interface NotificationConfig {
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number; // 毫秒
}

// Tauri API 响应包装类型
export type TauriResult<T> = {
  success: boolean;
  data?: T;
  error?: string;
};

// 默认应用配置
export const defaultAppConfig: AppConfig = {
  target_directory: '',
  last_active_config: undefined,
  app_settings: {
    auto_backup: true,
    confirm_delete: true,
  },
};

// 默认状态
export const defaultConfigManagerState: ConfigManagerState = {
  appConfig: null,
  isLoadingAppConfig: false,
  appConfigError: null,

  configurations: [],
  isLoadingConfigurations: false,
  configurationsError: null,

  currentConfig: null,

  selectedConfigName: null,
  isCreatingNew: false,
  isRenaming: false,
  showDeleteConfirm: false,
  deleteConfigName: null,

  isActivating: false,
  activationError: null,
  isSaving: false,
  savingError: null,
};

// 配置编辑器选项
export interface EditorOptions {
  theme: 'light' | 'dark';
  fontSize: number;
  tabSize: number;
  wordWrap: boolean;
  showLineNumbers: boolean;
  autoFormat: boolean;
  autoValidate: boolean;
}

export const defaultEditorOptions: EditorOptions = {
  theme: 'light',
  fontSize: 14,
  tabSize: 2,
  wordWrap: true,
  showLineNumbers: true,
  autoFormat: true,
  autoValidate: true,
};

// 常用的配置文件模板
export interface ConfigTemplate {
  name: string;
  description: string;
  content: string;
  category: string;
}

export const configTemplates: ConfigTemplate[] = [
  {
    name: '基础配置',
    description: '最基本的JSON配置文件模板',
    content: JSON.stringify({
      name: 'example-config',
      version: '1.0.0',
      settings: {
        enabled: true,
        debug: false,
      },
    }, null, 2),
    category: '通用',
  },
  {
    name: '数据库配置',
    description: '数据库连接配置模板',
    content: JSON.stringify({
      database: {
        host: 'localhost',
        port: 3306,
        username: 'user',
        password: 'password',
        database: 'mydb',
        pool: {
          min: 2,
          max: 10,
        },
      },
    }, null, 2),
    category: '数据库',
  },
  {
    name: 'API配置',
    description: 'API服务配置模板',
    content: JSON.stringify({
      api: {
        baseUrl: 'https://api.example.com',
        timeout: 5000,
        retries: 3,
        auth: {
          type: 'bearer',
          token: 'your-token-here',
        },
        endpoints: {
          users: '/users',
          posts: '/posts',
        },
      },
    }, null, 2),
    category: 'API',
  },
];