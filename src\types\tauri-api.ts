/**
 * Tauri命令的TypeScript类型定义
 * 提供完整的类型安全支持
 */

import { AppConfig, ConfigInfo, ValidationResult } from './config';

// Tauri命令的返回类型定义
export namespace TauriCommands {
  // 应用配置管理命令
  export type LoadAppConfig = () => Promise<AppConfig>;
  export type SaveAppConfig = (config: AppConfig) => Promise<void>;
  export type SelectTargetDirectory = () => Promise<string>;

  // 配置文件库管理命令
  export type ListConfigurations = () => Promise<ConfigInfo[]>;
  export type CreateConfiguration = (name: string, content: string) => Promise<void>;
  export type ReadConfiguration = (name: string) => Promise<string>;
  export type UpdateConfiguration = (name: string, content: string) => Promise<void>;
  export type RenameConfiguration = (oldName: string, newName: string) => Promise<void>;
  export type DeleteConfiguration = (name: string) => Promise<void>;
  export type ValidateJson = (content: string) => Promise<ValidationResult>;

  // 配置激活系统命令
  export type ActivateConfiguration = (name: string, targetDirectory: string) => Promise<void>;
  export type GetCurrentActiveConfig = () => Promise<string | null>;
  export type BackupExistingConfig = (targetFile: string) => Promise<string>;

  // 文件系统操作命令
  export type CheckPathExists = (path: string) => Promise<boolean>;
  export type CheckIsDirectory = (path: string) => Promise<boolean>;
  export type CheckIsWritable = (path: string) => Promise<boolean>;

  // JSON工具命令
  export type FormatJson = (content: string) => Promise<string>;
  export type MinifyJson = (content: string) => Promise<string>;
}

// Tauri命令调用的类型安全包装器
export class TauriAPI {
  private static async invoke<T>(command: string, args?: Record<string, unknown>): Promise<T> {
    const { invoke } = await import('@tauri-apps/api/core');
    return invoke<T>(command, args);
  }

  // 应用配置管理
  static async loadAppConfig(): Promise<AppConfig> {
    return this.invoke<AppConfig>('load_app_config');
  }

  static async saveAppConfig(config: AppConfig): Promise<void> {
    return this.invoke<void>('save_app_config', { config });
  }

  static async selectTargetDirectory(): Promise<string> {
    return this.invoke<string>('select_target_directory');
  }

  // 配置文件库管理
  static async listConfigurations(): Promise<ConfigInfo[]> {
    return this.invoke<ConfigInfo[]>('list_configurations');
  }

  static async createConfiguration(name: string, content: string): Promise<void> {
    return this.invoke<void>('create_configuration', { name, content });
  }

  static async readConfiguration(name: string): Promise<string> {
    return this.invoke<string>('read_configuration', { name });
  }

  static async updateConfiguration(name: string, content: string): Promise<void> {
    return this.invoke<void>('update_configuration', { name, content });
  }

  static async renameConfiguration(oldName: string, newName: string): Promise<void> {
    return this.invoke<void>('rename_configuration', { 
      oldName, 
      newName 
    });
  }

  static async deleteConfiguration(name: string): Promise<void> {
    return this.invoke<void>('delete_configuration', { name });
  }

  static async validateJson(content: string): Promise<ValidationResult> {
    return this.invoke<ValidationResult>('validate_json', { content });
  }

  // 配置激活系统
  static async activateConfiguration(name: string, targetDirectory: string): Promise<void> {
    return this.invoke<void>('activate_configuration', { 
      name, 
      targetDirectory 
    });
  }

  static async getCurrentActiveConfig(): Promise<string | null> {
    return this.invoke<string | null>('get_current_active_config');
  }

  static async backupExistingConfig(targetFile: string): Promise<string> {
    return this.invoke<string>('backup_existing_config', { targetFile });
  }

  // 文件系统操作
  static async checkPathExists(path: string): Promise<boolean> {
    return this.invoke<boolean>('check_path_exists', { path });
  }

  static async checkIsDirectory(path: string): Promise<boolean> {
    return this.invoke<boolean>('check_is_directory', { path });
  }

  static async checkIsWritable(path: string): Promise<boolean> {
    return this.invoke<boolean>('check_is_writable', { path });
  }

  // JSON工具
  static async formatJson(content: string): Promise<string> {
    return this.invoke<string>('format_json', { content });
  }

  static async minifyJson(content: string): Promise<string> {
    return this.invoke<string>('minify_json', { content });
  }
}

// 命令错误处理类型
export interface TauriCommandError {
  message: string;
  code?: string;
  details?: unknown;
}

// 类型保护函数
export function isTauriCommandError(error: unknown): error is TauriCommandError {
  return (
    typeof error === 'object' &&
    error !== null &&
    'message' in error &&
    typeof (error as TauriCommandError).message === 'string'
  );
}