import { useCallback } from 'react';
import { TauriAPI } from '../../types/tauri-api';
import { ValidationResult, ConfigManagerState, NotificationConfig } from '../../types/config';

/**
 * 配置编辑器Hook参数接口
 */
interface UseConfigEditorParams {
  state: ConfigManagerState;
  dispatch: (action: any) => void;
  showNotification: (config: NotificationConfig) => void;
}

/**
 * 配置编辑器状态管理Hook
 * 专门负责配置内容的读取、编辑、验证和格式化
 */
export function useConfigEditor({ state, dispatch, showNotification }: UseConfigEditorParams) {
  // 读取配置内容 - 简化逻辑，避免状态竞态
  const readConfiguration = useCallback(async (name: string) => {
    try {
      const content = await TauriAPI.readConfiguration(name);
      
      // 验证内容完整性
      if (typeof content !== 'string') {
        throw new Error('读取的配置内容格式无效');
      }
      
      // 直接设置当前配置
      dispatch({ 
        type: 'SET_CURRENT_CONFIG', 
        payload: { 
          name, 
          content,
          sessionId: `${name}_${Date.now()}`,
          loadedAt: Date.now()
        } 
      });
      
      return { content };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      showNotification({
        type: 'error',
        title: '读取配置失败',
        message: `配置文件 "${name}" 读取失败: ${errorMessage}`,
      });
      
      throw error;
    }
  }, [dispatch, showNotification]);

  // 更新当前配置内容 - 添加内容验证
  const updateCurrentConfigContent = useCallback((content: string) => {
    if (!state.currentConfig) {
      console.warn('尝试更新内容但当前没有选中的配置');
      return;
    }
    
    // 验证内容类型
    if (typeof content !== 'string') {
      console.warn('尝试设置非字符串内容到配置');
      return;
    }
    
    dispatch({ type: 'UPDATE_CURRENT_CONFIG_CONTENT', payload: content });
  }, [state.currentConfig, dispatch]);

  // 验证JSON内容
  const validateJson = useCallback(async (content: string) => {
    try {
      const result = await TauriAPI.validateJson(content);
      dispatch({ type: 'SET_CURRENT_CONFIG_VALIDATION', payload: result });
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorResult: ValidationResult = {
        is_valid: false,
        errors: [{
          line: 1,
          column: 1,
          message: errorMessage,
          error_type: 'SyntaxError',
        }],
        warnings: [],
      };
      dispatch({ type: 'SET_CURRENT_CONFIG_VALIDATION', payload: errorResult });
      return errorResult;
    }
  }, [dispatch]);

  // 格式化JSON
  const formatJson = useCallback(async (content: string) => {
    try {
      const formatted = await TauriAPI.formatJson(content);
      return formatted;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      showNotification({
        type: 'error',
        title: 'JSON格式化失败',
        message: errorMessage,
      });
      throw error;
    }
  }, [showNotification]);

  // 检查配置是否已修改
  const isConfigurationModified = useCallback(() => {
    return state.currentConfig?.isModified || false;
  }, [state.currentConfig]);

  // 获取当前配置的验证状态
  const getValidationStatus = useCallback(() => {
    if (!state.currentConfig?.validationResult) {
      return { isValid: true, hasWarnings: false, errorCount: 0 };
    }
    
    const result = state.currentConfig.validationResult;
    return {
      isValid: result.is_valid,
      hasWarnings: result.warnings.length > 0,
      errorCount: result.errors.length,
      warningCount: result.warnings.length,
    };
  }, [state.currentConfig]);

  return {
    readConfiguration,
    updateCurrentConfigContent,
    validateJson,
    formatJson,
    isConfigurationModified,
    getValidationStatus,
  };
}