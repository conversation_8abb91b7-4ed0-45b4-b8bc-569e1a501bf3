use log::{info, warn, error, debug};
use std::sync::Once;

static INIT: Once = Once::new();

pub struct Logger;

impl Logger {
    /// 初始化日志系统
    pub fn init() {
        INIT.call_once(|| {
            env_logger::Builder::from_default_env()
                .filter_level(log::LevelFilter::Info)
                .init();
            
            info!("日志系统初始化完成");
        });
    }

    /// 记录信息日志
    pub fn info(message: &str) {
        info!("{}", message);
    }

    /// 记录警告日志
    pub fn warn(message: &str) {
        warn!("{}", message);
    }

    /// 记录错误日志
    pub fn error(message: &str) {
        error!("{}", message);
    }

    /// 记录调试日志
    pub fn debug(message: &str) {
        debug!("{}", message);
    }

    /// 记录操作日志，包含操作类型和详细信息
    pub fn log_operation(operation: &str, details: &str) {
        info!("操作: {} - {}", operation, details);
    }

    /// 记录配置相关操作
    pub fn log_config_operation(operation: &str, config_name: &str, details: Option<&str>) {
        let message = match details {
            Some(detail) => format!("配置操作: {} [{}] - {}", operation, config_name, detail),
            None => format!("配置操作: {} [{}]", operation, config_name),
        };
        info!("{}", message);
    }

    /// 记录文件操作
    pub fn log_file_operation(operation: &str, file_path: &str, success: bool) {
        let status = if success { "成功" } else { "失败" };
        info!("文件操作: {} [{}] - {}", operation, file_path, status);
    }

    /// 记录错误详细信息
    pub fn log_error_detail(context: &str, error: &str) {
        error!("{}: {}", context, error);
    }

    /// 记录用户操作
    pub fn log_user_action(action: &str, target: Option<&str>) {
        let message = match target {
            Some(t) => format!("用户操作: {} -> {}", action, t),
            None => format!("用户操作: {}", action),
        };
        info!("{}", message);
    }
}

// 便于使用的宏
#[macro_export]
macro_rules! log_info {
    ($($arg:tt)*) => {
        crate::logger::Logger::info(&format!($($arg)*))
    };
}

#[macro_export]
macro_rules! log_warn {
    ($($arg:tt)*) => {
        crate::logger::Logger::warn(&format!($($arg)*))
    };
}

#[macro_export]
macro_rules! log_error {
    ($($arg:tt)*) => {
        crate::logger::Logger::error(&format!($($arg)*))
    };
}

#[macro_export]
macro_rules! log_debug {
    ($($arg:tt)*) => {
        crate::logger::Logger::debug(&format!($($arg)*))
    };
}