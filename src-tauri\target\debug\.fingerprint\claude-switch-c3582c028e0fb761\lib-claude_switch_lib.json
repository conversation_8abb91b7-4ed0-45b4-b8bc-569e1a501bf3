{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[]", "target": 6114574821762673293, "profile": 8731458305071235362, "path": 10763286916239946207, "deps": [[3572497473143799953, "build_script_build", false, 6944471394336290590], [5986029879202738730, "log", false, 11718238075132384891], [6825153089788476225, "tauri_plugin_dialog", false, 1845818855160674719], [8256202458064874477, "dirs", false, 9087887326732178353], [8319709847752024821, "uuid", false, 6979933589883007550], [9689903380558560274, "serde", false, 17400843983642683788], [9897246384292347999, "chrono", false, 422669289784912361], [12092653563678505622, "tauri", false, 10528297172954712415], [12103695930867503580, "env_logger", false, 13359838560301891570], [12504415026414629397, "tauri_plugin_fs", false, 7422136416433387782], [16362055519698394275, "serde_json", false, 10871486420267568902], [16702348383442838006, "tauri_plugin_opener", false, 17855640385034759389], [17531218394775549125, "tokio", false, 8410187807422300245]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\claude-switch-c3582c028e0fb761\\dep-lib-claude_switch_lib", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}