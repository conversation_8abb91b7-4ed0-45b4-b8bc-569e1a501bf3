import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Badge,
  Field,
  Input,
} from '@fluentui/react-components';
import {
  Folder24Regular,
  FolderOpen24Regular,
  Settings24Regular,
  Checkmark24Regular,
  Warning24Regular,
} from '@fluentui/react-icons';

interface AppConfig {
  target_directory: string | null;
  last_active_config: string | null;
}

interface FluentDirectorySelectorProps {
  appConfig: AppConfig | null;
  onConfigUpdate: (config: AppConfig) => Promise<void>;
  onSelectDirectory: () => Promise<string>;
}

export const FluentDirectorySelector: React.FC<FluentDirectorySelectorProps> = ({
  appConfig,
  onConfigUpdate,
  onSelectDirectory,
}) => {
  const [isSelecting, setIsSelecting] = React.useState(false);

  const handleSelectDirectory = async () => {
    if (isSelecting) return;
    
    setIsSelecting(true);
    try {
      const selectedPath = await onSelectDirectory();
      if (selectedPath && selectedPath.trim()) {
        const updatedConfig: AppConfig = {
          target_directory: selectedPath,
          last_active_config: appConfig?.last_active_config || null,
        };
        await onConfigUpdate(updatedConfig);
      }
    } catch (error) {
      console.error('选择目录失败:', error);
    } finally {
      setIsSelecting(false);
    }
  };

  const getDirectoryStatus = () => {
    if (!appConfig?.target_directory) {
      return {
        status: 'not-set',
        icon: <Warning24Regular />,
        color: 'warning' as const,
        text: '未设置',
      };
    }
    
    return {
      status: 'set',
      icon: <Checkmark24Regular />,
      color: 'success' as const,
      text: '已设置',
    };
  };

  const directoryStatus = getDirectoryStatus();

  return (
    <Card>
      <CardHeader
        image={<Folder24Regular />}
        header={
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <Text weight="semibold">目标目录设置</Text>
            <Badge 
              appearance="filled" 
              color={directoryStatus.color}
              icon={directoryStatus.icon}
            >
              {directoryStatus.text}
            </Badge>
          </div>
        }
        description={
          <Text size={200} style={{ color: 'var(--colorNeutralForeground3)' }}>
            配置文件将激活到此目录中的 settings.json
          </Text>
        }
      />

      <div style={{ padding: '16px 16px 20px 16px' }}>
        {appConfig?.target_directory ? (
          <Field label="当前目标目录">
            <div style={{ display: 'flex', gap: '8px', alignItems: 'flex-end' }}>
              <Input
                value={appConfig.target_directory}
                readOnly
                style={{ 
                  flex: 1,
                  fontFamily: 'var(--fontFamilyMonospace)',
                  fontSize: '13px',
                }}
              />
              <Button
                appearance="secondary"
                icon={<FolderOpen24Regular />}
                onClick={handleSelectDirectory}
                disabled={isSelecting}
              >
                {isSelecting ? '选择中...' : '更改'}
              </Button>
            </div>
          </Field>
        ) : (
          <div style={{ textAlign: 'center', padding: '20px 0' }}>
            <Settings24Regular 
              style={{ 
                fontSize: '32px', 
                color: 'var(--colorNeutralForeground3)',
                marginBottom: '12px',
                display: 'block',
                margin: '0 auto 12px auto'
              }} 
            />
            <Text size={300} style={{ marginBottom: '16px', display: 'block' }}>
              请选择配置文件的目标目录
            </Text>
            <Text size={200} style={{ 
              color: 'var(--colorNeutralForeground3)', 
              marginBottom: '20px',
              display: 'block',
              lineHeight: '1.4'
            }}>
              选择一个目录用于存放激活的 settings.json 配置文件。
              这通常是您的应用程序读取配置的目录。
            </Text>
            <Button
              appearance="primary"
              icon={<FolderOpen24Regular />}
              onClick={handleSelectDirectory}
              disabled={isSelecting}
              size="large"
            >
              {isSelecting ? '选择中...' : '选择目录'}
            </Button>
          </div>
        )}

        {appConfig?.last_active_config && appConfig.target_directory && (
          <div style={{ 
            marginTop: '16px',
            padding: '12px',
            backgroundColor: 'var(--colorNeutralBackground3)',
            borderRadius: 'var(--borderRadiusMedium)',
            border: '1px solid var(--colorNeutralStroke2)'
          }}>
            <Text size={200} style={{ color: 'var(--colorNeutralForeground3)' }}>
              当前激活配置:
            </Text>
            <Text 
              size={200} 
              weight="semibold"
              style={{ 
                marginLeft: '8px',
                fontFamily: 'var(--fontFamilyMonospace)',
                color: 'var(--colorBrandForeground1)'
              }}
            >
              {appConfig.last_active_config}
            </Text>
          </div>
        )}
      </div>
    </Card>
  );
};