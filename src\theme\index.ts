// Fluent UI主题配置
import {
  BrandVariants,
  createLightTheme,
  createDarkTheme,
  Theme
} from '@fluentui/react-components';

// 自定义品牌色系 - 基于配置管理应用的特点
const configManagerBrand: BrandVariants = {
  10: "#020305",
  20: "#111418", 
  30: "#16202D",
  40: "#1B2B42",
  50: "#203657",
  60: "#25426C",
  70: "#2A4F81",
  80: "#2F5C96",
  90: "#3469AB",
  100: "#3975C0",
  110: "#3E82D5",
  120: "#438FEA",
  130: "#489CFF",
  140: "#62A9FF",
  150: "#7CB5FF",
  160: "#96C2FF"
};

// 创建自定义浅色主题
export const configManagerLightTheme: Theme = {
  ...createLightTheme(configManagerBrand),
};

// 创建自定义深色主题
export const configManagerDarkTheme: Theme = {
  ...createDarkTheme(configManagerBrand),
};

// 主题切换hook
export type ThemeMode = 'light' | 'dark' | 'system';

export const getTheme = (mode: ThemeMode): Theme => {
  if (mode === 'system') {
    // 检测系统主题偏好
    const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    return systemPrefersDark ? configManagerDarkTheme : configManagerLightTheme;
  }
  return mode === 'dark' ? configManagerDarkTheme : configManagerLightTheme;
};

// 默认使用系统主题
export const defaultTheme = getTheme('system');