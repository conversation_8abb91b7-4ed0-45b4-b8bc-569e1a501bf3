/**
 * 应用程序清理管理器
 * 统一管理应用卸载时的资源清理，防止内存泄漏
 */

import { globalEventManager } from './eventManager';
import { cleanupMonacoTheme } from '../hooks/useMonacoThemeSimple';

/**
 * 清理函数注册表
 */
class AppCleanupManager {
  private cleanupFunctions = new Set<() => void>();
  private isCleaningUp = false;

  /**
   * 注册清理函数
   * @param cleanup 清理函数
   * @returns 取消注册的函数
   */
  register(cleanup: () => void): () => void {
    this.cleanupFunctions.add(cleanup);
    
    // 返回取消注册函数
    return () => {
      this.cleanupFunctions.delete(cleanup);
    };
  }

  /**
   * 执行所有清理函数
   */
  cleanup(): void {
    if (this.isCleaningUp) return;
    
    this.isCleaningUp = true;
    
    console.log(`开始清理应用资源，共 ${this.cleanupFunctions.size} 个清理函数`);
    
    // 执行所有注册的清理函数
    this.cleanupFunctions.forEach(cleanup => {
      try {
        cleanup();
      } catch (error) {
        console.error('清理函数执行失败:', error);
      }
    });
    
    // 清理全局事件管理器
    try {
      globalEventManager.cleanup();
    } catch (error) {
      console.error('全局事件管理器清理失败:', error);
    }
    
    // 清理Monaco主题资源
    try {
      cleanupMonacoTheme();
    } catch (error) {
      console.error('Monaco主题清理失败:', error);
    }
    
    // 清空清理函数集合
    this.cleanupFunctions.clear();
    
    console.log('应用资源清理完成');
  }

  /**
   * 获取当前注册的清理函数数量
   */
  getRegisteredCount(): number {
    return this.cleanupFunctions.size;
  }
}

/**
 * 全局清理管理器实例
 */
export const appCleanupManager = new AppCleanupManager();

/**
 * 注册页面卸载事件处理
 */
if (typeof window !== 'undefined') {
  // 页面卸载时清理资源
  window.addEventListener('beforeunload', () => {
    appCleanupManager.cleanup();
  });

  // 页面隐藏时清理资源（移动端支持）
  window.addEventListener('pagehide', () => {
    appCleanupManager.cleanup();
  });

  // 应用失去焦点时进行轻量清理
  window.addEventListener('blur', () => {
    // 可以在这里添加轻量清理逻辑
    console.log('应用失去焦点，可进行轻量清理');
  });
}

/**
 * React Hook 用于组件清理
 * 注意：需要在使用此Hook的组件中导入React
 * 
 * 使用示例：
 * ```tsx
 * import { useEffect } from 'react';
 * import { appCleanupManager } from '../utils/appCleanup';
 * 
 * function MyComponent() {
 *   useEffect(() => {
 *     const cleanup = () => {
 *       // 清理逻辑
 *     };
 *     
 *     const unregister = appCleanupManager.register(cleanup);
 *     return unregister;
 *   }, []);
 * }
 * ```
 */