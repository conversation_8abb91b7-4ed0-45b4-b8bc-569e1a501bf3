/* 全局样式重置和Fluent2兼容性 */
html, body {
  margin: 0;
  padding: 0;
  font-family: var(--fontFamilyBase, 'Segoe UI', 'Roboto', 'Helvetica Neue', sans-serif);
  font-size: var(--fontSizeBase300, 14px);
  line-height: var(--lineHeightBase300, 1.4);
  color: var(--colorNeutralForeground1);
  background-color: var(--colorNeutralBackground1);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 应用根容器 */
#root {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

.app {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--colorNeutralBackground2);
}

::-webkit-scrollbar-thumb {
  background: var(--colorNeutralStroke2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--colorNeutralStroke1);
}

/* 选择文本样式 */
::selection {
  background-color: var(--colorBrandBackground2);
  color: var(--colorBrandForeground2);
}

/* 焦点样式 */
*:focus-visible {
  outline: 2px solid var(--colorBrandStroke1);
  outline-offset: 2px;
  border-radius: var(--borderRadiusSmall);
}

/* 禁用状态样式 */
*:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 减少动画（可访问性） */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  * {
    border-color: ButtonText !important;
  }
}

/* 打印样式 */
@media print {
  .app {
    background: white !important;
    color: black !important;
  }
  
  button,
  .theme-toggle {
    display: none !important;
  }
}