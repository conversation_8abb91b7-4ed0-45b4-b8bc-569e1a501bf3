/**
 * 防抖工具函数
 * 用于限制函数执行频率，防止快速连续调用造成性能问题
 */

/**
 * 基础防抖函数
 * @param func 要防抖的函数
 * @param delay 防抖延迟时间（毫秒）
 * @param immediate 是否立即执行第一次调用
 * @returns 防抖后的函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number,
  immediate: boolean = false
): T & { cancel: () => void } {
  let timeoutId: number | null = null;
  let hasExecuted = false;

  const debounced = ((...args: Parameters<T>) => {
    const callNow = immediate && !hasExecuted;
    
    // 清除之前的定时器
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    if (callNow) {
      hasExecuted = true;
      return func.apply(null, args);
    }

    // 设置新的定时器
    timeoutId = setTimeout(() => {
      timeoutId = null;
      hasExecuted = false;
      if (!immediate) {
        func.apply(null, args);
      }
    }, delay);
  }) as T & { cancel: () => void };

  // 添加取消方法
  debounced.cancel = () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }
    hasExecuted = false;
  };

  return debounced;
}

/**
 * 主题切换专用防抖函数
 * 针对主题切换操作的特定优化
 */
export function debounceThemeChange<T extends (...args: any[]) => any>(
  func: T,
  delay: number = 150
): T & { cancel: () => void } {
  return debounce(func, delay, true); // 立即执行第一次，后续防抖
}

/**
 * 文件操作防抖函数
 * 针对文件读写操作的防抖优化
 */
export function debounceFileOperation<T extends (...args: any[]) => any>(
  func: T,
  delay: number = 300
): T & { cancel: () => void } {
  return debounce(func, delay, false); // 延迟执行，避免频繁文件操作
}

/**
 * 搜索输入防抖函数
 * 针对用户输入搜索的防抖优化
 */
export function debounceSearch<T extends (...args: any[]) => any>(
  func: T,
  delay: number = 500
): T & { cancel: () => void } {
  return debounce(func, delay, false); // 延迟执行，减少搜索请求
}