import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from '@fluentui/react-components';
import {
  Save24Regular,
  Play24Regular,
  Dismiss24Regular,
  DocumentBulletList24Regular,
  Checkmark24Regular,
  ErrorCircle24Regular,
} from '@fluentui/react-icons';
import { useEditorStyles, useStatusStyles } from '../styles/fluent-styles';

interface ValidationResult {
  is_valid: boolean;
  errors: Array<{
    line: number;
    column: number;
    message: string;
    error_type: string;
  }>;
  warnings: string[];
}

interface FluentConfigEditorProps {
  configName: string;
  content: string;
  isModified: boolean;
  validationResult: ValidationResult | null;
  isSaving: boolean;
  onContentChange: (content: string) => void;
  onSave: (name: string, content: string) => Promise<void>;
  onFormat: (content: string) => Promise<string>;
  onClose: () => void;
  onActivate?: () => void;
}

export const FluentConfigEditor: React.FC<FluentConfigEditorProps> = ({
  configName,
  content,
  isModified,
  validationResult,
  isSaving,
  onContentChange,
  onSave,
  onFormat,
  onClose,
  onActivate,
}) => {
  const styles = useEditorStyles();
  const statusStyles = useStatusStyles();

  const handleContentChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newContent = event.target.value;
    onContentChange(newContent);
  };

  const handleSave = async () => {
    if (isSaving) return;
    await onSave(configName, content);
  };

  const handleFormat = async () => {
    try {
      const formatted = await onFormat(content);
      onContentChange(formatted);
    } catch (error) {
      console.error('格式化失败:', error);
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.ctrlKey || event.metaKey) {
      switch (event.key) {
        case 's':
          event.preventDefault();
          // 只有在内容有变更时才保存
          if (isModified) {
            handleSave();
          }
          break;
        case 'f':
          event.preventDefault();
          handleFormat();
          break;
      }
    }
  };

  const getValidationStatus = () => {
    if (!validationResult) return null;
    
    if (validationResult.is_valid) {
      return (
        <Badge 
          appearance="filled" 
          color="success" 
          icon={<Checkmark24Regular />}
        >
          格式正确
        </Badge>
      );
    } else {
      return (
        <Badge 
          appearance="filled" 
          color="danger" 
          icon={<ErrorCircle24Regular />}
        >
          格式错误
        </Badge>
      );
    }
  };

  const getValidationDetails = () => {
    if (!validationResult) return null;

    return (
      <>
        {validationResult.errors.length > 0 && (
          <div style={{ marginTop: '8px' }}>
            <Text size={200} className={statusStyles.invalid}>
              错误: {validationResult.errors.map(e => e.message).join(', ')}
            </Text>
          </div>
        )}
        {validationResult.warnings.length > 0 && (
          <div style={{ marginTop: '4px' }}>
            <Text size={200} className={statusStyles.warning}>
              警告: {validationResult.warnings.join(', ')}
            </Text>
          </div>
        )}
      </>
    );
  };

  return (
    <Card className={styles.container}>
      <CardHeader
        image={<DocumentBulletList24Regular />}
        header={
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <Text className={styles.headerTitle}>
              {configName}
              {isModified && <span style={{ color: 'var(--colorPaletteYellowForeground2)' }}>*</span>}
            </Text>
            {getValidationStatus()}
          </div>
        }
        description={
          <div>
            <Text size={200} style={{ color: 'var(--colorNeutralForeground3)' }}>
              JSON配置文件编辑器
            </Text>
            {getValidationDetails()}
          </div>
        }
        action={
          <Button
            appearance="subtle"
            icon={<Dismiss24Regular />}
            onClick={onClose}
            title="关闭编辑器"
          />
        }
      />

      <div className={styles.editorArea}>
        {/* 工具栏 */}
        <Toolbar>
          <ToolbarButton
            appearance="primary"
            icon={<Save24Regular />}
            onClick={handleSave}
            disabled={!isModified || isSaving}
          >
            {isSaving ? <Spinner size="tiny" /> : '保存配置文件'}
          </ToolbarButton>
          
          <ToolbarButton
            appearance="subtle"
            onClick={handleFormat}
            disabled={isSaving}
          >
            格式化
          </ToolbarButton>
          
          <ToolbarDivider />
          
          {onActivate && validationResult?.is_valid && (
            <ToolbarButton
              appearance="subtle"
              icon={<Play24Regular />}
              onClick={onActivate}
              disabled={isModified || isSaving}
            >
              激活配置
            </ToolbarButton>
          )}
        </Toolbar>

        {/* 编辑器 */}
        <Textarea
          className={styles.textarea}
          value={content}
          onChange={handleContentChange}
          onKeyDown={handleKeyDown}
          placeholder="在此输入JSON配置内容..."
          rows={20}
          resize="none"
          style={{ 
            fontFamily: 'var(--fontFamilyMonospace)',
            fontSize: '14px',
            lineHeight: '1.5',
          }}
        />

        {/* 快捷键提示 */}
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          paddingTop: '8px',
          borderTop: '1px solid var(--colorNeutralStroke2)',
        }}>
          <Text size={100} style={{ color: 'var(--colorNeutralForeground3)' }}>
            快捷键: Ctrl+S 保存 | Ctrl+F 格式化
          </Text>
          <Text size={100} style={{ color: 'var(--colorNeutralForeground3)' }}>
            {content.split('\n').length} 行 | {content.length} 字符
          </Text>
        </div>
      </div>
    </Card>
  );
};