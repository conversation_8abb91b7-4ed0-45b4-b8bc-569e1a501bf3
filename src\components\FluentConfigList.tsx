import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>u<PERSON><PERSON>,
  <PERSON>u<PERSON><PERSON><PERSON>,
  MenuTrigger,
  Text,
  Spinner,
  MessageBar,
  MessageBarBody,
} from '@fluentui/react-components';
import {
  Settings24Regular,
  Add24Regular,
  MoreHorizontal24Regular,
  Edit24Regular,
  Delete24Regular,
  Play24Regular,
  DocumentText24Regular,
  Warning24Regular,
  CheckmarkCircle24Regular
} from '@fluentui/react-icons';
import { useConfigListStyles } from '../styles/fluent-styles';
import { ConfigInfo } from '../types/config';

interface FluentConfigListProps {
  configurations: ConfigInfo[];
  selectedConfig: string | null;
  activeConfig: string | null;
  editingFiles?: Set<string>;
  isLoading: boolean;
  error: string | null;
  onSelect: (configName: string | null) => void;
  onEdit: (configName: string) => void;
  onRename: (configName: string) => void;
  onDelete: (configName: string) => void;
  onActivate: (configName: string) => void;
  onRefresh: () => void;
  onCreateNew: () => void;
}

export const FluentConfigList: React.FC<FluentConfigListProps> = ({
  configurations,
  selectedConfig,
  activeConfig,
  editingFiles = new Set(),
  isLoading,
  error,
  onSelect,
  onEdit,
  onRename,
  onDelete,
  onActivate,
  onRefresh,
  onCreateNew,
}) => {
  const styles = useConfigListStyles();

  const getConfigStatus = (config: ConfigInfo) => {
    if (config.name === activeConfig) return 'active';
    if (!config.is_valid) return 'invalid';
    return 'normal';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckmarkCircle24Regular style={{ color: 'var(--colorPaletteGreenForeground2)' }} />;
      case 'invalid':
        return <Warning24Regular style={{ color: 'var(--colorPaletteRedForeground2)' }} />;
      default:
        return <DocumentText24Regular style={{ color: 'var(--colorNeutralForeground3)' }} />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge appearance="filled" color="success" size="small">激活中</Badge>;
      case 'invalid':
        return <Badge appearance="filled" color="danger" size="small">格式错误</Badge>;
      default:
        return null;
    }
  };

  if (isLoading) {
    return (
      <div className={styles.container}>
        <div className={styles.header}>
          <Text className={styles.headerTitle}>配置库</Text>
          <Button
            appearance="primary"
            icon={<Add24Regular />}
            size="small"
            onClick={onCreateNew}
          >
            新建
          </Button>
        </div>
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}>
          <Spinner size="medium" label="加载配置列表..." />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.container}>
        <div className={styles.header}>
          <Text className={styles.headerTitle}>配置库</Text>
          <Button
            appearance="primary"
            icon={<Add24Regular />}
            size="small"
            onClick={onCreateNew}
          >
            新建
          </Button>
        </div>
        <MessageBar intent="error">
          <MessageBarBody>
            <Text>加载配置失败: {error}</Text>
            <Button appearance="secondary" size="small" onClick={onRefresh}>
              重试
            </Button>
          </MessageBarBody>
        </MessageBar>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <Text className={styles.headerTitle}>配置库</Text>
        <Button
          appearance="primary"
          icon={<Add24Regular />}
          size="small"
          onClick={onCreateNew}
        >
          新建
        </Button>
      </div>

      <div className={styles.list}>
        {configurations.length === 0 ? (
          <div style={{ 
            textAlign: 'center', 
            padding: '40px 20px', 
            color: 'var(--colorNeutralForeground3)' 
          }}>
            <Settings24Regular style={{ fontSize: '48px', marginBottom: '16px' }} />
            <Text size={300}>暂无配置文件</Text>
            <br />
            <Button 
              appearance="subtle" 
              onClick={onCreateNew}
              style={{ marginTop: '12px' }}
            >
              创建第一个配置
            </Button>
          </div>
        ) : (
          configurations.map((config) => {
            const status = getConfigStatus(config);
            const isSelected = config.name === selectedConfig;
            
            return (
              <Card
                key={config.name}
                className={`${styles.listItem} ${
                  status === 'active' ? styles.listItemActive : 
                  status === 'invalid' ? styles.listItemInvalid : ''
                }`}
                appearance={isSelected ? "filled-alternative" : "outline"}
                onClick={() => onSelect(config.name)}
                style={{ cursor: 'pointer' }}
              >
                <CardHeader
                  image={getStatusIcon(status)}
                  header={
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      <Text className={styles.itemName}>{config.name}</Text>
                      {editingFiles.has(config.name) && (
                        <div style={{
                          width: '8px',
                          height: '8px',
                          borderRadius: '50%',
                          backgroundColor: 'var(--colorBrandBackground)',
                          title: '文件已修改'
                        }} />
                      )}
                      {getStatusBadge(status)}
                    </div>
                  }
                  description={
                    <Text size={200} style={{ color: 'var(--colorNeutralForeground3)' }}>
                      配置文件
                    </Text>
                  }
                  action={
                    <Menu>
                      <MenuTrigger disableButtonEnhancement>
                        <Button
                          appearance="subtle"
                          icon={<MoreHorizontal24Regular />}
                          size="small"
                          onClick={(e) => e.stopPropagation()}
                        />
                      </MenuTrigger>
                      <MenuPopover>
                        <MenuList>
                          <MenuItem
                            icon={<Edit24Regular />}
                            onClick={(e) => {
                              e.stopPropagation();
                              onEdit(config.name);
                            }}
                          >
                            编辑
                          </MenuItem>
                          <MenuItem
                            icon={<Edit24Regular />}
                            onClick={(e) => {
                              e.stopPropagation();
                              onRename(config.name);
                            }}
                          >
                            重命名
                          </MenuItem>
                          {config.is_valid && (
                            <MenuItem
                              icon={<Play24Regular />}
                              onClick={(e) => {
                                e.stopPropagation();
                                onActivate(config.name);
                              }}
                            >
                              激活配置
                            </MenuItem>
                          )}
                          <MenuItem
                            icon={<Delete24Regular />}
                            onClick={(e) => {
                              e.stopPropagation();
                              onDelete(config.name);
                            }}
                          >
                            删除
                          </MenuItem>
                        </MenuList>
                      </MenuPopover>
                    </Menu>
                  }
                />
              </Card>
            );
          })
        )}
      </div>
    </div>
  );
};