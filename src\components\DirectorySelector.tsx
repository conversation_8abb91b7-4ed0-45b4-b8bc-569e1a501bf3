import React, { useState, useCallback } from 'react';
import { AppConfig } from '../types/config';

interface DirectorySelectorProps {
  appConfig: AppConfig | null;
  onConfigUpdate: (config: AppConfig) => Promise<void>;
  onSelectDirectory: () => Promise<string | null>;
  className?: string;
}

export const DirectorySelector: React.FC<DirectorySelectorProps> = ({
  appConfig,
  onConfigUpdate,
  onSelectDirectory,
  className = '',
}) => {
  const [isSelecting, setIsSelecting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 处理目录选择
  const handleSelectDirectory = useCallback(async () => {
    setIsSelecting(true);
    setError(null);

    try {
      const selectedPath = await onSelectDirectory();
      
      if (selectedPath && appConfig) {
        const updatedConfig: AppConfig = {
          ...appConfig,
          target_directory: selectedPath,
        };
        
        await onConfigUpdate(updatedConfig);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '选择目录失败';
      setError(errorMessage);
    } finally {
      setIsSelecting(false);
    }
  }, [appConfig, onConfigUpdate, onSelectDirectory]);

  // 清除目标目录
  const handleClearDirectory = useCallback(async () => {
    if (!appConfig) return;

    try {
      const updatedConfig: AppConfig = {
        ...appConfig,
        target_directory: '',
      };
      
      await onConfigUpdate(updatedConfig);
      setError(null);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '清除目录失败';
      setError(errorMessage);
    }
  }, [appConfig, onConfigUpdate]);

  return (
    <div className={`directory-selector ${className}`}>
      <div className="directory-selector-header">
        <h3>目标目录设置</h3>
        <p className="directory-selector-description">
          选择配置文件激活后的目标目录，配置将被复制到此位置。
        </p>
      </div>

      <div className="directory-selector-content">
        <div className="current-directory">
          <label className="directory-label">当前目标目录:</label>
          <div className="directory-display">
            {appConfig?.target_directory ? (
              <div className="directory-path">
                <span className="path-text" title={appConfig.target_directory}>
                  {appConfig.target_directory}
                </span>
                <button
                  type="button"
                  className="clear-button"
                  onClick={handleClearDirectory}
                  title="清除目标目录"
                  disabled={isSelecting}
                >
                  ✕
                </button>
              </div>
            ) : (
              <span className="no-directory">未设置目标目录</span>
            )}
          </div>
        </div>

        <div className="directory-actions">
          <button
            type="button"
            className="select-button primary"
            onClick={handleSelectDirectory}
            disabled={isSelecting}
          >
            {isSelecting ? (
              <>
                <span className="loading-spinner"></span>
                选择中...
              </>
            ) : (
              <>
                📁 选择目录
              </>
            )}
          </button>

          {appConfig?.target_directory && (
            <div className="directory-info">
              <small className="info-text">
                💡 配置激活时将复制到此目录
              </small>
            </div>
          )}
        </div>

        {error && (
          <div className="error-message">
            <span className="error-icon">⚠️</span>
            {error}
          </div>
        )}
      </div>

      <style jsx>{`
        .directory-selector {
          background: #f9f9f9;
          border: 1px solid #e0e0e0;
          border-radius: 8px;
          padding: 20px;
          margin-bottom: 20px;
        }

        .directory-selector-header {
          margin-bottom: 15px;
        }

        .directory-selector-header h3 {
          margin: 0 0 8px 0;
          color: #333;
          font-size: 16px;
          font-weight: 600;
        }

        .directory-selector-description {
          margin: 0;
          color: #666;
          font-size: 14px;
          line-height: 1.4;
        }

        .directory-selector-content {
          display: flex;
          flex-direction: column;
          gap: 15px;
        }

        .current-directory {
          display: flex;
          flex-direction: column;
          gap: 8px;
        }

        .directory-label {
          font-weight: 500;
          color: #555;
          font-size: 14px;
        }

        .directory-display {
          min-height: 40px;
          display: flex;
          align-items: center;
        }

        .directory-path {
          display: flex;
          align-items: center;
          background: white;
          border: 1px solid #ddd;
          border-radius: 4px;
          padding: 8px 12px;
          flex: 1;
          min-height: 40px;
          box-sizing: border-box;
        }

        .path-text {
          flex: 1;
          font-family: 'Consolas', 'Monaco', monospace;
          font-size: 13px;
          color: #333;
          word-break: break-all;
          line-height: 1.4;
        }

        .clear-button {
          background: #ff4757;
          color: white;
          border: none;
          border-radius: 3px;
          width: 24px;
          height: 24px;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          margin-left: 8px;
          flex-shrink: 0;
        }

        .clear-button:hover {
          background: #ff3838;
        }

        .clear-button:disabled {
          background: #ccc;
          cursor: not-allowed;
        }

        .no-directory {
          color: #999;
          font-style: italic;
          background: white;
          border: 1px solid #ddd;
          border-radius: 4px;
          padding: 8px 12px;
          display: block;
          min-height: 40px;
          display: flex;
          align-items: center;
          box-sizing: border-box;
          flex: 1;
        }

        .directory-actions {
          display: flex;
          flex-direction: column;
          gap: 10px;
        }

        .select-button {
          background: #2c3e50;
          color: white;
          border: none;
          border-radius: 4px;
          padding: 10px 16px;
          cursor: pointer;
          font-size: 14px;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
          align-self: flex-start;
          min-width: 120px;
        }

        .select-button:hover {
          background: #34495e;
        }

        .select-button:disabled {
          background: #95a5a6;
          cursor: not-allowed;
        }

        .loading-spinner {
          width: 14px;
          height: 14px;
          border: 2px solid transparent;
          border-top: 2px solid #fff;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        .directory-info {
          margin-top: 5px;
        }

        .info-text {
          color: #666;
          font-size: 12px;
          display: flex;
          align-items: center;
          gap: 4px;
        }

        .error-message {
          background: #fff5f5;
          border: 1px solid #feb2b2;
          border-radius: 4px;
          padding: 10px 12px;
          color: #c53030;
          font-size: 14px;
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .error-icon {
          flex-shrink: 0;
        }

        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
          .directory-selector {
            background: #2d3748;
            border-color: #4a5568;
          }

          .directory-selector-header h3 {
            color: #e2e8f0;
          }

          .directory-selector-description {
            color: #a0aec0;
          }

          .directory-label {
            color: #cbd5e0;
          }

          .directory-path,
          .no-directory {
            background: #1a202c;
            border-color: #4a5568;
            color: #e2e8f0;
          }

          .path-text {
            color: #e2e8f0;
          }

          .select-button {
            background: #4299e1;
          }

          .select-button:hover {
            background: #3182ce;
          }

          .select-button:disabled {
            background: #718096;
          }

          .info-text {
            color: #a0aec0;
          }

          .error-message {
            background: #742a2a;
            border-color: #e53e3e;
            color: #fed7d7;
          }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
          .directory-selector {
            padding: 15px;
          }

          .directory-path {
            padding: 10px;
          }

          .path-text {
            font-size: 12px;
          }

          .select-button {
            width: 100%;
            justify-content: center;
          }
        }
      `}</style>
    </div>
  );
};