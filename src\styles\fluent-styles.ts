// Fluent UI样式系统
import { makeStyles, tokens, shorthands } from '@fluentui/react-components';

// 主布局样式
export const useMainLayoutStyles = makeStyles({
  root: {
    display: 'flex',
    flexDirection: 'column',
    height: '100vh',
    backgroundColor: tokens.colorNeutralBackground1,
    fontFamily: tokens.fontFamilyBase,
  },
  
  header: {
    ...shorthands.padding(tokens.spacingVerticalM, tokens.spacingHorizontalL),
    backgroundColor: tokens.colorNeutralBackground2,
    ...shorthands.borderBottom(tokens.strokeWidthThin, 'solid', tokens.colorNeutralStroke2),
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    boxShadow: tokens.shadow4,
  },
  
  headerTitle: {
    fontSize: tokens.fontSizeHero700,
    fontWeight: tokens.fontWeightSemibold,
    color: tokens.colorNeutralForeground1,
    margin: 0,
  },
  
  content: {
    flex: 1,
    display: 'flex',
    overflow: 'hidden',
    ...shorthands.gap(tokens.spacingHorizontalNone),
  },
  
  sidebar: {
    width: '350px',
    minWidth: '300px',
    maxWidth: '450px',
    backgroundColor: tokens.colorNeutralBackground3,
    ...shorthands.borderRight(tokens.strokeWidthThin, 'solid', tokens.colorNeutralStroke2),
    display: 'flex',
    flexDirection: 'column',
  },
  
  mainContent: {
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
    overflow: 'hidden',
    ...shorthands.padding(tokens.spacingVerticalL, tokens.spacingHorizontalL),
  },
});

// 配置列表样式
export const useConfigListStyles = makeStyles({
  container: {
    display: 'flex',
    flexDirection: 'column',
    height: '100%',
  },
  
  header: {
    ...shorthands.padding(tokens.spacingVerticalM, tokens.spacingHorizontalM),
    ...shorthands.borderBottom(tokens.strokeWidthThin, 'solid', tokens.colorNeutralStroke2),
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  
  headerTitle: {
    fontSize: tokens.fontSizeBase300,
    fontWeight: tokens.fontWeightSemibold,
    color: tokens.colorNeutralForeground1,
    margin: 0,
  },
  
  list: {
    flex: 1,
    overflow: 'auto',
    ...shorthands.padding(tokens.spacingVerticalS, tokens.spacingHorizontalNone),
  },
  
  listItem: {
    ...shorthands.margin(tokens.spacingVerticalXS, tokens.spacingHorizontalS),
    ...shorthands.padding(tokens.spacingVerticalS, tokens.spacingHorizontalM),
    backgroundColor: tokens.colorNeutralBackground1,
    ...shorthands.borderRadius(tokens.borderRadiusMedium),
    ...shorthands.border(tokens.strokeWidthThin, 'solid', tokens.colorNeutralStroke2),
    cursor: 'pointer',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    transition: `all ${tokens.durationNormal} ${tokens.curveEasyEase}`,
    
    ':hover': {
      backgroundColor: tokens.colorNeutralBackground1Hover,
      boxShadow: tokens.shadow4,
    },
    
    ':active': {
      backgroundColor: tokens.colorNeutralBackground1Pressed,
    },
  },
  
  listItemActive: {
    backgroundColor: tokens.colorBrandBackground2,
    ...shorthands.borderLeft(tokens.strokeWidthThick, 'solid', tokens.colorBrandBackground),
    
    ':hover': {
      backgroundColor: tokens.colorBrandBackground2Hover,
    },
  },
  
  listItemInvalid: {
    ...shorthands.borderLeft(tokens.strokeWidthThick, 'solid', tokens.colorPaletteRedBackground3),
    backgroundColor: tokens.colorPaletteRedBackground1,
  },
  
  itemContent: {
    display: 'flex',
    alignItems: 'center',
    ...shorthands.gap(tokens.spacingHorizontalS),
  },
  
  itemName: {
    fontSize: tokens.fontSizeBase200,
    fontWeight: tokens.fontWeightMedium,
    color: tokens.colorNeutralForeground1,
    fontFamily: tokens.fontFamilyMonospace,
  },
  
  itemActions: {
    display: 'flex',
    ...shorthands.gap(tokens.spacingHorizontalXS),
    opacity: 0,
    transition: `opacity ${tokens.durationNormal} ${tokens.curveEasyEase}`,
  },
  
  itemActionsVisible: {
    opacity: 1,
  },
});

// 编辑器样式
export const useEditorStyles = makeStyles({
  container: {
    display: 'flex',
    flexDirection: 'column',
    height: '100%',
    backgroundColor: tokens.colorNeutralBackground1,
    ...shorthands.borderRadius(tokens.borderRadiusLarge),
    ...shorthands.border(tokens.strokeWidthThin, 'solid', tokens.colorNeutralStroke2),
    boxShadow: tokens.shadow8,
    overflow: 'hidden',
  },
  
  header: {
    ...shorthands.padding(tokens.spacingVerticalM, tokens.spacingHorizontalL),
    backgroundColor: tokens.colorNeutralBackground2,
    ...shorthands.borderBottom(tokens.strokeWidthThin, 'solid', tokens.colorNeutralStroke2),
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  
  headerTitle: {
    fontSize: tokens.fontSizeBase300,
    fontWeight: tokens.fontWeightSemibold,
    color: tokens.colorNeutralForeground1,
    margin: 0,
  },
  
  editorArea: {
    flex: 1,
    ...shorthands.padding(tokens.spacingVerticalL),
    display: 'flex',
    flexDirection: 'column',
    ...shorthands.gap(tokens.spacingVerticalM),
  },
  
  textarea: {
    flex: 1,
    fontFamily: tokens.fontFamilyMonospace,
    fontSize: tokens.fontSizeBase200,
    ...shorthands.padding(tokens.spacingVerticalM, tokens.spacingHorizontalM),
    backgroundColor: tokens.colorNeutralBackground3,
    color: tokens.colorNeutralForeground1,
    ...shorthands.border(tokens.strokeWidthThin, 'solid', tokens.colorNeutralStroke2),
    ...shorthands.borderRadius(tokens.borderRadiusMedium),
    resize: 'none',
    outline: 'none',
    
    ':focus': {
      ...shorthands.borderColor(tokens.colorBrandStroke1),
      boxShadow: `0 0 0 2px ${tokens.colorBrandStroke2}`,
    },
  },
  
  actions: {
    display: 'flex',
    justifyContent: 'flex-end',
    ...shorthands.gap(tokens.spacingHorizontalS),
    ...shorthands.padding(tokens.spacingVerticalM, tokens.spacingHorizontalL),
    backgroundColor: tokens.colorNeutralBackground2,
    ...shorthands.borderTop(tokens.strokeWidthThin, 'solid', tokens.colorNeutralStroke2),
  },
});

// 状态指示器样式
export const useStatusStyles = makeStyles({
  valid: {
    color: tokens.colorPaletteGreenForeground2,
    fontSize: tokens.fontSizeBase100,
    fontWeight: tokens.fontWeightSemibold,
  },
  
  invalid: {
    color: tokens.colorPaletteRedForeground2,
    fontSize: tokens.fontSizeBase100,
    fontWeight: tokens.fontWeightSemibold,
  },
  
  warning: {
    color: tokens.colorPaletteYellowForeground2,
    fontSize: tokens.fontSizeBase100,
    fontWeight: tokens.fontWeightSemibold,
  },
});

// 欢迎页面样式
export const useWelcomeStyles = makeStyles({
  container: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    height: '100%',
    backgroundColor: tokens.colorNeutralBackground1,
    ...shorthands.borderRadius(tokens.borderRadiusLarge),
    ...shorthands.border(tokens.strokeWidthThin, 'solid', tokens.colorNeutralStroke2),
  },
  
  content: {
    textAlign: 'center',
    maxWidth: '400px',
    ...shorthands.padding(tokens.spacingVerticalXXL, tokens.spacingHorizontalXL),
  },
  
  icon: {
    fontSize: '64px',
    color: tokens.colorNeutralForeground3,
    marginBottom: tokens.spacingVerticalL,
  },
  
  title: {
    fontSize: tokens.fontSizeHero800,
    fontWeight: tokens.fontWeightSemibold,
    color: tokens.colorNeutralForeground1,
    marginBottom: tokens.spacingVerticalM,
  },
  
  description: {
    fontSize: tokens.fontSizeBase300,
    color: tokens.colorNeutralForeground2,
    lineHeight: tokens.lineHeightBase300,
    marginBottom: tokens.spacingVerticalXL,
  },
});

// 响应式样式
export const useResponsiveStyles = makeStyles({
  // 平板视图
  tablet: {
    '@media (max-width: 1024px)': {
      flexDirection: 'column',
    },
  },
  
  tabletSidebar: {
    '@media (max-width: 1024px)': {
      width: '100%',
      height: '300px',
      borderRight: 'none',
      ...shorthands.borderBottom(tokens.strokeWidthThin, 'solid', tokens.colorNeutralStroke2),
    },
  },
  
  // 手机视图
  mobile: {
    '@media (max-width: 768px)': {
      ...shorthands.padding(tokens.spacingVerticalS, tokens.spacingHorizontalS),
    },
  },
  
  mobileHeader: {
    '@media (max-width: 768px)': {
      ...shorthands.padding(tokens.spacingVerticalS, tokens.spacingHorizontalM),
      flexDirection: 'column',
      ...shorthands.gap(tokens.spacingVerticalS),
    },
  },
});