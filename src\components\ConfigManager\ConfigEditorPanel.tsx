import React from 'react';
import { Text } from '@fluentui/react-components';
import { EditorContainer } from '../EditorContainer';
import { ValidationResult } from '../../types/config';

interface ConfigEditorPanelProps {
  selectedConfigName: string | null;
  currentConfig: {
    name: string;
    content: string;
    isModified: boolean;
    validationResult: ValidationResult | null;
    sessionId?: string;
    loadedAt?: number;
  } | null;
  isSaving: boolean;
  onSave: (content: string) => Promise<void>;
  onContentChange: (fileName: string, hasChanges: boolean) => void;
}

/**
 * 配置编辑器面板组件
 * 负责显示和编辑配置文件内容
 */
export const ConfigEditorPanel: React.FC<ConfigEditorPanelProps> = ({
  selectedConfigName,
  currentConfig,
  isSaving,
  onSave,
  onContentChange,
}) => {
  // 如果没有选中配置，显示提示信息
  if (!selectedConfigName || !currentConfig) {
    return (
      <div style={{
        height: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: 'var(--colorNeutralForeground3)',
        fontSize: '16px',
        textAlign: 'center'
      }}>
        <Text size={400}>请从左侧选择一个配置文件进行编辑</Text>
      </div>
    );
  }

  return (
    <EditorContainer
      fileName={selectedConfigName}
      fileContent={currentConfig.content || '{}'}
      onSave={onSave}
      onContentChange={(_, hasChanges) => {
        if (selectedConfigName) {
          onContentChange(selectedConfigName, hasChanges);
        }
      }}
      isLoading={isSaving}
    />
  );
};