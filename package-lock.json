{"name": "claude-switch", "version": "0.1.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "claude-switch", "version": "0.1.0", "dependencies": {"@fluentui/react-components": "^9.54.0", "@fluentui/react-icons": "^2.0.239", "@monaco-editor/react": "^4.7.0", "@tauri-apps/api": "^2", "@tauri-apps/plugin-dialog": "^2", "@tauri-apps/plugin-fs": "^2", "@tauri-apps/plugin-opener": "^2", "react": "^18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"@tauri-apps/cli": "^2", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "monaco-editor": "^0.52.2", "typescript": "~5.6.2", "vite": "^6.0.3"}}, "node_modules/@ampproject/remapping": {"version": "2.3.0", "resolved": "https://registry.npmmirror.com/@ampproject/remapping/-/remapping-2.3.0.tgz", "integrity": "sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==", "dev": true, "license": "Apache-2.0", "dependencies": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.24"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@babel/code-frame": {"version": "7.27.1", "resolved": "https://registry.npmmirror.com/@babel/code-frame/-/code-frame-7.27.1.tgz", "integrity": "sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/compat-data": {"version": "7.28.0", "resolved": "https://registry.npmmirror.com/@babel/compat-data/-/compat-data-7.28.0.tgz", "integrity": "sha512-60X7qkglvrap8mn1lh2ebxXdZYtUcpd7gsmy9kLaBJ4i/WdY8PqTSdxyA8qraikqKQK5C1KRBKXqznrVapyNaw==", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/core": {"version": "7.28.0", "resolved": "https://registry.npmmirror.com/@babel/core/-/core-7.28.0.tgz", "integrity": "sha512-UlLAnTPrFdNGoFtbSXwcGFQBtQZJCNjaN6hQNP3UPvuNXT1i82N26KL3dZeIpNalWywr9IuQuncaAfUaS1g6sQ==", "dev": true, "license": "MIT", "dependencies": {"@ampproject/remapping": "^2.2.0", "@babel/code-frame": "^7.27.1", "@babel/generator": "^7.28.0", "@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-module-transforms": "^7.27.3", "@babel/helpers": "^7.27.6", "@babel/parser": "^7.28.0", "@babel/template": "^7.27.2", "@babel/traverse": "^7.28.0", "@babel/types": "^7.28.0", "convert-source-map": "^2.0.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.2", "json5": "^2.2.3", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/babel"}}, "node_modules/@babel/generator": {"version": "7.28.0", "resolved": "https://registry.npmmirror.com/@babel/generator/-/generator-7.28.0.tgz", "integrity": "sha512-lJjzvrbEeWrhB4P3QBsH7tey117PjLZnDbLiQEKjQ/fNJTjuq4HSqgFA+UNSwZT8D7dxxbnuSBMsa1lrWzKlQg==", "dev": true, "license": "MIT", "dependencies": {"@babel/parser": "^7.28.0", "@babel/types": "^7.28.0", "@jridgewell/gen-mapping": "^0.3.12", "@jridgewell/trace-mapping": "^0.3.28", "jsesc": "^3.0.2"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-compilation-targets": {"version": "7.27.2", "resolved": "https://registry.npmmirror.com/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz", "integrity": "sha512-2+1thGUUWWjLTYTHZWK1n8Yga0ijBz1XAhUXcKy81rd5g6yh7hGqMp45v7cadSbEHc9G3OTv45SyneRN3ps4DQ==", "dev": true, "license": "MIT", "dependencies": {"@babel/compat-data": "^7.27.2", "@babel/helper-validator-option": "^7.27.1", "browserslist": "^4.24.0", "lru-cache": "^5.1.1", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-globals": {"version": "7.28.0", "resolved": "https://registry.npmmirror.com/@babel/helper-globals/-/helper-globals-7.28.0.tgz", "integrity": "sha512-+W6cISkXFa1jXsDEdYA8HeevQT/FULhxzR99pxphltZcVaugps53THCeiWA8SguxxpSp3gKPiuYfSWopkLQ4hw==", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-imports": {"version": "7.27.1", "resolved": "https://registry.npmmirror.com/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz", "integrity": "sha512-0gSFWUPNXNopqtIPQvlD5WgXYI5GY2kP2cCvoT8kczjbfcfuIljTbcWrulD1CIPIX2gt1wghbDy08yE1p+/r3w==", "dev": true, "license": "MIT", "dependencies": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-transforms": {"version": "7.27.3", "resolved": "https://registry.npmmirror.com/@babel/helper-module-transforms/-/helper-module-transforms-7.27.3.tgz", "integrity": "sha512-dSOvYwvyLsWBeIRyOeHXp5vPj5l1I011r52FM1+r1jCERv+aFXYk4whgQccYEGYxK2H3ZAIA8nuPkQ0HaUo3qg==", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1", "@babel/traverse": "^7.27.3"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://registry.npmmirror.com/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-1gn1Up5YXka3YYAHGKpbideQ5Yjf1tDa9qYcgysz+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw==", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-string-parser": {"version": "7.27.1", "resolved": "https://registry.npmmirror.com/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz", "integrity": "sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA==", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-identifier": {"version": "7.27.1", "resolved": "https://registry.npmmirror.com/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz", "integrity": "sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow==", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-option": {"version": "7.27.1", "resolved": "https://registry.npmmirror.com/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz", "integrity": "sha512-YvjJow9FxbhFFKDSuFnVCe2WxXk1zWc22fFePVNEaWJEu8IrZVlda6N0uHwzZrUM1il7NC9Mlp4MaJYbYd9JSg==", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helpers": {"version": "7.28.2", "resolved": "https://registry.npmmirror.com/@babel/helpers/-/helpers-7.28.2.tgz", "integrity": "sha512-/V9771t+EgXz62aCcyofnQhGM8DQACbRhvzKFsXKC9QM+5MadF8ZmIm0crDMaz3+o0h0zXfJnd4EhbYbxsrcFw==", "dev": true, "license": "MIT", "dependencies": {"@babel/template": "^7.27.2", "@babel/types": "^7.28.2"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/parser": {"version": "7.28.0", "resolved": "https://registry.npmmirror.com/@babel/parser/-/parser-7.28.0.tgz", "integrity": "sha512-jVZGvOxOuNSsuQuLRTh13nU0AogFlw32w/MT+LV6D3sP5WdbW61E77RnkbaO2dUvmPAYrBDJXGn5gGS6tH4j8g==", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.28.0"}, "bin": {"parser": "bin/babel-parser.js"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@babel/plugin-transform-react-jsx-self": {"version": "7.27.1", "resolved": "https://registry.npmmirror.com/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.27.1.tgz", "integrity": "sha512-6UzkCs+ejGdZ5mFFC/OCUrv028ab2fp1znZmCZjAOBKiBK2jXD1O+BPSfX8X2qjJ75fZBMSnQn3Rq2mrBJK2mw==", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-react-jsx-source": {"version": "7.27.1", "resolved": "https://registry.npmmirror.com/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.27.1.tgz", "integrity": "sha512-zbwoTsBruTeKB9hSq73ha66iFeJHuaFkUbwvqElnygoNbj/jHRsSeokowZFN3CZ64IvEqcmmkVe89OPXc7ldAw==", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/runtime": {"version": "7.28.2", "resolved": "https://registry.npmmirror.com/@babel/runtime/-/runtime-7.28.2.tgz", "integrity": "sha512-KHp2IflsnGywDjBWDkR9iEqiWSpc8GIi0lgTT3mOElT0PP1tG26P4tmFI2YvAdzgq9RGyoHZQEIEdZy6Ec5xCA==", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/template": {"version": "7.27.2", "resolved": "https://registry.npmmirror.com/@babel/template/-/template-7.27.2.tgz", "integrity": "sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw==", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/parser": "^7.27.2", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/traverse": {"version": "7.28.0", "resolved": "https://registry.npmmirror.com/@babel/traverse/-/traverse-7.28.0.tgz", "integrity": "sha512-mGe7UK5wWyh0bKRfupsUchrQGqvDbZDbKJw+kcRGSmdHVYrv+ltd0pnpDTVpiTqnaBru9iEvA8pz8W46v0Amwg==", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.28.0", "@babel/helper-globals": "^7.28.0", "@babel/parser": "^7.28.0", "@babel/template": "^7.27.2", "@babel/types": "^7.28.0", "debug": "^4.3.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/types": {"version": "7.28.2", "resolved": "https://registry.npmmirror.com/@babel/types/-/types-7.28.2.tgz", "integrity": "sha512-ruv7Ae4J5dUYULmeXw1gmb7rYRz57OWCPM57pHojnLq/3Z1CK2lNSLTCVjxVk1F/TZHwOZZrOWi0ur95BbLxNQ==", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@ctrl/tinycolor": {"version": "3.6.1", "resolved": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-3.6.1.tgz", "integrity": "sha512-SITSV6aIXsuVNV3f3O0f2n/cgyEDWoSqtZMYiAmcsYHydcKrOz3gUxB/iXd/Qf08+IZX4KpgNbvUdMBmWz+kcA==", "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/@emotion/hash": {"version": "0.9.2", "resolved": "https://registry.npmmirror.com/@emotion/hash/-/hash-0.9.2.tgz", "integrity": "sha512-MyqliTZGuOm3+5ZRSaaBGP3USLw6+EGykkwZns2EPC5g8jJ4z9OrdZY9apkl3+UP9+sdz76YYkwCKP5gh8iY3g==", "license": "MIT"}, "node_modules/@esbuild/aix-ppc64": {"version": "0.25.8", "resolved": "https://registry.npmmirror.com/@esbuild/aix-ppc64/-/aix-ppc64-0.25.8.tgz", "integrity": "sha512-urAvrUedIqEiFR3FYSLTWQgLu5tb+m0qZw0NBEasUeo6wuqatkMDaRT+1uABiGXEu5vqgPd7FGE1BhsAIy9QVA==", "cpu": ["ppc64"], "dev": true, "license": "MIT", "optional": true, "os": ["aix"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/android-arm": {"version": "0.25.8", "resolved": "https://registry.npmmirror.com/@esbuild/android-arm/-/android-arm-0.25.8.tgz", "integrity": "sha512-RONsAvGCz5oWyePVnLdZY/HHwA++nxYWIX1atInlaW6SEkwq6XkP3+cb825EUcRs5Vss/lGh/2YxAb5xqc07Uw==", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/android-arm64": {"version": "0.25.8", "resolved": "https://registry.npmmirror.com/@esbuild/android-arm64/-/android-arm64-0.25.8.tgz", "integrity": "sha512-OD3p7LYzWpLhZEyATcTSJ67qB5D+20vbtr6vHlHWSQYhKtzUYrETuWThmzFpZtFsBIxRvhO07+UgVA9m0i/O1w==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/android-x64": {"version": "0.25.8", "resolved": "https://registry.npmmirror.com/@esbuild/android-x64/-/android-x64-0.25.8.tgz", "integrity": "sha512-yJAVPklM5+4+9dTeKwHOaA+LQkmrKFX96BM0A/2zQrbS6ENCmxc4OVoBs5dPkCCak2roAD+jKCdnmOqKszPkjA==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/darwin-arm64": {"version": "0.25.8", "resolved": "https://registry.npmmirror.com/@esbuild/darwin-arm64/-/darwin-arm64-0.25.8.tgz", "integrity": "sha512-Jw0mxgIaYX6R8ODrdkLLPwBqHTtYHJSmzzd+QeytSugzQ0Vg4c5rDky5VgkoowbZQahCbsv1rT1KW72MPIkevw==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/darwin-x64": {"version": "0.25.8", "resolved": "https://registry.npmmirror.com/@esbuild/darwin-x64/-/darwin-x64-0.25.8.tgz", "integrity": "sha512-Vh2gLxxHnuoQ+GjPNvDSDRpoBCUzY4Pu0kBqMBDlK4fuWbKgGtmDIeEC081xi26PPjn+1tct+Bh8FjyLlw1Zlg==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/freebsd-arm64": {"version": "0.25.8", "resolved": "https://registry.npmmirror.com/@esbuild/freebsd-arm64/-/freebsd-arm64-0.25.8.tgz", "integrity": "sha512-YPJ7hDQ9DnNe5vxOm6jaie9QsTwcKedPvizTVlqWG9GBSq+BuyWEDazlGaDTC5NGU4QJd666V0yqCBL2oWKPfA==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["freebsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/freebsd-x64": {"version": "0.25.8", "resolved": "https://registry.npmmirror.com/@esbuild/freebsd-x64/-/freebsd-x64-0.25.8.tgz", "integrity": "sha512-MmaEXxQRdXNFsRN/KcIimLnSJrk2r5H8v+WVafRWz5xdSVmWLoITZQXcgehI2ZE6gioE6HirAEToM/RvFBeuhw==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["freebsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-arm": {"version": "0.25.8", "resolved": "https://registry.npmmirror.com/@esbuild/linux-arm/-/linux-arm-0.25.8.tgz", "integrity": "sha512-FuzEP9BixzZohl1kLf76KEVOsxtIBFwCaLupVuk4eFVnOZfU+Wsn+x5Ryam7nILV2pkq2TqQM9EZPsOBuMC+kg==", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-arm64": {"version": "0.25.8", "resolved": "https://registry.npmmirror.com/@esbuild/linux-arm64/-/linux-arm64-0.25.8.tgz", "integrity": "sha512-WIgg00ARWv/uYLU7lsuDK00d/hHSfES5BzdWAdAig1ioV5kaFNrtK8EqGcUBJhYqotlUByUKz5Qo6u8tt7iD/w==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-ia32": {"version": "0.25.8", "resolved": "https://registry.npmmirror.com/@esbuild/linux-ia32/-/linux-ia32-0.25.8.tgz", "integrity": "sha512-A1D9YzRX1i+1AJZuFFUMP1E9fMaYY+GnSQil9Tlw05utlE86EKTUA7RjwHDkEitmLYiFsRd9HwKBPEftNdBfjg==", "cpu": ["ia32"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-loong64": {"version": "0.25.8", "resolved": "https://registry.npmmirror.com/@esbuild/linux-loong64/-/linux-loong64-0.25.8.tgz", "integrity": "sha512-O7k1J/dwHkY1RMVvglFHl1HzutGEFFZ3kNiDMSOyUrB7WcoHGf96Sh+64nTRT26l3GMbCW01Ekh/ThKM5iI7hQ==", "cpu": ["loong64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-mips64el": {"version": "0.25.8", "resolved": "https://registry.npmmirror.com/@esbuild/linux-mips64el/-/linux-mips64el-0.25.8.tgz", "integrity": "sha512-uv+dqfRazte3BzfMp8PAQXmdGHQt2oC/y2ovwpTteqrMx2lwaksiFZ/bdkXJC19ttTvNXBuWH53zy/aTj1FgGw==", "cpu": ["mips64el"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-ppc64": {"version": "0.25.8", "resolved": "https://registry.npmmirror.com/@esbuild/linux-ppc64/-/linux-ppc64-0.25.8.tgz", "integrity": "sha512-GyG0KcMi1GBavP5JgAkkstMGyMholMDybAf8wF5A70CALlDM2p/f7YFE7H92eDeH/VBtFJA5MT4nRPDGg4JuzQ==", "cpu": ["ppc64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-riscv64": {"version": "0.25.8", "resolved": "https://registry.npmmirror.com/@esbuild/linux-riscv64/-/linux-riscv64-0.25.8.tgz", "integrity": "sha512-rAqDYFv3yzMrq7GIcen3XP7TUEG/4LK86LUPMIz6RT8A6pRIDn0sDcvjudVZBiiTcZCY9y2SgYX2lgK3AF+1eg==", "cpu": ["riscv64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-s390x": {"version": "0.25.8", "resolved": "https://registry.npmmirror.com/@esbuild/linux-s390x/-/linux-s390x-0.25.8.tgz", "integrity": "sha512-Xutvh6VjlbcHpsIIbwY8GVRbwoviWT19tFhgdA7DlenLGC/mbc3lBoVb7jxj9Z+eyGqvcnSyIltYUrkKzWqSvg==", "cpu": ["s390x"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-x64": {"version": "0.25.8", "resolved": "https://registry.npmmirror.com/@esbuild/linux-x64/-/linux-x64-0.25.8.tgz", "integrity": "sha512-ASFQhgY4ElXh3nDcOMTkQero4b1lgubskNlhIfJrsH5OKZXDpUAKBlNS0Kx81jwOBp+HCeZqmoJuihTv57/jvQ==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/netbsd-arm64": {"version": "0.25.8", "resolved": "https://registry.npmmirror.com/@esbuild/netbsd-arm64/-/netbsd-arm64-0.25.8.tgz", "integrity": "sha512-d1KfruIeohqAi6SA+gENMuObDbEjn22olAR7egqnkCD9DGBG0wsEARotkLgXDu6c4ncgWTZJtN5vcgxzWRMzcw==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["netbsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/netbsd-x64": {"version": "0.25.8", "resolved": "https://registry.npmmirror.com/@esbuild/netbsd-x64/-/netbsd-x64-0.25.8.tgz", "integrity": "sha512-nVDCkrvx2ua+XQNyfrujIG38+YGyuy2Ru9kKVNyh5jAys6n+l44tTtToqHjino2My8VAY6Lw9H7RI73XFi66Cg==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["netbsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/openbsd-arm64": {"version": "0.25.8", "resolved": "https://registry.npmmirror.com/@esbuild/openbsd-arm64/-/openbsd-arm64-0.25.8.tgz", "integrity": "sha512-j8HgrDuSJFAujkivSMSfPQSAa5Fxbvk4rgNAS5i3K+r8s1X0p1uOO2Hl2xNsGFppOeHOLAVgYwDVlmxhq5h+SQ==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["openbsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/openbsd-x64": {"version": "0.25.8", "resolved": "https://registry.npmmirror.com/@esbuild/openbsd-x64/-/openbsd-x64-0.25.8.tgz", "integrity": "sha512-1h8MUAwa0VhNCDp6Af0HToI2TJFAn1uqT9Al6DJVzdIBAd21m/G0Yfc77KDM3uF3T/YaOgQq3qTJHPbTOInaIQ==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["openbsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/openharmony-arm64": {"version": "0.25.8", "resolved": "https://registry.npmmirror.com/@esbuild/openharmony-arm64/-/openharmony-arm64-0.25.8.tgz", "integrity": "sha512-r2nVa5SIK9tSWd0kJd9HCffnDHKchTGikb//9c7HX+r+wHYCpQrSgxhlY6KWV1nFo1l4KFbsMlHk+L6fekLsUg==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["openharmony"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/sunos-x64": {"version": "0.25.8", "resolved": "https://registry.npmmirror.com/@esbuild/sunos-x64/-/sunos-x64-0.25.8.tgz", "integrity": "sha512-zUlaP2S12YhQ2UzUfcCuMDHQFJyKABkAjvO5YSndMiIkMimPmxA+BYSBikWgsRpvyxuRnow4nS5NPnf9fpv41w==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["sunos"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/win32-arm64": {"version": "0.25.8", "resolved": "https://registry.npmmirror.com/@esbuild/win32-arm64/-/win32-arm64-0.25.8.tgz", "integrity": "sha512-YEGFFWESlPva8hGL+zvj2z/SaK+pH0SwOM0Nc/d+rVnW7GSTFlLBGzZkuSU9kFIGIo8q9X3ucpZhu8PDN5A2sQ==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/win32-ia32": {"version": "0.25.8", "resolved": "https://registry.npmmirror.com/@esbuild/win32-ia32/-/win32-ia32-0.25.8.tgz", "integrity": "sha512-hiGgGC6KZ5LZz58OL/+qVVoZiuZlUYlYHNAmczOm7bs2oE1XriPFi5ZHHrS8ACpV5EjySrnoCKmcbQMN+ojnHg==", "cpu": ["ia32"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/win32-x64": {"version": "0.25.8", "resolved": "https://registry.npmmirror.com/@esbuild/win32-x64/-/win32-x64-0.25.8.tgz", "integrity": "sha512-cn3Yr7+OaaZq1c+2pe+8yxC8E144SReCQjN6/2ynubzYjvyqZjTXfQJpAcQpsdJq3My7XADANiYGHoFC69pLQw==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=18"}}, "node_modules/@floating-ui/core": {"version": "1.7.3", "resolved": "https://registry.npmmirror.com/@floating-ui/core/-/core-1.7.3.tgz", "integrity": "sha512-sGnvb5dmrJaKEZ+LDIpguvdX3bDlEllmv4/ClQ9awcmCZrlx5jQyyMWFM5kBI+EyNOCDDiKk8il0zeuX3Zlg/w==", "license": "MIT", "dependencies": {"@floating-ui/utils": "^0.2.10"}}, "node_modules/@floating-ui/devtools": {"version": "0.2.1", "resolved": "https://registry.npmmirror.com/@floating-ui/devtools/-/devtools-0.2.1.tgz", "integrity": "sha512-8PHJLbD6VhBh+LJ1uty/Bz30qs02NXCE5u8WpOhSewlYXUWl03GNXknr9AS2yaAWJEQaY27x7eByJs44gODBcw==", "peerDependencies": {"@floating-ui/dom": ">=1.5.4"}}, "node_modules/@floating-ui/dom": {"version": "1.7.3", "resolved": "https://registry.npmmirror.com/@floating-ui/dom/-/dom-1.7.3.tgz", "integrity": "sha512-uZA413QEpNuhtb3/iIKoYMSK07keHPYeXF02Zhd6e213j+d1NamLix/mCLxBUDW/Gx52sPH2m+chlUsyaBs/Ag==", "license": "MIT", "dependencies": {"@floating-ui/core": "^1.7.3", "@floating-ui/utils": "^0.2.10"}}, "node_modules/@floating-ui/utils": {"version": "0.2.10", "resolved": "https://registry.npmmirror.com/@floating-ui/utils/-/utils-0.2.10.tgz", "integrity": "sha512-aGTxbpbg8/b5JfU1HXSrbH3wXZuLPJcNEcZQFMxLs3oSzgtVu6nFPkbbGGUvBcUjKV2YyB9Wxxabo+HEH9tcRQ==", "license": "MIT"}, "node_modules/@fluentui/keyboard-keys": {"version": "9.0.8", "resolved": "https://registry.npmmirror.com/@fluentui/keyboard-keys/-/keyboard-keys-9.0.8.tgz", "integrity": "sha512-iUSJUUHAyTosnXK8O2Ilbfxma+ZyZPMua5vB028Ys96z80v+LFwntoehlFsdH3rMuPsA8GaC1RE7LMezwPBPdw==", "license": "MIT", "dependencies": {"@swc/helpers": "^0.5.1"}}, "node_modules/@fluentui/priority-overflow": {"version": "9.1.15", "resolved": "https://registry.npmmirror.com/@fluentui/priority-overflow/-/priority-overflow-9.1.15.tgz", "integrity": "sha512-/3jPBBq64hRdA416grVj+ZeMBUIaKZk2S5HiRg7CKCAV1JuyF84Do0rQI6ns8Vb9XOGuc4kurMcL/UEftoEVrg==", "license": "MIT", "dependencies": {"@swc/helpers": "^0.5.1"}}, "node_modules/@fluentui/react-accordion": {"version": "9.8.3", "resolved": "https://registry.npmmirror.com/@fluentui/react-accordion/-/react-accordion-9.8.3.tgz", "integrity": "sha512-NEmiF+l/V9ToWwrruursX/xWwPSskkU9S+1sjCN/F/rNgPtlDEyFmVSuGPpKddmUIi1YEJuozqedEx2ybsru4w==", "license": "MIT", "dependencies": {"@fluentui/react-aria": "^9.16.2", "@fluentui/react-context-selector": "^9.2.4", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-motion": "^9.10.1", "@fluentui/react-motion-components-preview": "^0.8.1", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-tabster": "^9.26.2", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-accordion/node_modules/@fluentui/react-context-selector": {"version": "9.2.4", "resolved": "https://registry.npmmirror.com/@fluentui/react-context-selector/-/react-context-selector-9.2.4.tgz", "integrity": "sha512-4QJjaBWoJSW+Vu6XRT0A9j3h2RGdXNUj9ur6ljdXwG13DoE69v03cDVF8GT3jiXWO3veNJntelI0BS99Qb5wzQ==", "license": "MIT", "dependencies": {"@fluentui/react-utilities": "^9.23.1", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": ">=0.19.0 <=0.23.0"}}, "node_modules/@fluentui/react-accordion/node_modules/scheduler": {"version": "0.23.0", "resolved": "https://registry.npmmirror.com/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "license": "MIT", "peer": true, "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/@fluentui/react-alert": {"version": "9.0.0-beta.124", "resolved": "https://registry.npmmirror.com/@fluentui/react-alert/-/react-alert-9.0.0-beta.124.tgz", "integrity": "sha512-yFBo3B5H9hnoaXxlkuz8wRz04DEyQ+ElYA/p5p+Vojf19Zuta8DmFZZ6JtWdtxcdnnQ4LvAfC5OYYlzdReozPA==", "license": "MIT", "dependencies": {"@fluentui/react-avatar": "^9.6.29", "@fluentui/react-button": "^9.3.83", "@fluentui/react-icons": "^2.0.239", "@fluentui/react-jsx-runtime": "^9.0.39", "@fluentui/react-tabster": "^9.21.5", "@fluentui/react-theme": "^9.1.19", "@fluentui/react-utilities": "^9.18.10", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-aria": {"version": "9.16.2", "resolved": "https://registry.npmmirror.com/@fluentui/react-aria/-/react-aria-9.16.2.tgz", "integrity": "sha512-7ly8FJPG7Ra/oNmN1S9RNr4YiuKGTK5vkSUS6ojbyY2BCBiU96SQWDyHRmge4JKuqHUeioRumqrnQcxmmT/Skw==", "license": "MIT", "dependencies": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-tabster": "^9.26.2", "@fluentui/react-utilities": "^9.23.1", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-avatar": {"version": "9.9.3", "resolved": "https://registry.npmmirror.com/@fluentui/react-avatar/-/react-avatar-9.9.3.tgz", "integrity": "sha512-0Z0ueMrtneioQbg4MOFQ3aYwhEsDyNApj1zaoPVd6vj4YLU3NgNp2ryL5HgEE5TtjYltrTXcRVqid8RDchMXGw==", "license": "MIT", "dependencies": {"@fluentui/react-badge": "^9.4.2", "@fluentui/react-context-selector": "^9.2.4", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-popover": "^9.12.3", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-tabster": "^9.26.2", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-tooltip": "^9.8.2", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-avatar/node_modules/@fluentui/react-context-selector": {"version": "9.2.4", "resolved": "https://registry.npmmirror.com/@fluentui/react-context-selector/-/react-context-selector-9.2.4.tgz", "integrity": "sha512-4QJjaBWoJSW+Vu6XRT0A9j3h2RGdXNUj9ur6ljdXwG13DoE69v03cDVF8GT3jiXWO3veNJntelI0BS99Qb5wzQ==", "license": "MIT", "dependencies": {"@fluentui/react-utilities": "^9.23.1", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": ">=0.19.0 <=0.23.0"}}, "node_modules/@fluentui/react-avatar/node_modules/scheduler": {"version": "0.23.0", "resolved": "https://registry.npmmirror.com/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "license": "MIT", "peer": true, "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/@fluentui/react-badge": {"version": "9.4.2", "resolved": "https://registry.npmmirror.com/@fluentui/react-badge/-/react-badge-9.4.2.tgz", "integrity": "sha512-jCTHsfhIY5bHsvnw/uuhzlDQl4I//trEJTR7wgBhxWq3T50LOe0wCCBbh7ik+YAVregoXk59Y7J6oet1oJOhOA==", "license": "MIT", "dependencies": {"@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-breadcrumb": {"version": "9.3.3", "resolved": "https://registry.npmmirror.com/@fluentui/react-breadcrumb/-/react-breadcrumb-9.3.3.tgz", "integrity": "sha512-RAa16tcJi/dBLS/+qn2AO/PvnUpVQjLCfUmBiUQaGeJ6uo2qQ7bbGjSVIbdvC2yo0pZ4PhNRPyLcFdT3w4RpMw==", "license": "MIT", "dependencies": {"@fluentui/react-aria": "^9.16.2", "@fluentui/react-button": "^9.6.3", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-link": "^9.6.2", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-tabster": "^9.26.2", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-button": {"version": "9.6.3", "resolved": "https://registry.npmmirror.com/@fluentui/react-button/-/react-button-9.6.3.tgz", "integrity": "sha512-+x8cNTjKPoQvu+paZ1wjWc+oOjlcFSJLhIHmaxIrtK3FE/93WQwk7rwmKDJ7QF1Jf7ghiUsmgRfDGfuNe1IQbw==", "license": "MIT", "dependencies": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-aria": "^9.16.2", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-tabster": "^9.26.2", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-card": {"version": "9.4.3", "resolved": "https://registry.npmmirror.com/@fluentui/react-card/-/react-card-9.4.3.tgz", "integrity": "sha512-jt5Xjnl00O1vXNH/Zlrr5dehmHHX4wllFzVv4CyctW1X97U6ruyaow3RTwLXhkPV0chPJo7mTfItQrnmKtyKnw==", "license": "MIT", "dependencies": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-tabster": "^9.26.2", "@fluentui/react-text": "^9.6.2", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-carousel": {"version": "9.8.3", "resolved": "https://registry.npmmirror.com/@fluentui/react-carousel/-/react-carousel-9.8.3.tgz", "integrity": "sha512-etmp7bCzYDCRFk5S3lV56aZN2iyH79O8vYylsKVAYOQzFZvY+Xcmgiyj3i5h5wJV2fJKwBp6Vi64ClHURwz0XA==", "license": "MIT", "dependencies": {"@fluentui/react-aria": "^9.16.2", "@fluentui/react-button": "^9.6.3", "@fluentui/react-context-selector": "^9.2.4", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-tabster": "^9.26.2", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-tooltip": "^9.8.2", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1", "embla-carousel": "^8.5.1", "embla-carousel-autoplay": "^8.5.1", "embla-carousel-fade": "^8.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-carousel/node_modules/@fluentui/react-context-selector": {"version": "9.2.4", "resolved": "https://registry.npmmirror.com/@fluentui/react-context-selector/-/react-context-selector-9.2.4.tgz", "integrity": "sha512-4QJjaBWoJSW+Vu6XRT0A9j3h2RGdXNUj9ur6ljdXwG13DoE69v03cDVF8GT3jiXWO3veNJntelI0BS99Qb5wzQ==", "license": "MIT", "dependencies": {"@fluentui/react-utilities": "^9.23.1", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": ">=0.19.0 <=0.23.0"}}, "node_modules/@fluentui/react-carousel/node_modules/scheduler": {"version": "0.23.0", "resolved": "https://registry.npmmirror.com/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "license": "MIT", "peer": true, "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/@fluentui/react-checkbox": {"version": "9.5.2", "resolved": "https://registry.npmmirror.com/@fluentui/react-checkbox/-/react-checkbox-9.5.2.tgz", "integrity": "sha512-xb6apCPe2hHLIAdmZ8oHu6FQkc/tNG/MH69DvJ0r7muHCD/MqQUjsvi00BJcmLuPOCsbfIL9kzYtraGY4oLhMA==", "license": "MIT", "dependencies": {"@fluentui/react-field": "^9.4.2", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-label": "^9.3.2", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-tabster": "^9.26.2", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-color-picker": {"version": "9.2.2", "resolved": "https://registry.npmmirror.com/@fluentui/react-color-picker/-/react-color-picker-9.2.2.tgz", "integrity": "sha512-STR94ApqxR+nv6AS+9OX5gDd28fgl766deTz+9AodxI50UfEv+aWwL5ihiPSOzjc6ypTkdWZSV5s3Jr4w6iM5g==", "license": "MIT", "dependencies": {"@ctrl/tinycolor": "^3.3.4", "@fluentui/react-context-selector": "^9.2.4", "@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-tabster": "^9.26.2", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-color-picker/node_modules/@fluentui/react-context-selector": {"version": "9.2.4", "resolved": "https://registry.npmmirror.com/@fluentui/react-context-selector/-/react-context-selector-9.2.4.tgz", "integrity": "sha512-4QJjaBWoJSW+Vu6XRT0A9j3h2RGdXNUj9ur6ljdXwG13DoE69v03cDVF8GT3jiXWO3veNJntelI0BS99Qb5wzQ==", "license": "MIT", "dependencies": {"@fluentui/react-utilities": "^9.23.1", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": ">=0.19.0 <=0.23.0"}}, "node_modules/@fluentui/react-color-picker/node_modules/scheduler": {"version": "0.23.0", "resolved": "https://registry.npmmirror.com/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "license": "MIT", "peer": true, "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/@fluentui/react-combobox": {"version": "9.16.3", "resolved": "https://registry.npmmirror.com/@fluentui/react-combobox/-/react-combobox-9.16.3.tgz", "integrity": "sha512-H1pR687K99iSpXInI0IyRt4IoC0F7VJvv69rmOX4/193jKX1CXJLNK9yEwReArLP+NJAC949TB5iMFTXIncqXg==", "license": "MIT", "dependencies": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-aria": "^9.16.2", "@fluentui/react-context-selector": "^9.2.4", "@fluentui/react-field": "^9.4.2", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-portal": "^9.7.2", "@fluentui/react-positioning": "^9.20.2", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-tabster": "^9.26.2", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-combobox/node_modules/@fluentui/react-context-selector": {"version": "9.2.4", "resolved": "https://registry.npmmirror.com/@fluentui/react-context-selector/-/react-context-selector-9.2.4.tgz", "integrity": "sha512-4QJjaBWoJSW+Vu6XRT0A9j3h2RGdXNUj9ur6ljdXwG13DoE69v03cDVF8GT3jiXWO3veNJntelI0BS99Qb5wzQ==", "license": "MIT", "dependencies": {"@fluentui/react-utilities": "^9.23.1", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": ">=0.19.0 <=0.23.0"}}, "node_modules/@fluentui/react-combobox/node_modules/scheduler": {"version": "0.23.0", "resolved": "https://registry.npmmirror.com/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "license": "MIT", "peer": true, "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/@fluentui/react-components": {"version": "9.68.2", "resolved": "https://registry.npmmirror.com/@fluentui/react-components/-/react-components-9.68.2.tgz", "integrity": "sha512-5r4Pi2rNslPr18ewWP/xpisZKHFQFzYozYy2aYnvAIIiZic4dkCF2lxAix3nXJghNSiSQZszgRUd7YYPka1xFA==", "license": "MIT", "dependencies": {"@fluentui/react-accordion": "^9.8.3", "@fluentui/react-alert": "9.0.0-beta.124", "@fluentui/react-aria": "^9.16.2", "@fluentui/react-avatar": "^9.9.3", "@fluentui/react-badge": "^9.4.2", "@fluentui/react-breadcrumb": "^9.3.3", "@fluentui/react-button": "^9.6.3", "@fluentui/react-card": "^9.4.3", "@fluentui/react-carousel": "^9.8.3", "@fluentui/react-checkbox": "^9.5.2", "@fluentui/react-color-picker": "^9.2.2", "@fluentui/react-combobox": "^9.16.3", "@fluentui/react-dialog": "^9.14.3", "@fluentui/react-divider": "^9.4.2", "@fluentui/react-drawer": "^9.9.3", "@fluentui/react-field": "^9.4.2", "@fluentui/react-image": "^9.3.2", "@fluentui/react-infobutton": "9.0.0-beta.102", "@fluentui/react-infolabel": "^9.4.3", "@fluentui/react-input": "^9.7.2", "@fluentui/react-label": "^9.3.2", "@fluentui/react-link": "^9.6.2", "@fluentui/react-list": "^9.4.1", "@fluentui/react-menu": "^9.19.3", "@fluentui/react-message-bar": "^9.6.3", "@fluentui/react-motion": "^9.10.1", "@fluentui/react-nav": "^9.3.3", "@fluentui/react-overflow": "^9.5.3", "@fluentui/react-persona": "^9.5.3", "@fluentui/react-popover": "^9.12.3", "@fluentui/react-portal": "^9.7.2", "@fluentui/react-positioning": "^9.20.2", "@fluentui/react-progress": "^9.4.2", "@fluentui/react-provider": "^9.22.2", "@fluentui/react-radio": "^9.5.2", "@fluentui/react-rating": "^9.3.2", "@fluentui/react-search": "^9.3.2", "@fluentui/react-select": "^9.4.2", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-skeleton": "^9.4.2", "@fluentui/react-slider": "^9.5.2", "@fluentui/react-spinbutton": "^9.5.2", "@fluentui/react-spinner": "^9.7.2", "@fluentui/react-swatch-picker": "^9.4.2", "@fluentui/react-switch": "^9.4.2", "@fluentui/react-table": "^9.18.3", "@fluentui/react-tabs": "^9.9.2", "@fluentui/react-tabster": "^9.26.2", "@fluentui/react-tag-picker": "^9.7.3", "@fluentui/react-tags": "^9.7.3", "@fluentui/react-teaching-popover": "^9.6.3", "@fluentui/react-text": "^9.6.2", "@fluentui/react-textarea": "^9.6.2", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-toast": "^9.6.3", "@fluentui/react-toolbar": "^9.6.3", "@fluentui/react-tooltip": "^9.8.2", "@fluentui/react-tree": "^9.12.3", "@fluentui/react-utilities": "^9.23.1", "@fluentui/react-virtualizer": "9.0.0-alpha.102", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-dialog": {"version": "9.14.3", "resolved": "https://registry.npmmirror.com/@fluentui/react-dialog/-/react-dialog-9.14.3.tgz", "integrity": "sha512-88zYoa7bOqjzKyR+7L1Cykr9zHx2kH2aOahalZb5ogocSjXP2mGTXhC2S5nuRKwYzmc0Q05jOJJDLxdk9zfFhg==", "license": "MIT", "dependencies": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-aria": "^9.16.2", "@fluentui/react-context-selector": "^9.2.4", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-motion": "^9.10.1", "@fluentui/react-motion-components-preview": "^0.8.1", "@fluentui/react-portal": "^9.7.2", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-tabster": "^9.26.2", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-dialog/node_modules/@fluentui/react-context-selector": {"version": "9.2.4", "resolved": "https://registry.npmmirror.com/@fluentui/react-context-selector/-/react-context-selector-9.2.4.tgz", "integrity": "sha512-4QJjaBWoJSW+Vu6XRT0A9j3h2RGdXNUj9ur6ljdXwG13DoE69v03cDVF8GT3jiXWO3veNJntelI0BS99Qb5wzQ==", "license": "MIT", "dependencies": {"@fluentui/react-utilities": "^9.23.1", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": ">=0.19.0 <=0.23.0"}}, "node_modules/@fluentui/react-dialog/node_modules/scheduler": {"version": "0.23.0", "resolved": "https://registry.npmmirror.com/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "license": "MIT", "peer": true, "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/@fluentui/react-divider": {"version": "9.4.2", "resolved": "https://registry.npmmirror.com/@fluentui/react-divider/-/react-divider-9.4.2.tgz", "integrity": "sha512-+deTs9M/FswHSC2zUJU8YUniUx2IiUOnXdfpKd3/pK0caCF1cLA6Vrr36TdTCLBImJMQXj4dU7Gzoq9XCXacRw==", "license": "MIT", "dependencies": {"@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-drawer": {"version": "9.9.3", "resolved": "https://registry.npmmirror.com/@fluentui/react-drawer/-/react-drawer-9.9.3.tgz", "integrity": "sha512-380Riyv6mfo05p5+tDu8XwmGPRt/gX1PxWkFVx69so1V0w/WAytafHDbcKhiQThiBs+1bquY0mHc1jiUXjzbyw==", "license": "MIT", "dependencies": {"@fluentui/react-dialog": "^9.14.3", "@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-motion": "^9.10.1", "@fluentui/react-portal": "^9.7.2", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-tabster": "^9.26.2", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-field": {"version": "9.4.2", "resolved": "https://registry.npmmirror.com/@fluentui/react-field/-/react-field-9.4.2.tgz", "integrity": "sha512-11ayN4VoMiyX4U5/fi97AZV7UE587oUr0Uv7Pi02eGW2K0Hc/czf3UxRS1DTwwamVJPgIMHPiraF36b/X2oihg==", "license": "MIT", "dependencies": {"@fluentui/react-context-selector": "^9.2.4", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-label": "^9.3.2", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-field/node_modules/@fluentui/react-context-selector": {"version": "9.2.4", "resolved": "https://registry.npmmirror.com/@fluentui/react-context-selector/-/react-context-selector-9.2.4.tgz", "integrity": "sha512-4QJjaBWoJSW+Vu6XRT0A9j3h2RGdXNUj9ur6ljdXwG13DoE69v03cDVF8GT3jiXWO3veNJntelI0BS99Qb5wzQ==", "license": "MIT", "dependencies": {"@fluentui/react-utilities": "^9.23.1", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": ">=0.19.0 <=0.23.0"}}, "node_modules/@fluentui/react-field/node_modules/scheduler": {"version": "0.23.0", "resolved": "https://registry.npmmirror.com/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "license": "MIT", "peer": true, "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/@fluentui/react-icons": {"version": "2.0.307", "resolved": "https://registry.npmmirror.com/@fluentui/react-icons/-/react-icons-2.0.307.tgz", "integrity": "sha512-HSXrzQ6o+RWPnNy68EJN2M/Dh9LAJ8l5U9zWfwaFWDgktMF7dJxItyckA5BsH6inFisi6cqKtazsq9oZdAj32A==", "license": "MIT", "dependencies": {"@griffel/react": "^1.0.0", "tslib": "^2.1.0"}, "peerDependencies": {"react": ">=16.8.0 <19.0.0"}}, "node_modules/@fluentui/react-image": {"version": "9.3.2", "resolved": "https://registry.npmmirror.com/@fluentui/react-image/-/react-image-9.3.2.tgz", "integrity": "sha512-pLn3Me4pWKZ6eCC74zjk0YOq4nPlCMM7Sql4Ee5uYyscAqpmkl2fue8fPOWT1tar8FI342KpDZWTYoUWWbiFcA==", "license": "MIT", "dependencies": {"@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-infobutton": {"version": "9.0.0-beta.102", "resolved": "https://registry.npmmirror.com/@fluentui/react-infobutton/-/react-infobutton-9.0.0-beta.102.tgz", "integrity": "sha512-3kA4F0Vga8Ds6JGlBajLCCDOo/LmPuS786Wg7ui4ZTDYVIMzy1yp2XuVcZniifBFvEp0HQCUoDPWUV0VI3FfzQ==", "license": "MIT", "dependencies": {"@fluentui/react-icons": "^2.0.237", "@fluentui/react-jsx-runtime": "^9.0.36", "@fluentui/react-label": "^9.1.68", "@fluentui/react-popover": "^9.9.6", "@fluentui/react-tabster": "^9.21.0", "@fluentui/react-theme": "^9.1.19", "@fluentui/react-utilities": "^9.18.7", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-infolabel": {"version": "9.4.3", "resolved": "https://registry.npmmirror.com/@fluentui/react-infolabel/-/react-infolabel-9.4.3.tgz", "integrity": "sha512-lVyAINXxKBWp7a+c3L8G4VNEjBBepvtpdKGSrio+YQlmwJzqY3HmcE1YxNJyEI6iw5Wk29fEgs2PgpV7yDhsmA==", "license": "MIT", "dependencies": {"@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-label": "^9.3.2", "@fluentui/react-popover": "^9.12.3", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-tabster": "^9.26.2", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.8.0 <19.0.0", "@types/react-dom": ">=16.8.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.8.0 <19.0.0"}}, "node_modules/@fluentui/react-input": {"version": "9.7.2", "resolved": "https://registry.npmmirror.com/@fluentui/react-input/-/react-input-9.7.2.tgz", "integrity": "sha512-F8F1inTs3eNz+5GMDThgMmYhQyofEomAEVQnpzxvhhMwBbabMrA57Leh0xyVGGzYzl8o4GZqJxrBEzIkvfddtg==", "license": "MIT", "dependencies": {"@fluentui/react-field": "^9.4.2", "@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-jsx-runtime": {"version": "9.1.4", "resolved": "https://registry.npmmirror.com/@fluentui/react-jsx-runtime/-/react-jsx-runtime-9.1.4.tgz", "integrity": "sha512-VD1+vGFOF1F5TDx2+ghcstSltaT6OSmzuNz/9bGPn6CIX4e5+o6aIFWBlJcFvj4WiJS4d+l7d6uSwgJpqT4rhw==", "license": "MIT", "dependencies": {"@fluentui/react-utilities": "^9.23.1", "@swc/helpers": "^0.5.1", "react-is": "^17.0.2"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "react": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-label": {"version": "9.3.2", "resolved": "https://registry.npmmirror.com/@fluentui/react-label/-/react-label-9.3.2.tgz", "integrity": "sha512-TVYXC/dG5ck6fI2tpt+krZv6vYDAqJajIuSdoTs1IWYyg0CkKQXpPWSP7ghrlsmWPF4BKiTfaGZw+DgIR4lOXg==", "license": "MIT", "dependencies": {"@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-link": {"version": "9.6.2", "resolved": "https://registry.npmmirror.com/@fluentui/react-link/-/react-link-9.6.2.tgz", "integrity": "sha512-X7IJsC3PSQ0Wt6ETIOWW1+ByNCR2paxE4U3SnPrslCAtV845f/isI61STXKmVCSnK8y8fnLqWIVuPqPhMfDJog==", "license": "MIT", "dependencies": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-tabster": "^9.26.2", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-list": {"version": "9.4.1", "resolved": "https://registry.npmmirror.com/@fluentui/react-list/-/react-list-9.4.1.tgz", "integrity": "sha512-QR4MUulhSSaRmR5S3sRv64ca2KDYaA8c3OLTBcH1XhQYoPG8pgIrNSEiQlgkfn6ejUBtlv/imBbyG0qTHFKZZg==", "license": "MIT", "dependencies": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-checkbox": "^9.5.2", "@fluentui/react-context-selector": "^9.2.4", "@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-tabster": "^9.26.2", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.8.0 <19.0.0", "@types/react-dom": ">=16.8.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.8.0 <19.0.0"}}, "node_modules/@fluentui/react-list/node_modules/@fluentui/react-context-selector": {"version": "9.2.4", "resolved": "https://registry.npmmirror.com/@fluentui/react-context-selector/-/react-context-selector-9.2.4.tgz", "integrity": "sha512-4QJjaBWoJSW+Vu6XRT0A9j3h2RGdXNUj9ur6ljdXwG13DoE69v03cDVF8GT3jiXWO3veNJntelI0BS99Qb5wzQ==", "license": "MIT", "dependencies": {"@fluentui/react-utilities": "^9.23.1", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": ">=0.19.0 <=0.23.0"}}, "node_modules/@fluentui/react-list/node_modules/scheduler": {"version": "0.23.0", "resolved": "https://registry.npmmirror.com/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "license": "MIT", "peer": true, "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/@fluentui/react-menu": {"version": "9.19.3", "resolved": "https://registry.npmmirror.com/@fluentui/react-menu/-/react-menu-9.19.3.tgz", "integrity": "sha512-ZiXtTPk8SE8bS/zKIQdgu3nMNQwtJNocmIunb7ZzOnHN02Joh7R9klzyN9H4ZxwgwIKmRGmyJZzTp/D/8CczXQ==", "license": "MIT", "dependencies": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-aria": "^9.16.2", "@fluentui/react-context-selector": "^9.2.4", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-portal": "^9.7.2", "@fluentui/react-positioning": "^9.20.2", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-tabster": "^9.26.2", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-menu/node_modules/@fluentui/react-context-selector": {"version": "9.2.4", "resolved": "https://registry.npmmirror.com/@fluentui/react-context-selector/-/react-context-selector-9.2.4.tgz", "integrity": "sha512-4QJjaBWoJSW+Vu6XRT0A9j3h2RGdXNUj9ur6ljdXwG13DoE69v03cDVF8GT3jiXWO3veNJntelI0BS99Qb5wzQ==", "license": "MIT", "dependencies": {"@fluentui/react-utilities": "^9.23.1", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": ">=0.19.0 <=0.23.0"}}, "node_modules/@fluentui/react-menu/node_modules/scheduler": {"version": "0.23.0", "resolved": "https://registry.npmmirror.com/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "license": "MIT", "peer": true, "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/@fluentui/react-message-bar": {"version": "9.6.3", "resolved": "https://registry.npmmirror.com/@fluentui/react-message-bar/-/react-message-bar-9.6.3.tgz", "integrity": "sha512-gFeZb/WIBdpNrJ9Q3GoxPzYOwZ37MnbVxny8frO29cL0kZKYF9gbaBQ2qo12/YK4SMs/dw1usf8As551X3jZBw==", "license": "MIT", "dependencies": {"@fluentui/react-button": "^9.6.3", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-link": "^9.6.2", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1", "react-transition-group": "^4.4.1"}, "peerDependencies": {"@types/react": ">=16.8.0 <19.0.0", "@types/react-dom": ">=16.8.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.8.0 <19.0.0"}}, "node_modules/@fluentui/react-motion": {"version": "9.10.1", "resolved": "https://registry.npmmirror.com/@fluentui/react-motion/-/react-motion-9.10.1.tgz", "integrity": "sha512-DMHRWzWoei4/FCV9ziQ9M4GkY8P24Ld8ouPjH5Eopurg/fGua2AOVDkm/sY2AdjsWSJ1oDGqS6xMXRYm/u6FtQ==", "license": "MIT", "dependencies": {"@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-utilities": "^9.23.1", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.8.0 <19.0.0", "@types/react-dom": ">=16.8.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.8.0 <19.0.0"}}, "node_modules/@fluentui/react-motion-components-preview": {"version": "0.8.1", "resolved": "https://registry.npmmirror.com/@fluentui/react-motion-components-preview/-/react-motion-components-preview-0.8.1.tgz", "integrity": "sha512-TeU3nNeOapIyLd15T3jBArEsdrsZoknW4U/RJv6dFOe0aV2IRQ86wnKbD8eVmXxc9cLQ1UoWxF549YfQwxyKKQ==", "license": "MIT", "dependencies": {"@fluentui/react-motion": "*", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-nav": {"version": "9.3.3", "resolved": "https://registry.npmmirror.com/@fluentui/react-nav/-/react-nav-9.3.3.tgz", "integrity": "sha512-smzXtC1j3iMddi2GAwe8xv5e77Mr6PtrLnnrctpvRpUdMqKi5pnjXAqnErHe9JP05FuYC7DoNTQue4SXNgzC1A==", "license": "MIT", "dependencies": {"@fluentui/react-aria": "^9.16.2", "@fluentui/react-button": "^9.6.3", "@fluentui/react-context-selector": "^9.2.4", "@fluentui/react-divider": "^9.4.2", "@fluentui/react-drawer": "^9.9.3", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-motion": "^9.10.1", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-tabster": "^9.26.2", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-tooltip": "^9.8.2", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-nav/node_modules/@fluentui/react-context-selector": {"version": "9.2.4", "resolved": "https://registry.npmmirror.com/@fluentui/react-context-selector/-/react-context-selector-9.2.4.tgz", "integrity": "sha512-4QJjaBWoJSW+Vu6XRT0A9j3h2RGdXNUj9ur6ljdXwG13DoE69v03cDVF8GT3jiXWO3veNJntelI0BS99Qb5wzQ==", "license": "MIT", "dependencies": {"@fluentui/react-utilities": "^9.23.1", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": ">=0.19.0 <=0.23.0"}}, "node_modules/@fluentui/react-nav/node_modules/scheduler": {"version": "0.23.0", "resolved": "https://registry.npmmirror.com/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "license": "MIT", "peer": true, "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/@fluentui/react-overflow": {"version": "9.5.3", "resolved": "https://registry.npmmirror.com/@fluentui/react-overflow/-/react-overflow-9.5.3.tgz", "integrity": "sha512-FAq96FkHp+3/0QA+pYSKwSTQpFifacPbEMkt2rAQWMnhxXXuJFhBzLrdqK0HFIT5SijkKNcROMe8YdSl4RdK7g==", "license": "MIT", "dependencies": {"@fluentui/priority-overflow": "^9.1.15", "@fluentui/react-context-selector": "^9.2.4", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-overflow/node_modules/@fluentui/react-context-selector": {"version": "9.2.4", "resolved": "https://registry.npmmirror.com/@fluentui/react-context-selector/-/react-context-selector-9.2.4.tgz", "integrity": "sha512-4QJjaBWoJSW+Vu6XRT0A9j3h2RGdXNUj9ur6ljdXwG13DoE69v03cDVF8GT3jiXWO3veNJntelI0BS99Qb5wzQ==", "license": "MIT", "dependencies": {"@fluentui/react-utilities": "^9.23.1", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": ">=0.19.0 <=0.23.0"}}, "node_modules/@fluentui/react-overflow/node_modules/scheduler": {"version": "0.23.0", "resolved": "https://registry.npmmirror.com/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "license": "MIT", "peer": true, "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/@fluentui/react-persona": {"version": "9.5.3", "resolved": "https://registry.npmmirror.com/@fluentui/react-persona/-/react-persona-9.5.3.tgz", "integrity": "sha512-xZwVjw9dYGR9QVZT7O9KVFmtFII+4JSFdbLLsdaWdPwuGVUqYMuuwESyu6aAROIWjXU8GAkFlhXXMUAQ//aGVw==", "license": "MIT", "dependencies": {"@fluentui/react-avatar": "^9.9.3", "@fluentui/react-badge": "^9.4.2", "@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-popover": {"version": "9.12.3", "resolved": "https://registry.npmmirror.com/@fluentui/react-popover/-/react-popover-9.12.3.tgz", "integrity": "sha512-LNzzZghFzpfCDCxl2Sk5aRoCiAhjr86HgwS9ENkV9SiN30TiD9naRF33+zWVKTZwY3ZfRHEC/yo8BH1OXmamcw==", "license": "MIT", "dependencies": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-aria": "^9.16.2", "@fluentui/react-context-selector": "^9.2.4", "@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-portal": "^9.7.2", "@fluentui/react-positioning": "^9.20.2", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-tabster": "^9.26.2", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-popover/node_modules/@fluentui/react-context-selector": {"version": "9.2.4", "resolved": "https://registry.npmmirror.com/@fluentui/react-context-selector/-/react-context-selector-9.2.4.tgz", "integrity": "sha512-4QJjaBWoJSW+Vu6XRT0A9j3h2RGdXNUj9ur6ljdXwG13DoE69v03cDVF8GT3jiXWO3veNJntelI0BS99Qb5wzQ==", "license": "MIT", "dependencies": {"@fluentui/react-utilities": "^9.23.1", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": ">=0.19.0 <=0.23.0"}}, "node_modules/@fluentui/react-popover/node_modules/scheduler": {"version": "0.23.0", "resolved": "https://registry.npmmirror.com/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "license": "MIT", "peer": true, "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/@fluentui/react-portal": {"version": "9.7.2", "resolved": "https://registry.npmmirror.com/@fluentui/react-portal/-/react-portal-9.7.2.tgz", "integrity": "sha512-UC12+PsfrtAB+WrT39A8l86Szu2SXh7CUOf6q7+lJ2AWDdt93cYNQOU4QuLotoDebGuXZW4xMiB6QHy0sUGFiw==", "license": "MIT", "dependencies": {"@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-tabster": "^9.26.2", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1", "use-disposable": "^1.0.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-positioning": {"version": "9.20.2", "resolved": "https://registry.npmmirror.com/@fluentui/react-positioning/-/react-positioning-9.20.2.tgz", "integrity": "sha512-nqj9hVl26D75fK072wDG2reYKI+aJx4X6IeqxYiYAzXlz6gGJuRKqmQwSAYiy9y6vBrpwWj5QGMjEOlwkd42nw==", "license": "MIT", "dependencies": {"@floating-ui/devtools": "0.2.1", "@floating-ui/dom": "^1.6.12", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1", "use-sync-external-store": "^1.2.0"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-progress": {"version": "9.4.2", "resolved": "https://registry.npmmirror.com/@fluentui/react-progress/-/react-progress-9.4.2.tgz", "integrity": "sha512-ra7kKByfq+uIg330twi9aagqUkKKls35jC9Hth0k8txIvt3u9y7Qm28wZStP4EAxapJXs/4LfI+0ZwdPQguH2A==", "license": "MIT", "dependencies": {"@fluentui/react-field": "^9.4.2", "@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-provider": {"version": "9.22.2", "resolved": "https://registry.npmmirror.com/@fluentui/react-provider/-/react-provider-9.22.2.tgz", "integrity": "sha512-7/ZUluKMBb9sxlSi/r0n4nHcgfWW0Ph4ZkkAi8c4uFg7JSKJjR3a1Ii9Wqxs+veK9PdNzILe6EMRXj8MWje8HA==", "license": "MIT", "dependencies": {"@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-tabster": "^9.26.2", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/core": "^1.16.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-radio": {"version": "9.5.2", "resolved": "https://registry.npmmirror.com/@fluentui/react-radio/-/react-radio-9.5.2.tgz", "integrity": "sha512-ny+odHLH6h0bl/x0wxT1/J9k93lxQb3UMrTppDNFN4QudomHJJCAuwIzYLHrehxU0Iz6a9BGcbBXyUqEZANvNg==", "license": "MIT", "dependencies": {"@fluentui/react-field": "^9.4.2", "@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-label": "^9.3.2", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-tabster": "^9.26.2", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-rating": {"version": "9.3.2", "resolved": "https://registry.npmmirror.com/@fluentui/react-rating/-/react-rating-9.3.2.tgz", "integrity": "sha512-TsrPYNZW57A8KhBjWxTyeyb/3qt9NG8ZmBy5QB9DXS7dykj02dJXOVVOT3+vRQk4aKZ17RxXYZXfZNYoYzlAnQ==", "license": "MIT", "dependencies": {"@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-tabster": "^9.26.2", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.8.0 <19.0.0", "@types/react-dom": ">=16.8.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.8.0 <19.0.0"}}, "node_modules/@fluentui/react-search": {"version": "9.3.2", "resolved": "https://registry.npmmirror.com/@fluentui/react-search/-/react-search-9.3.2.tgz", "integrity": "sha512-s2tvGeVGy6jdIEo3ezhzAg1CzvsaaspXgF/x9Ykapyjs9AiG78cWJ3/HAfZY6kajq9h47eTwYEkDTEORqdvajQ==", "license": "MIT", "dependencies": {"@fluentui/react-icons": "^2.0.245", "@fluentui/react-input": "^9.7.2", "@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-select": {"version": "9.4.2", "resolved": "https://registry.npmmirror.com/@fluentui/react-select/-/react-select-9.4.2.tgz", "integrity": "sha512-ST5hoXjjN0RQMDtwlC85AKej+W+3ayx1a9v+YlkQX2lOh+6Sj3iABsETgPM01Se8YwJ4DCbkJ5B52FVxXzXuuA==", "license": "MIT", "dependencies": {"@fluentui/react-field": "^9.4.2", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-shared-contexts": {"version": "9.24.1", "resolved": "https://registry.npmmirror.com/@fluentui/react-shared-contexts/-/react-shared-contexts-9.24.1.tgz", "integrity": "sha512-gQmynczh226aiPTV8PyzpOlSX0QTOZt0DG7ok/Q53DLWqBkRqsmfMHCEKZcDnjnemizu1IzB01NiXjXtk3WKzQ==", "license": "MIT", "dependencies": {"@fluentui/react-theme": "^9.2.0", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "react": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-skeleton": {"version": "9.4.2", "resolved": "https://registry.npmmirror.com/@fluentui/react-skeleton/-/react-skeleton-9.4.2.tgz", "integrity": "sha512-v3elD30iWnx2frIdvcZnnd8D62pWi2HmLP5JA7So/v4iJ+mAAfnvLKDzbs27rtGhO6qTptPROt5WmtnHUI8y0Q==", "license": "MIT", "dependencies": {"@fluentui/react-field": "^9.4.2", "@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-slider": {"version": "9.5.2", "resolved": "https://registry.npmmirror.com/@fluentui/react-slider/-/react-slider-9.5.2.tgz", "integrity": "sha512-ZWoR+a+hLdbmjlH3WJNccH+yEjaeXVdZJT85v/OwrKIUoLdDHkCOPuW35lxuVd8slLlh7gBxRKC2aS1uP/83Iw==", "license": "MIT", "dependencies": {"@fluentui/react-field": "^9.4.2", "@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-tabster": "^9.26.2", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-spinbutton": {"version": "9.5.2", "resolved": "https://registry.npmmirror.com/@fluentui/react-spinbutton/-/react-spinbutton-9.5.2.tgz", "integrity": "sha512-LKRNI2SbdiKL5u8m1DZCaamfJjlHh+a0VDbPvy6KWfORHtlUYhvbGf1CRLGHNNLtL40WgbEzZx9DeM6iv/kM/w==", "license": "MIT", "dependencies": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-field": "^9.4.2", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-spinner": {"version": "9.7.2", "resolved": "https://registry.npmmirror.com/@fluentui/react-spinner/-/react-spinner-9.7.2.tgz", "integrity": "sha512-+5XFzqwfDrFhK8iJ1MDdYZ0wGHeGsFR5H9Iw58JyIxcNNFNkHIGZYLpHgM15wYHzLmheS4CzWUY1IT2Ifen3oQ==", "license": "MIT", "dependencies": {"@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-label": "^9.3.2", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-swatch-picker": {"version": "9.4.2", "resolved": "https://registry.npmmirror.com/@fluentui/react-swatch-picker/-/react-swatch-picker-9.4.2.tgz", "integrity": "sha512-vduek/Psaqll/bFO9XJ91YCnE+KXphOqTkYzBKf4aLYJBJys/IFi5sIrEGlaxDMpgg8BeExSHjit9vuCArGjaA==", "license": "MIT", "dependencies": {"@fluentui/react-context-selector": "^9.2.4", "@fluentui/react-field": "^9.4.2", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-tabster": "^9.26.2", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.8.0 <19.0.0", "@types/react-dom": ">=16.8.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.8.0 <19.0.0"}}, "node_modules/@fluentui/react-swatch-picker/node_modules/@fluentui/react-context-selector": {"version": "9.2.4", "resolved": "https://registry.npmmirror.com/@fluentui/react-context-selector/-/react-context-selector-9.2.4.tgz", "integrity": "sha512-4QJjaBWoJSW+Vu6XRT0A9j3h2RGdXNUj9ur6ljdXwG13DoE69v03cDVF8GT3jiXWO3veNJntelI0BS99Qb5wzQ==", "license": "MIT", "dependencies": {"@fluentui/react-utilities": "^9.23.1", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": ">=0.19.0 <=0.23.0"}}, "node_modules/@fluentui/react-swatch-picker/node_modules/scheduler": {"version": "0.23.0", "resolved": "https://registry.npmmirror.com/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "license": "MIT", "peer": true, "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/@fluentui/react-switch": {"version": "9.4.2", "resolved": "https://registry.npmmirror.com/@fluentui/react-switch/-/react-switch-9.4.2.tgz", "integrity": "sha512-u/E7UeRRUbe5RnHNdslois6eGV6AFqJpzau48EDokmyJmv9nHIjq8ckt32kRH1dNcUm0/6BsXS40Wg0dhLgS0g==", "license": "MIT", "dependencies": {"@fluentui/react-field": "^9.4.2", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-label": "^9.3.2", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-tabster": "^9.26.2", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-table": {"version": "9.18.3", "resolved": "https://registry.npmmirror.com/@fluentui/react-table/-/react-table-9.18.3.tgz", "integrity": "sha512-khocRe5sjHp1KDDfnw4nd6iA7tWn4y7pK10ZTPRixSWzW2mu24PSPWq9I+suMpAuIhcjZfbit4SRZd8v+JQt+Q==", "license": "MIT", "dependencies": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-aria": "^9.16.2", "@fluentui/react-avatar": "^9.9.3", "@fluentui/react-checkbox": "^9.5.2", "@fluentui/react-context-selector": "^9.2.4", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-radio": "^9.5.2", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-tabster": "^9.26.2", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-table/node_modules/@fluentui/react-context-selector": {"version": "9.2.4", "resolved": "https://registry.npmmirror.com/@fluentui/react-context-selector/-/react-context-selector-9.2.4.tgz", "integrity": "sha512-4QJjaBWoJSW+Vu6XRT0A9j3h2RGdXNUj9ur6ljdXwG13DoE69v03cDVF8GT3jiXWO3veNJntelI0BS99Qb5wzQ==", "license": "MIT", "dependencies": {"@fluentui/react-utilities": "^9.23.1", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": ">=0.19.0 <=0.23.0"}}, "node_modules/@fluentui/react-table/node_modules/scheduler": {"version": "0.23.0", "resolved": "https://registry.npmmirror.com/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "license": "MIT", "peer": true, "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/@fluentui/react-tabs": {"version": "9.9.2", "resolved": "https://registry.npmmirror.com/@fluentui/react-tabs/-/react-tabs-9.9.2.tgz", "integrity": "sha512-7dOhz7NHoi4kcn0B+RcReOgGi776yy5chPborwZQOTLg/E94fGkUecj8TkCLZdEUBOTrSIPaaWRF6m4atl+UEQ==", "license": "MIT", "dependencies": {"@fluentui/react-context-selector": "^9.2.4", "@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-tabster": "^9.26.2", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-tabs/node_modules/@fluentui/react-context-selector": {"version": "9.2.4", "resolved": "https://registry.npmmirror.com/@fluentui/react-context-selector/-/react-context-selector-9.2.4.tgz", "integrity": "sha512-4QJjaBWoJSW+Vu6XRT0A9j3h2RGdXNUj9ur6ljdXwG13DoE69v03cDVF8GT3jiXWO3veNJntelI0BS99Qb5wzQ==", "license": "MIT", "dependencies": {"@fluentui/react-utilities": "^9.23.1", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": ">=0.19.0 <=0.23.0"}}, "node_modules/@fluentui/react-tabs/node_modules/scheduler": {"version": "0.23.0", "resolved": "https://registry.npmmirror.com/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "license": "MIT", "peer": true, "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/@fluentui/react-tabster": {"version": "9.26.2", "resolved": "https://registry.npmmirror.com/@fluentui/react-tabster/-/react-tabster-9.26.2.tgz", "integrity": "sha512-Go9wzXjhZEcejfiw1vFqgC06p/+fPxw5woPblmiXYUDjNByCY5iTx2vNv6gf+duFZqLDITVhsA0t5P9xdDLiLg==", "license": "MIT", "dependencies": {"@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1", "keyborg": "^2.6.0", "tabster": "^8.5.5"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-tag-picker": {"version": "9.7.3", "resolved": "https://registry.npmmirror.com/@fluentui/react-tag-picker/-/react-tag-picker-9.7.3.tgz", "integrity": "sha512-IRvQ00Q8QU8ReXOB7agTsl0hc+9xMWW9wVEnkvnAxGYHWrGhq/w161+kZHO5LAVHOB7HHufKOlgNKLokKcpUUQ==", "license": "MIT", "dependencies": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-aria": "^9.16.2", "@fluentui/react-combobox": "^9.16.3", "@fluentui/react-context-selector": "^9.2.4", "@fluentui/react-field": "^9.4.2", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-portal": "^9.7.2", "@fluentui/react-positioning": "^9.20.2", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-tabster": "^9.26.2", "@fluentui/react-tags": "^9.7.3", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-tag-picker/node_modules/@fluentui/react-context-selector": {"version": "9.2.4", "resolved": "https://registry.npmmirror.com/@fluentui/react-context-selector/-/react-context-selector-9.2.4.tgz", "integrity": "sha512-4QJjaBWoJSW+Vu6XRT0A9j3h2RGdXNUj9ur6ljdXwG13DoE69v03cDVF8GT3jiXWO3veNJntelI0BS99Qb5wzQ==", "license": "MIT", "dependencies": {"@fluentui/react-utilities": "^9.23.1", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": ">=0.19.0 <=0.23.0"}}, "node_modules/@fluentui/react-tag-picker/node_modules/scheduler": {"version": "0.23.0", "resolved": "https://registry.npmmirror.com/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "license": "MIT", "peer": true, "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/@fluentui/react-tags": {"version": "9.7.3", "resolved": "https://registry.npmmirror.com/@fluentui/react-tags/-/react-tags-9.7.3.tgz", "integrity": "sha512-SSzU9tpuYdU9MsWoaK2dCuNl47yT8Wm+yBJLUJ6T/mV2yrJ7nL7r9/8LsSLf3T02MKPqWp9NwV2b1Bdy7cWRJg==", "license": "MIT", "dependencies": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-aria": "^9.16.2", "@fluentui/react-avatar": "^9.9.3", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-tabster": "^9.26.2", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-teaching-popover": {"version": "9.6.3", "resolved": "https://registry.npmmirror.com/@fluentui/react-teaching-popover/-/react-teaching-popover-9.6.3.tgz", "integrity": "sha512-sorXgLYGVtP30IpAZIXVyylIW13FL3r+2DXPGBjq2Q69JM1ZBvzNaty0csx6V/Keoo1DGLF/39NpLgdVWWdikw==", "license": "MIT", "dependencies": {"@fluentui/react-aria": "^9.16.2", "@fluentui/react-button": "^9.6.3", "@fluentui/react-context-selector": "^9.2.4", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-popover": "^9.12.3", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-tabster": "^9.26.2", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1", "use-sync-external-store": "^1.2.0"}, "peerDependencies": {"@types/react": ">=16.8.0 <19.0.0", "@types/react-dom": ">=16.8.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.8.0 <19.0.0"}}, "node_modules/@fluentui/react-teaching-popover/node_modules/@fluentui/react-context-selector": {"version": "9.2.4", "resolved": "https://registry.npmmirror.com/@fluentui/react-context-selector/-/react-context-selector-9.2.4.tgz", "integrity": "sha512-4QJjaBWoJSW+Vu6XRT0A9j3h2RGdXNUj9ur6ljdXwG13DoE69v03cDVF8GT3jiXWO3veNJntelI0BS99Qb5wzQ==", "license": "MIT", "dependencies": {"@fluentui/react-utilities": "^9.23.1", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": ">=0.19.0 <=0.23.0"}}, "node_modules/@fluentui/react-teaching-popover/node_modules/scheduler": {"version": "0.23.0", "resolved": "https://registry.npmmirror.com/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "license": "MIT", "peer": true, "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/@fluentui/react-text": {"version": "9.6.2", "resolved": "https://registry.npmmirror.com/@fluentui/react-text/-/react-text-9.6.2.tgz", "integrity": "sha512-gBUV3jvTdKmvXkRU2s9vsFWIBP2Ukx+BAalR+vgJM2wibVYFAgHPVDw2/Sojjl7NxzgHDAD2dZdKegQR1AScZQ==", "license": "MIT", "dependencies": {"@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-textarea": {"version": "9.6.2", "resolved": "https://registry.npmmirror.com/@fluentui/react-textarea/-/react-textarea-9.6.2.tgz", "integrity": "sha512-8sRTrrO0/iEVsA2OfINasG3eo5TVQ9br0vmm04zFZgtxKkEC9xtuJGLLHk3B6wbsmAST/JVl9k3xvkg1ES/Xqg==", "license": "MIT", "dependencies": {"@fluentui/react-field": "^9.4.2", "@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-theme": {"version": "9.2.0", "resolved": "https://registry.npmmirror.com/@fluentui/react-theme/-/react-theme-9.2.0.tgz", "integrity": "sha512-Q0zp/MY1m5RjlkcwMcjn/PQRT2T+q3bgxuxWbhgaD07V+tLzBhGROvuqbsdg4YWF/IK21zPfLhmGyifhEu0DnQ==", "license": "MIT", "dependencies": {"@fluentui/tokens": "1.0.0-alpha.22", "@swc/helpers": "^0.5.1"}}, "node_modules/@fluentui/react-toast": {"version": "9.6.3", "resolved": "https://registry.npmmirror.com/@fluentui/react-toast/-/react-toast-9.6.3.tgz", "integrity": "sha512-7PCmFbeQxLGTxr8pKE621C8NHZ85A25i+0FZcqHpHMmVYHJhEvefxMS7ttLD7nlfwNHW63wtTJlAFIJ43NTfrg==", "license": "MIT", "dependencies": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-aria": "^9.16.2", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-motion": "^9.10.1", "@fluentui/react-motion-components-preview": "^0.8.1", "@fluentui/react-portal": "^9.7.2", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-tabster": "^9.26.2", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-toolbar": {"version": "9.6.3", "resolved": "https://registry.npmmirror.com/@fluentui/react-toolbar/-/react-toolbar-9.6.3.tgz", "integrity": "sha512-8hlDI4f2fpqJVu94y4RyuNxq7TA3Blj3tepyCZ3ifTcXTPt/DEfAz48MF4H8xRygz7Et6klOLwCkrwWm0U+8PA==", "license": "MIT", "dependencies": {"@fluentui/react-button": "^9.6.3", "@fluentui/react-context-selector": "^9.2.4", "@fluentui/react-divider": "^9.4.2", "@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-radio": "^9.5.2", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-tabster": "^9.26.2", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-toolbar/node_modules/@fluentui/react-context-selector": {"version": "9.2.4", "resolved": "https://registry.npmmirror.com/@fluentui/react-context-selector/-/react-context-selector-9.2.4.tgz", "integrity": "sha512-4QJjaBWoJSW+Vu6XRT0A9j3h2RGdXNUj9ur6ljdXwG13DoE69v03cDVF8GT3jiXWO3veNJntelI0BS99Qb5wzQ==", "license": "MIT", "dependencies": {"@fluentui/react-utilities": "^9.23.1", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": ">=0.19.0 <=0.23.0"}}, "node_modules/@fluentui/react-toolbar/node_modules/scheduler": {"version": "0.23.0", "resolved": "https://registry.npmmirror.com/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "license": "MIT", "peer": true, "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/@fluentui/react-tooltip": {"version": "9.8.2", "resolved": "https://registry.npmmirror.com/@fluentui/react-tooltip/-/react-tooltip-9.8.2.tgz", "integrity": "sha512-nqU4JqgkC50HYplmAWvNMSgemK8OFSlYqSurzGw9KAFTqddKlVjPmYochR6xzYq7Kpu1qfaCmaX/Nqz8rM30IA==", "license": "MIT", "dependencies": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-portal": "^9.7.2", "@fluentui/react-positioning": "^9.20.2", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-tabster": "^9.26.2", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-tree": {"version": "9.12.3", "resolved": "https://registry.npmmirror.com/@fluentui/react-tree/-/react-tree-9.12.3.tgz", "integrity": "sha512-XZUJh7sU1EefU4Ow7tEF/KTi8GRyIToiCAAZ5uThp2lEJ8/RSAEI6m0eh8jX4JIs1bPi5sfnLWWI34Fqua0ltA==", "license": "MIT", "dependencies": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-aria": "^9.16.2", "@fluentui/react-avatar": "^9.9.3", "@fluentui/react-button": "^9.6.3", "@fluentui/react-checkbox": "^9.5.2", "@fluentui/react-context-selector": "^9.2.4", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-motion": "^9.10.1", "@fluentui/react-motion-components-preview": "^0.8.1", "@fluentui/react-radio": "^9.5.2", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-tabster": "^9.26.2", "@fluentui/react-theme": "^9.2.0", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-tree/node_modules/@fluentui/react-context-selector": {"version": "9.2.4", "resolved": "https://registry.npmmirror.com/@fluentui/react-context-selector/-/react-context-selector-9.2.4.tgz", "integrity": "sha512-4QJjaBWoJSW+Vu6XRT0A9j3h2RGdXNUj9ur6ljdXwG13DoE69v03cDVF8GT3jiXWO3veNJntelI0BS99Qb5wzQ==", "license": "MIT", "dependencies": {"@fluentui/react-utilities": "^9.23.1", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": ">=0.19.0 <=0.23.0"}}, "node_modules/@fluentui/react-tree/node_modules/scheduler": {"version": "0.23.0", "resolved": "https://registry.npmmirror.com/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "license": "MIT", "peer": true, "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/@fluentui/react-utilities": {"version": "9.23.1", "resolved": "https://registry.npmmirror.com/@fluentui/react-utilities/-/react-utilities-9.23.1.tgz", "integrity": "sha512-Vzxq4To9/HhfohYaZLcmSSSjLwn3bZ6HrlHHyCtv8jA28D5HtI9Xue3Xqy+nCshMSozwBxkrI7iWj9awmyUdQA==", "license": "MIT", "dependencies": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-shared-contexts": "^9.24.1", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "react": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-virtualizer": {"version": "9.0.0-alpha.102", "resolved": "https://registry.npmmirror.com/@fluentui/react-virtualizer/-/react-virtualizer-9.0.0-alpha.102.tgz", "integrity": "sha512-kt/kuAMTKTTY/00ToUlgUwUCty2HGj4Tnr+fxKRmr7Ziy5VWhi1YoNJ8vcgmxog5J90t4tS29LB0LP0KztQUVg==", "license": "MIT", "dependencies": {"@fluentui/react-jsx-runtime": "^9.1.4", "@fluentui/react-shared-contexts": "^9.24.1", "@fluentui/react-utilities": "^9.23.1", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/tokens": {"version": "1.0.0-alpha.22", "resolved": "https://registry.npmmirror.com/@fluentui/tokens/-/tokens-1.0.0-alpha.22.tgz", "integrity": "sha512-i9fgYyyCWFRdUi+vQwnV6hp7wpLGK4p09B+O/f2u71GBXzPuniubPYvrIJYtl444DD6shLjYToJhQ1S6XTFwLg==", "license": "MIT", "dependencies": {"@swc/helpers": "^0.5.1"}}, "node_modules/@griffel/core": {"version": "1.19.2", "resolved": "https://registry.npmmirror.com/@griffel/core/-/core-1.19.2.tgz", "integrity": "sha512-WkB/QQkjy9dE4vrNYGhQvRRUHFkYVOuaznVOMNTDT4pS9aTJ9XPrMTXXlkpcwaf0D3vNKoerj4zAwnU2lBzbOg==", "license": "MIT", "dependencies": {"@emotion/hash": "^0.9.0", "@griffel/style-types": "^1.3.0", "csstype": "^3.1.3", "rtl-css-js": "^1.16.1", "stylis": "^4.2.0", "tslib": "^2.1.0"}}, "node_modules/@griffel/react": {"version": "1.5.30", "resolved": "https://registry.npmmirror.com/@griffel/react/-/react-1.5.30.tgz", "integrity": "sha512-1q4ojbEVFY5YA0j1NamP0WWF4BKh+GHsVugltDYeEgEaVbH3odJ7tJabuhQgY+7Nhka0pyEFWSiHJev0K3FSew==", "license": "MIT", "dependencies": {"@griffel/core": "^1.19.2", "tslib": "^2.1.0"}, "peerDependencies": {"react": ">=16.8.0 <20.0.0"}}, "node_modules/@griffel/style-types": {"version": "1.3.0", "resolved": "https://registry.npmmirror.com/@griffel/style-types/-/style-types-1.3.0.tgz", "integrity": "sha512-bHwD3sUE84Xwv4dH011gOKe1jul77M1S6ZFN9Tnq8pvZ48UMdY//vtES6fv7GRS5wXYT4iqxQPBluAiYAfkpmw==", "license": "MIT", "dependencies": {"csstype": "^3.1.3"}}, "node_modules/@jridgewell/gen-mapping": {"version": "0.3.12", "resolved": "https://registry.npmmirror.com/@jridgewell/gen-mapping/-/gen-mapping-0.3.12.tgz", "integrity": "sha512-OuLGC46TjB5BbN1dH8JULVVZY4WTdkF7tV9Ys6wLL1rubZnCMstOhNHueU5bLCrnRuDhKPDM4g6sw4Bel5Gzqg==", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0", "@jridgewell/trace-mapping": "^0.3.24"}}, "node_modules/@jridgewell/resolve-uri": {"version": "3.1.2", "resolved": "https://registry.npmmirror.com/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz", "integrity": "sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==", "dev": true, "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.5.4", "resolved": "https://registry.npmmirror.com/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.4.tgz", "integrity": "sha512-VT2+G1VQs/9oz078bLrYbecdZKs912zQlkelYpuf+SXF+QvZDYJlbx/LSx+meSAwdDFnF8FVXW92AVjjkVmgFw==", "dev": true, "license": "MIT"}, "node_modules/@jridgewell/trace-mapping": {"version": "0.3.29", "resolved": "https://registry.npmmirror.com/@jridgewell/trace-mapping/-/trace-mapping-0.3.29.tgz", "integrity": "sha512-uw6guiW/gcAGPDhLmd77/6lW8QLeiV5RUTsAX46Db6oLhGaVj4lhnPwb184s1bkc8kdVg/+h988dro8GRDpmYQ==", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14"}}, "node_modules/@monaco-editor/loader": {"version": "1.5.0", "resolved": "https://registry.npmmirror.com/@monaco-editor/loader/-/loader-1.5.0.tgz", "integrity": "sha512-hKoGSM+7aAc7eRTRjpqAZucPmoNOC4UUbknb/VNoTkEIkCPhqV8LfbsgM1webRM7S/z21eHEx9Fkwx8Z/C/+Xw==", "license": "MIT", "dependencies": {"state-local": "^1.0.6"}}, "node_modules/@monaco-editor/react": {"version": "4.7.0", "resolved": "https://registry.npmmirror.com/@monaco-editor/react/-/react-4.7.0.tgz", "integrity": "sha512-cyzXQCtO47ydzxpQtCGSQGOC8Gk3ZUeBXFAxD+CWXYFo5OqZyZUonFl0DwUlTyAfRHntBfw2p3w4s9R6oe1eCA==", "license": "MIT", "dependencies": {"@monaco-editor/loader": "^1.5.0"}, "peerDependencies": {"monaco-editor": ">= 0.25.0 < 1", "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}}, "node_modules/@rolldown/pluginutils": {"version": "1.0.0-beta.27", "resolved": "https://registry.npmmirror.com/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.27.tgz", "integrity": "sha512-+d0F4MKMCbeVUJwG96uQ4SgAznZNSq93I3V+9NHA4OpvqG8mRCpGdKmK8l/dl02h2CCDHwW2FqilnTyDcAnqjA==", "dev": true, "license": "MIT"}, "node_modules/@rollup/rollup-android-arm-eabi": {"version": "4.46.2", "resolved": "https://registry.npmmirror.com/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.46.2.tgz", "integrity": "sha512-Zj3Hl6sN34xJtMv7Anwb5Gu01yujyE/cLBDB2gnHTAHaWS1Z38L7kuSG+oAh0giZMqG060f/YBStXtMH6FvPMA==", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["android"]}, "node_modules/@rollup/rollup-android-arm64": {"version": "4.46.2", "resolved": "https://registry.npmmirror.com/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.46.2.tgz", "integrity": "sha512-nTeCWY83kN64oQ5MGz3CgtPx8NSOhC5lWtsjTs+8JAJNLcP3QbLCtDDgUKQc/Ro/frpMq4SHUaHN6AMltcEoLQ==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["android"]}, "node_modules/@rollup/rollup-darwin-arm64": {"version": "4.46.2", "resolved": "https://registry.npmmirror.com/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.46.2.tgz", "integrity": "sha512-HV7bW2Fb/F5KPdM/9bApunQh68YVDU8sO8BvcW9OngQVN3HHHkw99wFupuUJfGR9pYLLAjcAOA6iO+evsbBaPQ==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"]}, "node_modules/@rollup/rollup-darwin-x64": {"version": "4.46.2", "resolved": "https://registry.npmmirror.com/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.46.2.tgz", "integrity": "sha512-SSj8TlYV5nJixSsm/y3QXfhspSiLYP11zpfwp6G/YDXctf3Xkdnk4woJIF5VQe0of2OjzTt8EsxnJDCdHd2xMA==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"]}, "node_modules/@rollup/rollup-freebsd-arm64": {"version": "4.46.2", "resolved": "https://registry.npmmirror.com/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.46.2.tgz", "integrity": "sha512-ZyrsG4TIT9xnOlLsSSi9w/X29tCbK1yegE49RYm3tu3wF1L/B6LVMqnEWyDB26d9Ecx9zrmXCiPmIabVuLmNSg==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["freebsd"]}, "node_modules/@rollup/rollup-freebsd-x64": {"version": "4.46.2", "resolved": "https://registry.npmmirror.com/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.46.2.tgz", "integrity": "sha512-pCgHFoOECwVCJ5GFq8+gR8SBKnMO+xe5UEqbemxBpCKYQddRQMgomv1104RnLSg7nNvgKy05sLsY51+OVRyiVw==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["freebsd"]}, "node_modules/@rollup/rollup-linux-arm-gnueabihf": {"version": "4.46.2", "resolved": "https://registry.npmmirror.com/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.46.2.tgz", "integrity": "sha512-EtP8aquZ0xQg0ETFcxUbU71MZlHaw9MChwrQzatiE8U/bvi5uv/oChExXC4mWhjiqK7azGJBqU0tt5H123SzVA==", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-arm-musleabihf": {"version": "4.46.2", "resolved": "https://registry.npmmirror.com/@rollup/rollup-linux-arm-musleabihf/-/rollup-linux-arm-musleabihf-4.46.2.tgz", "integrity": "sha512-qO7F7U3u1nfxYRPM8HqFtLd+raev2K137dsV08q/LRKRLEc7RsiDWihUnrINdsWQxPR9jqZ8DIIZ1zJJAm5PjQ==", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-arm64-gnu": {"version": "4.46.2", "resolved": "https://registry.npmmirror.com/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.46.2.tgz", "integrity": "sha512-3dRaqLfcOXYsfvw5xMrxAk9Lb1f395gkoBYzSFcc/scgRFptRXL9DOaDpMiehf9CO8ZDRJW2z45b6fpU5nwjng==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-arm64-musl": {"version": "4.46.2", "resolved": "https://registry.npmmirror.com/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.46.2.tgz", "integrity": "sha512-fhHFTutA7SM+IrR6lIfiHskxmpmPTJUXpWIsBXpeEwNgZzZZSg/q4i6FU4J8qOGyJ0TR+wXBwx/L7Ho9z0+uDg==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-loongarch64-gnu": {"version": "4.46.2", "resolved": "https://registry.npmmirror.com/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.46.2.tgz", "integrity": "sha512-i7wfGFXu8x4+FRqPymzjD+Hyav8l95UIZ773j7J7zRYc3Xsxy2wIn4x+llpunexXe6laaO72iEjeeGyUFmjKeA==", "cpu": ["loong64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-ppc64-gnu": {"version": "4.46.2", "resolved": "https://registry.npmmirror.com/@rollup/rollup-linux-ppc64-gnu/-/rollup-linux-ppc64-gnu-4.46.2.tgz", "integrity": "sha512-B/l0dFcHVUnqcGZWKcWBSV2PF01YUt0Rvlurci5P+neqY/yMKchGU8ullZvIv5e8Y1C6wOn+U03mrDylP5q9Yw==", "cpu": ["ppc64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-riscv64-gnu": {"version": "4.46.2", "resolved": "https://registry.npmmirror.com/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.46.2.tgz", "integrity": "sha512-32k4ENb5ygtkMwPMucAb8MtV8olkPT03oiTxJbgkJa7lJ7dZMr0GCFJlyvy+K8iq7F/iuOr41ZdUHaOiqyR3iQ==", "cpu": ["riscv64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-riscv64-musl": {"version": "4.46.2", "resolved": "https://registry.npmmirror.com/@rollup/rollup-linux-riscv64-musl/-/rollup-linux-riscv64-musl-4.46.2.tgz", "integrity": "sha512-t5B2loThlFEauloaQkZg9gxV05BYeITLvLkWOkRXogP4qHXLkWSbSHKM9S6H1schf/0YGP/qNKtiISlxvfmmZw==", "cpu": ["riscv64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-s390x-gnu": {"version": "4.46.2", "resolved": "https://registry.npmmirror.com/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.46.2.tgz", "integrity": "sha512-YKjekwTEKgbB7n17gmODSmJVUIvj8CX7q5442/CK80L8nqOUbMtf8b01QkG3jOqyr1rotrAnW6B/qiHwfcuWQA==", "cpu": ["s390x"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-x64-gnu": {"version": "4.46.2", "resolved": "https://registry.npmmirror.com/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.46.2.tgz", "integrity": "sha512-Jj5a9RUoe5ra+MEyERkDKLwTXVu6s3aACP51nkfnK9wJTraCC8IMe3snOfALkrjTYd2G1ViE1hICj0fZ7ALBPA==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-x64-musl": {"version": "4.46.2", "resolved": "https://registry.npmmirror.com/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.46.2.tgz", "integrity": "sha512-7kX69DIrBeD7yNp4A5b81izs8BqoZkCIaxQaOpumcJ1S/kmqNFjPhDu1LHeVXv0SexfHQv5cqHsxLOjETuqDuA==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-win32-arm64-msvc": {"version": "4.46.2", "resolved": "https://registry.npmmirror.com/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.46.2.tgz", "integrity": "sha512-wiJWMIpeaak/jsbaq2HMh/rzZxHVW1rU6coyeNNpMwk5isiPjSTx0a4YLSlYDwBH/WBvLz+EtsNqQScZTLJy3g==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"]}, "node_modules/@rollup/rollup-win32-ia32-msvc": {"version": "4.46.2", "resolved": "https://registry.npmmirror.com/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.46.2.tgz", "integrity": "sha512-gBgaUDESVzMgWZhcyjfs9QFK16D8K6QZpwAaVNJxYDLHWayOta4ZMjGm/vsAEy3hvlS2GosVFlBlP9/Wb85DqQ==", "cpu": ["ia32"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"]}, "node_modules/@rollup/rollup-win32-x64-msvc": {"version": "4.46.2", "resolved": "https://registry.npmmirror.com/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.46.2.tgz", "integrity": "sha512-CvUo2ixeIQGtF6WvuB87XWqPQkoFAFqW+HUo/WzHwuHDvIwZCtjdWXoYCcr06iKGydiqTclC4jU/TNObC/xKZg==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"]}, "node_modules/@swc/helpers": {"version": "0.5.17", "resolved": "https://registry.npmmirror.com/@swc/helpers/-/helpers-0.5.17.tgz", "integrity": "sha512-5IKx/Y13RsYd+sauPb2x+U/xZikHjolzfuDgTAl/Tdf3Q8rslRvC19NKDLgAJQ6wsqADk10ntlv08nPFw/gO/A==", "license": "Apache-2.0", "dependencies": {"tslib": "^2.8.0"}}, "node_modules/@tauri-apps/api": {"version": "2.7.0", "resolved": "https://registry.npmmirror.com/@tauri-apps/api/-/api-2.7.0.tgz", "integrity": "sha512-v7fVE8jqBl8xJFOcBafDzXFc8FnicoH3j8o8DNNs0tHuEBmXUDqrCOAzMRX0UkfpwqZLqvrvK0GNQ45DfnoVDg==", "license": "Apache-2.0 OR MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/tauri"}}, "node_modules/@tauri-apps/cli": {"version": "2.7.1", "resolved": "https://registry.npmmirror.com/@tauri-apps/cli/-/cli-2.7.1.tgz", "integrity": "sha512-RcGWR4jOUEl92w3uvI0h61Llkfj9lwGD1iwvDRD2isMrDhOzjeeeVn9aGzeW1jubQ/kAbMYfydcA4BA0Cy733Q==", "dev": true, "license": "Apache-2.0 OR MIT", "bin": {"tauri": "tauri.js"}, "engines": {"node": ">= 10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/tauri"}, "optionalDependencies": {"@tauri-apps/cli-darwin-arm64": "2.7.1", "@tauri-apps/cli-darwin-x64": "2.7.1", "@tauri-apps/cli-linux-arm-gnueabihf": "2.7.1", "@tauri-apps/cli-linux-arm64-gnu": "2.7.1", "@tauri-apps/cli-linux-arm64-musl": "2.7.1", "@tauri-apps/cli-linux-riscv64-gnu": "2.7.1", "@tauri-apps/cli-linux-x64-gnu": "2.7.1", "@tauri-apps/cli-linux-x64-musl": "2.7.1", "@tauri-apps/cli-win32-arm64-msvc": "2.7.1", "@tauri-apps/cli-win32-ia32-msvc": "2.7.1", "@tauri-apps/cli-win32-x64-msvc": "2.7.1"}}, "node_modules/@tauri-apps/cli-darwin-arm64": {"version": "2.7.1", "resolved": "https://registry.npmmirror.com/@tauri-apps/cli-darwin-arm64/-/cli-darwin-arm64-2.7.1.tgz", "integrity": "sha512-j2NXQN6+08G03xYiyKDKqbCV2Txt+hUKg0a8hYr92AmoCU8fgCjHyva/p16lGFGUG3P2Yu0xiNe1hXL9ZuRMzA==", "cpu": ["arm64"], "dev": true, "license": "Apache-2.0 OR MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">= 10"}}, "node_modules/@tauri-apps/cli-darwin-x64": {"version": "2.7.1", "resolved": "https://registry.npmmirror.com/@tauri-apps/cli-darwin-x64/-/cli-darwin-x64-2.7.1.tgz", "integrity": "sha512-CdYAefeM35zKsc91qIyKzbaO7FhzTyWKsE8hj7tEJ1INYpoh1NeNNyL/NSEA3Nebi5ilugioJ5tRK8ZXG8y3gw==", "cpu": ["x64"], "dev": true, "license": "Apache-2.0 OR MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">= 10"}}, "node_modules/@tauri-apps/cli-linux-arm-gnueabihf": {"version": "2.7.1", "resolved": "https://registry.npmmirror.com/@tauri-apps/cli-linux-arm-gnueabihf/-/cli-linux-arm-gnueabihf-2.7.1.tgz", "integrity": "sha512-dnvyJrTA1UJxJjQ8q1N/gWomjP8Twij1BUQu2fdcT3OPpqlrbOk5R1yT0oD/721xoKNjroB5BXCsmmlykllxNg==", "cpu": ["arm"], "dev": true, "license": "Apache-2.0 OR MIT", "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@tauri-apps/cli-linux-arm64-gnu": {"version": "2.7.1", "resolved": "https://registry.npmmirror.com/@tauri-apps/cli-linux-arm64-gnu/-/cli-linux-arm64-gnu-2.7.1.tgz", "integrity": "sha512-FtBW6LJPNRTws3qyUc294AqCWU91l/H0SsFKq6q4Q45MSS4x6wxLxou8zB53tLDGEPx3JSoPLcDaSfPlSbyujQ==", "cpu": ["arm64"], "dev": true, "license": "Apache-2.0 OR MIT", "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@tauri-apps/cli-linux-arm64-musl": {"version": "2.7.1", "resolved": "https://registry.npmmirror.com/@tauri-apps/cli-linux-arm64-musl/-/cli-linux-arm64-musl-2.7.1.tgz", "integrity": "sha512-/HXY0t4FHkpFzjeYS5c16mlA6z0kzn5uKLWptTLTdFSnYpr8FCnOP4Sdkvm2TDQPF2ERxXtNCd+WR/jQugbGnA==", "cpu": ["arm64"], "dev": true, "license": "Apache-2.0 OR MIT", "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@tauri-apps/cli-linux-riscv64-gnu": {"version": "2.7.1", "resolved": "https://registry.npmmirror.com/@tauri-apps/cli-linux-riscv64-gnu/-/cli-linux-riscv64-gnu-2.7.1.tgz", "integrity": "sha512-GeW5lVI2GhhnaYckiDzstG2j2Jwlud5d2XefRGwlOK+C/bVGLT1le8MNPYK8wgRlpeK8fG1WnJJYD6Ke7YQ8bg==", "cpu": ["riscv64"], "dev": true, "license": "Apache-2.0 OR MIT", "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@tauri-apps/cli-linux-x64-gnu": {"version": "2.7.1", "resolved": "https://registry.npmmirror.com/@tauri-apps/cli-linux-x64-gnu/-/cli-linux-x64-gnu-2.7.1.tgz", "integrity": "sha512-DprxKQkPxIPYwUgg+cscpv2lcIUhn2nxEPlk0UeaiV9vATxCXyytxr1gLcj3xgjGyNPlM0MlJyYaPy1JmRg1cA==", "cpu": ["x64"], "dev": true, "license": "Apache-2.0 OR MIT", "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@tauri-apps/cli-linux-x64-musl": {"version": "2.7.1", "resolved": "https://registry.npmmirror.com/@tauri-apps/cli-linux-x64-musl/-/cli-linux-x64-musl-2.7.1.tgz", "integrity": "sha512-KLlq3kOK7OUyDR757c0zQjPULpGZpLhNB0lZmZpHXvoOUcqZoCXJHh4dT/mryWZJp5ilrem5l8o9ngrDo0X1AA==", "cpu": ["x64"], "dev": true, "license": "Apache-2.0 OR MIT", "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@tauri-apps/cli-win32-arm64-msvc": {"version": "2.7.1", "resolved": "https://registry.npmmirror.com/@tauri-apps/cli-win32-arm64-msvc/-/cli-win32-arm64-msvc-2.7.1.tgz", "integrity": "sha512-dH7KUjKkSypCeWPiainHyXoES3obS+JIZVoSwSZfKq2gWgs48FY3oT0hQNYrWveE+VR4VoR3b/F3CPGbgFvksA==", "cpu": ["arm64"], "dev": true, "license": "Apache-2.0 OR MIT", "optional": true, "os": ["win32"], "engines": {"node": ">= 10"}}, "node_modules/@tauri-apps/cli-win32-ia32-msvc": {"version": "2.7.1", "resolved": "https://registry.npmmirror.com/@tauri-apps/cli-win32-ia32-msvc/-/cli-win32-ia32-msvc-2.7.1.tgz", "integrity": "sha512-1oeibfyWQPVcijOrTg709qhbXArjX3x1MPjrmA5anlygwrbByxLBcLXvotcOeULFcnH2FYUMMLLant8kgvwE5A==", "cpu": ["ia32"], "dev": true, "license": "Apache-2.0 OR MIT", "optional": true, "os": ["win32"], "engines": {"node": ">= 10"}}, "node_modules/@tauri-apps/cli-win32-x64-msvc": {"version": "2.7.1", "resolved": "https://registry.npmmirror.com/@tauri-apps/cli-win32-x64-msvc/-/cli-win32-x64-msvc-2.7.1.tgz", "integrity": "sha512-D7Q9kDObutuirCNLxYQ7KAg2Xxg99AjcdYz/KuMw5HvyEPbkC9Q7JL0vOrQOrHEHxIQ2lYzFOZvKKoC2yyqXcg==", "cpu": ["x64"], "dev": true, "license": "Apache-2.0 OR MIT", "optional": true, "os": ["win32"], "engines": {"node": ">= 10"}}, "node_modules/@tauri-apps/plugin-dialog": {"version": "2.3.2", "resolved": "https://registry.npmmirror.com/@tauri-apps/plugin-dialog/-/plugin-dialog-2.3.2.tgz", "integrity": "sha512-c<PERSON>o9YeQSC0MF4IgXnotHsqEgJk72MBZLXmQPrLA95qTaaWiiaFQ38hIMdZ6YbGUNkr3oni3EhU+AD5jLHcdUA==", "license": "MIT OR Apache-2.0", "dependencies": {"@tauri-apps/api": "^2.6.0"}}, "node_modules/@tauri-apps/plugin-fs": {"version": "2.4.1", "resolved": "https://registry.npmmirror.com/@tauri-apps/plugin-fs/-/plugin-fs-2.4.1.tgz", "integrity": "sha512-vJlKZVGF3UAFGoIEVT6Oq5L4HGDCD78WmA4uhzitToqYiBKWAvZR61M6zAyQzHqLs0ADemkE4RSy/5sCmZm6ZQ==", "license": "MIT OR Apache-2.0", "dependencies": {"@tauri-apps/api": "^2.6.0"}}, "node_modules/@tauri-apps/plugin-opener": {"version": "2.4.0", "resolved": "https://registry.npmmirror.com/@tauri-apps/plugin-opener/-/plugin-opener-2.4.0.tgz", "integrity": "sha512-43VyN8JJtvKWJY72WI/KNZszTpDpzHULFxQs0CJBIYUdCRowQ6Q1feWTDb979N7nldqSuDOaBupZ6wz2nvuWwQ==", "license": "MIT OR Apache-2.0", "dependencies": {"@tauri-apps/api": "^2.6.0"}}, "node_modules/@types/babel__core": {"version": "7.20.5", "resolved": "https://registry.npmmirror.com/@types/babel__core/-/babel__core-7.20.5.tgz", "integrity": "sha512-qoQprZvz5wQFJwMDqeseRXWv3rqMvhgpbXFfVyWhbx9X47POIA6i/+dXefEmZKoAgOaTdaIgNSMqMIU61yRyzA==", "dev": true, "license": "MIT", "dependencies": {"@babel/parser": "^7.20.7", "@babel/types": "^7.20.7", "@types/babel__generator": "*", "@types/babel__template": "*", "@types/babel__traverse": "*"}}, "node_modules/@types/babel__generator": {"version": "7.27.0", "resolved": "https://registry.npmmirror.com/@types/babel__generator/-/babel__generator-7.27.0.tgz", "integrity": "sha512-ufFd2Xi92OAVPYsy+P4n7/U7e68fex0+Ee8gSG9KX7eo084CWiQ4sdxktvdl0bOPupXtVJPY19zk6EwWqUQ8lg==", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.0.0"}}, "node_modules/@types/babel__template": {"version": "7.4.4", "resolved": "https://registry.npmmirror.com/@types/babel__template/-/babel__template-7.4.4.tgz", "integrity": "sha512-h/NUaSyG5EyxBIp8YRxo4RMe2/qQgvyowRwVMzhYhBCONbW8PUsg4lkFMrhgZhUe5z3L3MiLDuvyJ/CaPa2A8A==", "dev": true, "license": "MIT", "dependencies": {"@babel/parser": "^7.1.0", "@babel/types": "^7.0.0"}}, "node_modules/@types/babel__traverse": {"version": "7.28.0", "resolved": "https://registry.npmmirror.com/@types/babel__traverse/-/babel__traverse-7.28.0.tgz", "integrity": "sha512-8PvcXf70gTDZBgt9ptxJ8elBeBjcLOAcOtoO/mPJjtji1+CdGbHgm77om1GrsPxsiE+uXIpNSK64UYaIwQXd4Q==", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.28.2"}}, "node_modules/@types/estree": {"version": "1.0.8", "resolved": "https://registry.npmmirror.com/@types/estree/-/estree-1.0.8.tgz", "integrity": "sha512-dWHzHa2WqEXI/O1E9OjrocMTKJl2mSrEolh1Iomrv6U+JuNwaHXsXx9bLu5gG7BUWFIN0skIQJQ/L1rIex4X6w==", "dev": true, "license": "MIT"}, "node_modules/@types/prop-types": {"version": "15.7.15", "resolved": "https://registry.npmmirror.com/@types/prop-types/-/prop-types-15.7.15.tgz", "integrity": "sha512-F6bEyamV9jKGAFBEmlQnesRPGOQqS2+Uwi0Em15xenOxHaf2hv6L8YCVn3rPdPJOiJfPiCnLIRyvwVaqMY3MIw==", "license": "MIT"}, "node_modules/@types/react": {"version": "18.3.23", "resolved": "https://registry.npmmirror.com/@types/react/-/react-18.3.23.tgz", "integrity": "sha512-/LDXMQh55EzZQ0uVAZmKKhfENivEvWz6E+EYzh+/MCjMhNsotd+ZHhBGIjFDTi6+fz0OhQQQLbTgdQIxxCsC0w==", "license": "MIT", "dependencies": {"@types/prop-types": "*", "csstype": "^3.0.2"}}, "node_modules/@types/react-dom": {"version": "18.3.7", "resolved": "https://registry.npmmirror.com/@types/react-dom/-/react-dom-18.3.7.tgz", "integrity": "sha512-MEe3UeoENYVFXzoXEWsvcpg6ZvlrFNlOQ7EOsvhI3CfAXwzPfO8Qwuxd40nepsYKqyyVQnTdEfv68q91yLcKrQ==", "license": "MIT", "peerDependencies": {"@types/react": "^18.0.0"}}, "node_modules/@vitejs/plugin-react": {"version": "4.7.0", "resolved": "https://registry.npmmirror.com/@vitejs/plugin-react/-/plugin-react-4.7.0.tgz", "integrity": "sha512-gUu9hwfWvvEDBBmgtAowQCojwZmJ5mcLn3aufeCsitijs3+f2NsrPtlAWIR6OPiqljl96GVCUbLe0HyqIpVaoA==", "dev": true, "license": "MIT", "dependencies": {"@babel/core": "^7.28.0", "@babel/plugin-transform-react-jsx-self": "^7.27.1", "@babel/plugin-transform-react-jsx-source": "^7.27.1", "@rolldown/pluginutils": "1.0.0-beta.27", "@types/babel__core": "^7.20.5", "react-refresh": "^0.17.0"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "peerDependencies": {"vite": "^4.2.0 || ^5.0.0 || ^6.0.0 || ^7.0.0"}}, "node_modules/browserslist": {"version": "4.25.1", "resolved": "https://registry.npmmirror.com/browserslist/-/browserslist-4.25.1.tgz", "integrity": "sha512-KGj0KoOMXLpSNkkEI6Z6mShmQy0bc1I+T7K9N81k4WWMrfz+6fQ6es80B/YLAeRoKvjYE1YSHHOW1qe9xIVzHw==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"caniuse-lite": "^1.0.30001726", "electron-to-chromium": "^1.5.173", "node-releases": "^2.0.19", "update-browserslist-db": "^1.1.3"}, "bin": {"browserslist": "cli.js"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}}, "node_modules/caniuse-lite": {"version": "1.0.30001731", "resolved": "https://registry.npmmirror.com/caniuse-lite/-/caniuse-lite-1.0.30001731.tgz", "integrity": "sha512-lDdp2/wrOmTRWuoB5DpfNkC0rJDU8DqRa6nYL6HK6sytw70QMopt/NIc/9SM7ylItlBWfACXk0tEn37UWM/+mg==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/caniuse-lite"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "CC-BY-4.0"}, "node_modules/convert-source-map": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/convert-source-map/-/convert-source-map-2.0.0.tgz", "integrity": "sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==", "dev": true, "license": "MIT"}, "node_modules/csstype": {"version": "3.1.3", "resolved": "https://registry.npmmirror.com/csstype/-/csstype-3.1.3.tgz", "integrity": "sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==", "license": "MIT"}, "node_modules/debug": {"version": "4.4.1", "resolved": "https://registry.npmmirror.com/debug/-/debug-4.4.1.tgz", "integrity": "sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/dom-helpers": {"version": "5.2.1", "resolved": "https://registry.npmmirror.com/dom-helpers/-/dom-helpers-5.2.1.tgz", "integrity": "sha512-nRCa7CK3VTrM2NmGkIy4cbK7IZlgBE/PYMn55rrXefr5xXDP0LdtfPnblFDoVdcAfslJ7or6iqAUnx0CCGIWQA==", "license": "MIT", "dependencies": {"@babel/runtime": "^7.8.7", "csstype": "^3.0.2"}}, "node_modules/electron-to-chromium": {"version": "1.5.194", "resolved": "https://registry.npmmirror.com/electron-to-chromium/-/electron-to-chromium-1.5.194.tgz", "integrity": "sha512-SdnWJwSUot04UR51I2oPD8kuP2VI37/CADR1OHsFOUzZIvfWJBO6q11k5P/uKNyTT3cdOsnyjkrZ+DDShqYqJA==", "dev": true, "license": "ISC"}, "node_modules/embla-carousel": {"version": "8.6.0", "resolved": "https://registry.npmmirror.com/embla-carousel/-/embla-carousel-8.6.0.tgz", "integrity": "sha512-SjWyZBHJPbqxHOzckOfo8lHisEaJWmwd23XppYFYVh10bU66/Pn5tkVkbkCMZVdbUE5eTCI2nD8OyIP4Z+uwkA==", "license": "MIT"}, "node_modules/embla-carousel-autoplay": {"version": "8.6.0", "resolved": "https://registry.npmmirror.com/embla-carousel-autoplay/-/embla-carousel-autoplay-8.6.0.tgz", "integrity": "sha512-OBu5G3nwaSXkZCo1A6LTaFMZ8EpkYbwIaH+bPqdBnDGQ2fh4+NbzjXjs2SktoPNKCtflfVMc75njaDHOYXcrsA==", "license": "MIT", "peerDependencies": {"embla-carousel": "8.6.0"}}, "node_modules/embla-carousel-fade": {"version": "8.6.0", "resolved": "https://registry.npmmirror.com/embla-carousel-fade/-/embla-carousel-fade-8.6.0.tgz", "integrity": "sha512-qaYsx5mwCz72ZrjlsXgs1nKejSrW+UhkbOMwLgfRT7w2LtdEB03nPRI06GHuHv5ac2USvbEiX2/nAHctcDwvpg==", "license": "MIT", "peerDependencies": {"embla-carousel": "8.6.0"}}, "node_modules/esbuild": {"version": "0.25.8", "resolved": "https://registry.npmmirror.com/esbuild/-/esbuild-0.25.8.tgz", "integrity": "sha512-vVC0USHGtMi8+R4Kz8rt6JhEWLxsv9Rnu/lGYbPR8u47B+DCBksq9JarW0zOO7bs37hyOK1l2/oqtbciutL5+Q==", "dev": true, "hasInstallScript": true, "license": "MIT", "bin": {"esbuild": "bin/esbuild"}, "engines": {"node": ">=18"}, "optionalDependencies": {"@esbuild/aix-ppc64": "0.25.8", "@esbuild/android-arm": "0.25.8", "@esbuild/android-arm64": "0.25.8", "@esbuild/android-x64": "0.25.8", "@esbuild/darwin-arm64": "0.25.8", "@esbuild/darwin-x64": "0.25.8", "@esbuild/freebsd-arm64": "0.25.8", "@esbuild/freebsd-x64": "0.25.8", "@esbuild/linux-arm": "0.25.8", "@esbuild/linux-arm64": "0.25.8", "@esbuild/linux-ia32": "0.25.8", "@esbuild/linux-loong64": "0.25.8", "@esbuild/linux-mips64el": "0.25.8", "@esbuild/linux-ppc64": "0.25.8", "@esbuild/linux-riscv64": "0.25.8", "@esbuild/linux-s390x": "0.25.8", "@esbuild/linux-x64": "0.25.8", "@esbuild/netbsd-arm64": "0.25.8", "@esbuild/netbsd-x64": "0.25.8", "@esbuild/openbsd-arm64": "0.25.8", "@esbuild/openbsd-x64": "0.25.8", "@esbuild/openharmony-arm64": "0.25.8", "@esbuild/sunos-x64": "0.25.8", "@esbuild/win32-arm64": "0.25.8", "@esbuild/win32-ia32": "0.25.8", "@esbuild/win32-x64": "0.25.8"}}, "node_modules/escalade": {"version": "3.2.0", "resolved": "https://registry.npmmirror.com/escalade/-/escalade-3.2.0.tgz", "integrity": "sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/fdir": {"version": "6.4.6", "resolved": "https://registry.npmmirror.com/fdir/-/fdir-6.4.6.tgz", "integrity": "sha512-hiFoqpyZcfNm1yc4u8oWCf9A2c4D3QjCrks3zmoVKVxpQRzmPNar1hUJcBG2RQHvEVGDN+Jm81ZheVLAQMK6+w==", "dev": true, "license": "MIT", "peerDependencies": {"picomatch": "^3 || ^4"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}}, "node_modules/fsevents": {"version": "2.3.3", "resolved": "https://registry.npmmirror.com/fsevents/-/fsevents-2.3.3.tgz", "integrity": "sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==", "dev": true, "hasInstallScript": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}}, "node_modules/gensync": {"version": "1.0.0-beta.2", "resolved": "https://registry.npmmirror.com/gensync/-/gensync-1.0.0-beta.2.tgz", "integrity": "sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/js-tokens": {"version": "4.0.0", "resolved": "https://registry.npmmirror.com/js-tokens/-/js-tokens-4.0.0.tgz", "integrity": "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==", "license": "MIT"}, "node_modules/jsesc": {"version": "3.1.0", "resolved": "https://registry.npmmirror.com/jsesc/-/jsesc-3.1.0.tgz", "integrity": "sha512-/sM3dO2FOzXjKQhJuo0Q173wf2KOo8t4I8vHy6lF9poUp7bKT0/NHE8fPX23PwfhnykfqnC2xRxOnVw5XuGIaA==", "dev": true, "license": "MIT", "bin": {"jsesc": "bin/jsesc"}, "engines": {"node": ">=6"}}, "node_modules/json5": {"version": "2.2.3", "resolved": "https://registry.npmmirror.com/json5/-/json5-2.2.3.tgz", "integrity": "sha512-<PERSON>m<PERSON>e7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==", "dev": true, "license": "MIT", "bin": {"json5": "lib/cli.js"}, "engines": {"node": ">=6"}}, "node_modules/keyborg": {"version": "2.6.0", "resolved": "https://registry.npmmirror.com/keyborg/-/keyborg-2.6.0.tgz", "integrity": "sha512-o5kvLbuTF+o326CMVYpjlaykxqYP9DphFQZ2ZpgrvBouyvOxyEB7oqe8nOLFpiV5VCtz0D3pt8gXQYWpLpBnmA==", "license": "MIT"}, "node_modules/loose-envify": {"version": "1.4.0", "resolved": "https://registry.npmmirror.com/loose-envify/-/loose-envify-1.4.0.tgz", "integrity": "sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==", "license": "MIT", "dependencies": {"js-tokens": "^3.0.0 || ^4.0.0"}, "bin": {"loose-envify": "cli.js"}}, "node_modules/lru-cache": {"version": "5.1.1", "resolved": "https://registry.npmmirror.com/lru-cache/-/lru-cache-5.1.1.tgz", "integrity": "sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==", "dev": true, "license": "ISC", "dependencies": {"yallist": "^3.0.2"}}, "node_modules/monaco-editor": {"version": "0.52.2", "resolved": "https://registry.npmmirror.com/monaco-editor/-/monaco-editor-0.52.2.tgz", "integrity": "sha512-GEQWEZmfkOGLdd3XK8ryrfWz3AIP8YymVXiPHEdewrUq7mh0qrKrfHLNCXcbB6sTnMLnOZ3ztSiKcciFUkIJwQ==", "license": "MIT"}, "node_modules/ms": {"version": "2.1.3", "resolved": "https://registry.npmmirror.com/ms/-/ms-2.1.3.tgz", "integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==", "dev": true, "license": "MIT"}, "node_modules/nanoid": {"version": "3.3.11", "resolved": "https://registry.npmmirror.com/nanoid/-/nanoid-3.3.11.tgz", "integrity": "sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "node_modules/node-releases": {"version": "2.0.19", "resolved": "https://registry.npmmirror.com/node-releases/-/node-releases-2.0.19.tgz", "integrity": "sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw==", "dev": true, "license": "MIT"}, "node_modules/object-assign": {"version": "4.1.1", "resolved": "https://registry.npmmirror.com/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/picocolors": {"version": "1.1.1", "resolved": "https://registry.npmmirror.com/picocolors/-/picocolors-1.1.1.tgz", "integrity": "sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==", "dev": true, "license": "ISC"}, "node_modules/picomatch": {"version": "4.0.3", "resolved": "https://registry.npmmirror.com/picomatch/-/picomatch-4.0.3.tgz", "integrity": "sha512-5gTmgEY/sqK6gFXLIsQNH19lWb4ebPDLA4SdLP7dsWkIXHWlG66oPuVvXSGFPppYZz8ZDZq0dYYrbHfBCVUb1Q==", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/postcss": {"version": "8.5.6", "resolved": "https://registry.npmmirror.com/postcss/-/postcss-8.5.6.tgz", "integrity": "sha512-3Ybi1tAuwAP9s0r1UQ2J4n5Y0G05bJkpUIO0/bI9MhwmD70S5aTWbXGBwxHrelT+XM1k6dM0pk+SwNkpTRN7Pg==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"nanoid": "^3.3.11", "picocolors": "^1.1.1", "source-map-js": "^1.2.1"}, "engines": {"node": "^10 || ^12 || >=14"}}, "node_modules/prop-types": {"version": "15.8.1", "resolved": "https://registry.npmmirror.com/prop-types/-/prop-types-15.8.1.tgz", "integrity": "sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==", "license": "MIT", "dependencies": {"loose-envify": "^1.4.0", "object-assign": "^4.1.1", "react-is": "^16.13.1"}}, "node_modules/prop-types/node_modules/react-is": {"version": "16.13.1", "resolved": "https://registry.npmmirror.com/react-is/-/react-is-16.13.1.tgz", "integrity": "sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==", "license": "MIT"}, "node_modules/react": {"version": "18.3.1", "resolved": "https://registry.npmmirror.com/react/-/react-18.3.1.tgz", "integrity": "sha512-wS+hAgJShR0KhEvPJArfuPVN1+Hz1t0Y6n5jLrGQbkb4urgPE/0Rve+1kMB1v/oWgHgm4WIcV+i7F2pTVj+2iQ==", "license": "MIT", "dependencies": {"loose-envify": "^1.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/react-dom": {"version": "18.3.1", "resolved": "https://registry.npmmirror.com/react-dom/-/react-dom-18.3.1.tgz", "integrity": "sha512-5m4nQKp+rZRb09LNH59GM4BxTh9251/ylbKIbpe7TpGxfJ+9kv6BLkLBXIjjspbgbnIBNqlI23tRnTWT0snUIw==", "license": "MIT", "dependencies": {"loose-envify": "^1.1.0", "scheduler": "^0.23.2"}, "peerDependencies": {"react": "^18.3.1"}}, "node_modules/react-is": {"version": "17.0.2", "resolved": "https://registry.npmmirror.com/react-is/-/react-is-17.0.2.tgz", "integrity": "sha512-w2GsyukL62IJnlaff/nRegPQR94C/XXamvMWmSHRJ4y7Ts/4ocGRmTHvOs8PSE6pB3dWOrD/nueuU5sduBsQ4w==", "license": "MIT"}, "node_modules/react-refresh": {"version": "0.17.0", "resolved": "https://registry.npmmirror.com/react-refresh/-/react-refresh-0.17.0.tgz", "integrity": "sha512-z6F7K9bV85EfseRCp2bzrpyQ0Gkw1uLoCel9XBVWPg/TjRj94SkJzUTGfOa4bs7iJvBWtQG0Wq7wnI0syw3EBQ==", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/react-transition-group": {"version": "4.4.5", "resolved": "https://registry.npmmirror.com/react-transition-group/-/react-transition-group-4.4.5.tgz", "integrity": "sha512-pZcd1MCJoiKiBR2NRxeCRg13uCXbydPnmB4EOeRrY7480qNWO8IIgQG6zlDkm6uRMsURXPuKq0GWtiM59a5Q6g==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@babel/runtime": "^7.5.5", "dom-helpers": "^5.0.1", "loose-envify": "^1.4.0", "prop-types": "^15.6.2"}, "peerDependencies": {"react": ">=16.6.0", "react-dom": ">=16.6.0"}}, "node_modules/rollup": {"version": "4.46.2", "resolved": "https://registry.npmmirror.com/rollup/-/rollup-4.46.2.tgz", "integrity": "sha512-WMmLFI+Boh6xbop+OAGo9cQ3OgX9MIg7xOQjn+pTCwOkk+FNDAeAemXkJ3HzDJrVXleLOFVa1ipuc1AmEx1Dwg==", "dev": true, "license": "MIT", "dependencies": {"@types/estree": "1.0.8"}, "bin": {"rollup": "dist/bin/rollup"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "optionalDependencies": {"@rollup/rollup-android-arm-eabi": "4.46.2", "@rollup/rollup-android-arm64": "4.46.2", "@rollup/rollup-darwin-arm64": "4.46.2", "@rollup/rollup-darwin-x64": "4.46.2", "@rollup/rollup-freebsd-arm64": "4.46.2", "@rollup/rollup-freebsd-x64": "4.46.2", "@rollup/rollup-linux-arm-gnueabihf": "4.46.2", "@rollup/rollup-linux-arm-musleabihf": "4.46.2", "@rollup/rollup-linux-arm64-gnu": "4.46.2", "@rollup/rollup-linux-arm64-musl": "4.46.2", "@rollup/rollup-linux-loongarch64-gnu": "4.46.2", "@rollup/rollup-linux-ppc64-gnu": "4.46.2", "@rollup/rollup-linux-riscv64-gnu": "4.46.2", "@rollup/rollup-linux-riscv64-musl": "4.46.2", "@rollup/rollup-linux-s390x-gnu": "4.46.2", "@rollup/rollup-linux-x64-gnu": "4.46.2", "@rollup/rollup-linux-x64-musl": "4.46.2", "@rollup/rollup-win32-arm64-msvc": "4.46.2", "@rollup/rollup-win32-ia32-msvc": "4.46.2", "@rollup/rollup-win32-x64-msvc": "4.46.2", "fsevents": "~2.3.2"}}, "node_modules/rtl-css-js": {"version": "1.16.1", "resolved": "https://registry.npmmirror.com/rtl-css-js/-/rtl-css-js-1.16.1.tgz", "integrity": "sha512-lRQgou1mu19e+Ya0LsTvKrVJ5TYUbqCVPAiImX3UfLTenarvPUl1QFdvu5Z3PYmHT9RCcwIfbjRQBntExyj3Zg==", "license": "MIT", "dependencies": {"@babel/runtime": "^7.1.2"}}, "node_modules/scheduler": {"version": "0.23.2", "resolved": "https://registry.npmmirror.com/scheduler/-/scheduler-0.23.2.tgz", "integrity": "sha512-UOShsPwz7NrMUqhR6t0hWjFduvOzbtv7toDH1/hIrfRNIDBnnBWd0CwJTGvTpngVlmwGCdP9/Zl/tVrDqcuYzQ==", "license": "MIT", "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/semver": {"version": "6.3.1", "resolved": "https://registry.npmmirror.com/semver/-/semver-6.3.1.tgz", "integrity": "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/source-map-js": {"version": "1.2.1", "resolved": "https://registry.npmmirror.com/source-map-js/-/source-map-js-1.2.1.tgz", "integrity": "sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/state-local": {"version": "1.0.7", "resolved": "https://registry.npmmirror.com/state-local/-/state-local-1.0.7.tgz", "integrity": "sha512-HTEHMNieakEnoe33shBYcZ7NX83ACUjCu8c40iOGEZsngj9zRnkqS9j1pqQPXwobB0ZcVTk27REb7COQ0UR59w==", "license": "MIT"}, "node_modules/stylis": {"version": "4.3.6", "resolved": "https://registry.npmmirror.com/stylis/-/stylis-4.3.6.tgz", "integrity": "sha512-yQ3rwFWRfwNUY7H5vpU0wfdkNSnvnJinhF9830Swlaxl03zsOjCfmX0ugac+3LtK0lYSgwL/KXc8oYL3mG4YFQ==", "license": "MIT"}, "node_modules/tabster": {"version": "8.5.6", "resolved": "https://registry.npmmirror.com/tabster/-/tabster-8.5.6.tgz", "integrity": "sha512-2vfrRGrx8O9BjdrtSlVA5fvpmbq5HQBRN13XFRg6LAvZ1Fr3QdBnswgT4YgFS5Bhoo5nxwgjRaRueI2Us/dv7g==", "license": "MIT", "dependencies": {"keyborg": "2.6.0", "tslib": "^2.8.1"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.40.0"}}, "node_modules/tabster/node_modules/@rollup/rollup-linux-x64-gnu": {"version": "4.40.0", "resolved": "https://registry.npmmirror.com/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.40.0.tgz", "integrity": "sha512-RcDGMtqF9EFN8i2RYN2W+64CdHruJ5rPqrlYw+cgM3uOVPSsnAQps7cpjXe9be/yDp8UC7VLoCoKC8J3Kn2FkQ==", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/tinyglobby": {"version": "0.2.14", "resolved": "https://registry.npmmirror.com/tinyglobby/-/tinyglobby-0.2.14.tgz", "integrity": "sha512-tX5e7OM1HnYr2+a2C/4V0htOcSQcoSTH9KgJnVvNm5zm/cyEWKJ7j7YutsH9CxMdtOkkLFy2AHrMci9IM8IPZQ==", "dev": true, "license": "MIT", "dependencies": {"fdir": "^6.4.4", "picomatch": "^4.0.2"}, "engines": {"node": ">=12.0.0"}, "funding": {"url": "https://github.com/sponsors/SuperchupuDev"}}, "node_modules/tslib": {"version": "2.8.1", "resolved": "https://registry.npmmirror.com/tslib/-/tslib-2.8.1.tgz", "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==", "license": "0BSD"}, "node_modules/typescript": {"version": "5.6.3", "resolved": "https://registry.npmmirror.com/typescript/-/typescript-5.6.3.tgz", "integrity": "sha512-hjcS1mhfuyi4WW8IWtjP7brDrG2cuDZukyrYrSauoXGNgx0S7zceP07adYkJycEr56BOUTNPzbInooiN3fn1qw==", "dev": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=14.17"}}, "node_modules/update-browserslist-db": {"version": "1.1.3", "resolved": "https://registry.npmmirror.com/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz", "integrity": "sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"escalade": "^3.2.0", "picocolors": "^1.1.1"}, "bin": {"update-browserslist-db": "cli.js"}, "peerDependencies": {"browserslist": ">= 4.21.0"}}, "node_modules/use-disposable": {"version": "1.0.4", "resolved": "https://registry.npmmirror.com/use-disposable/-/use-disposable-1.0.4.tgz", "integrity": "sha512-j83t6AMLWUyb5zwlTDqf6dP9LezM9R0yTbI/b6olmdaGtCKQUe9pgJWV6dRaaQLcozypjIEp4EmZr2DkZGKLSg==", "license": "MIT", "peerDependencies": {"@types/react": ">=16.8.0 <19.0.0", "@types/react-dom": ">=16.8.0 <19.0.0", "react": ">=16.8.0 <19.0.0", "react-dom": ">=16.8.0 <19.0.0"}}, "node_modules/use-sync-external-store": {"version": "1.5.0", "resolved": "https://registry.npmmirror.com/use-sync-external-store/-/use-sync-external-store-1.5.0.tgz", "integrity": "sha512-Rb46I4cGGVBmjamjphe8L/UnvJD+uPPtTkNvX5mZgqdbavhI4EbgIWJiIHXJ8bc/i9EQGPRh4DwEURJ552Do0A==", "license": "MIT", "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}}, "node_modules/vite": {"version": "6.3.5", "resolved": "https://registry.npmmirror.com/vite/-/vite-6.3.5.tgz", "integrity": "sha512-cZn6NDFE7wdTpINgs++ZJ4N49W2vRp8LCKrn3Ob1kYNtOo21vfDoaV5GzBfLU4MovSAB8uNRm4jgzVQZ+mBzPQ==", "dev": true, "license": "MIT", "dependencies": {"esbuild": "^0.25.0", "fdir": "^6.4.4", "picomatch": "^4.0.2", "postcss": "^8.5.3", "rollup": "^4.34.9", "tinyglobby": "^0.2.13"}, "bin": {"vite": "bin/vite.js"}, "engines": {"node": "^18.0.0 || ^20.0.0 || >=22.0.0"}, "funding": {"url": "https://github.com/vitejs/vite?sponsor=1"}, "optionalDependencies": {"fsevents": "~2.3.3"}, "peerDependencies": {"@types/node": "^18.0.0 || ^20.0.0 || >=22.0.0", "jiti": ">=1.21.0", "less": "*", "lightningcss": "^1.21.0", "sass": "*", "sass-embedded": "*", "stylus": "*", "sugarss": "*", "terser": "^5.16.0", "tsx": "^4.8.1", "yaml": "^2.4.2"}, "peerDependenciesMeta": {"@types/node": {"optional": true}, "jiti": {"optional": true}, "less": {"optional": true}, "lightningcss": {"optional": true}, "sass": {"optional": true}, "sass-embedded": {"optional": true}, "stylus": {"optional": true}, "sugarss": {"optional": true}, "terser": {"optional": true}, "tsx": {"optional": true}, "yaml": {"optional": true}}}, "node_modules/yallist": {"version": "3.1.1", "resolved": "https://registry.npmmirror.com/yallist/-/yallist-3.1.1.tgz", "integrity": "sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==", "dev": true, "license": "ISC"}}}