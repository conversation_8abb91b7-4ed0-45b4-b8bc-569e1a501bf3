import { useCallback } from 'react';
import { TauriAPI } from '../../types/tauri-api';
import { ConfigManagerState, NotificationConfig } from '../../types/config';

/**
 * 配置列表管理Hook参数接口
 */
interface UseConfigListParams {
  state: ConfigManagerState;
  dispatch: (action: any) => void;
  showNotification: (config: NotificationConfig) => void;
  selectConfig: (name: string | null) => void;
  clearCurrentConfig: () => void;
}

/**
 * 配置列表管理Hook
 * 负责配置列表的加载、选择和基础管理
 */
export function useConfigList({ 
  state, 
  dispatch, 
  showNotification,
  selectConfig,
  clearCurrentConfig 
}: UseConfigListParams) {
  // 加载配置列表
  const loadConfigurations = useCallback(async () => {
    dispatch({ type: 'SET_CONFIGURATIONS_LOADING', payload: true });
    try {
      const configs = await <PERSON>ri<PERSON><PERSON>.listConfigurations();
      dispatch({ type: 'SET_CONFIGURATIONS', payload: configs || [] });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      dispatch({ type: 'SET_CONFIGURATIONS_ERROR', payload: errorMessage });
      showNotification({
        type: 'error',
        title: '加载配置列表失败',
        message: errorMessage,
      });
    } finally {
      dispatch({ type: 'SET_CONFIGURATIONS_LOADING', payload: false });
    }
  }, [dispatch, showNotification]);

  // 选择配置 - 增强隔离机制
  const selectConfiguration = useCallback((name: string | null) => {
    selectConfig(name);
  }, [selectConfig]);

  // 获取当前选中的配置信息
  const getSelectedConfigInfo = useCallback(() => {
    if (!state.selectedConfigName) return null;
    
    return state.configurations.find(config => 
      config.name === state.selectedConfigName
    ) || null;
  }, [state.selectedConfigName, state.configurations]);

  // 检查配置是否存在
  const isConfigExists = useCallback((name: string) => {
    return state.configurations.some(config => config.name === name);
  }, [state.configurations]);

  // 获取配置总数
  const getConfigCount = useCallback(() => {
    return state.configurations.length;
  }, [state.configurations]);

  return {
    loadConfigurations,
    selectConfiguration,
    clearCurrentConfig,
    getSelectedConfigInfo,
    isConfigExists,
    getConfigCount,
  };
}