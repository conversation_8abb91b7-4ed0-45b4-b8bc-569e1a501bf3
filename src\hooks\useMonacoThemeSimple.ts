import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useTheme } from './useTheme';
import { 
  MonacoTheme, 
  ThemeConfiguration, 
  ThemeInfo,
  MonacoEditorInstance,
  createMonacoError,
  isValidMonacoTheme
} from '../types/monaco';
// import { debounce } from '../utils/debounce'; // 未使用，暂时注释
import { globalEventManager, createSafeEventListener } from '../utils/eventManager';

/**
 * 默认主题配置
 */
const DEFAULT_THEME_CONFIG: ThemeConfiguration = {
  light: 'vs',
  dark: 'vs-dark',
  highContrast: {
    light: 'hc-light',
    dark: 'hc-black'
  }
};

/**
 * 简化的 Monaco Editor 主题管理钩子
 * 
 * 重构目标：
 * - 减少复杂度，提高可维护性
 * - 统一事件管理，防止内存泄漏
 * - 添加防抖机制，提高性能
 * - 简化API，更易使用
 */
export const useMonacoTheme = (themeConfig: Partial<ThemeConfiguration> = {}) => {
  const { themeMode } = useTheme();
  const [isThemeChanging, setIsThemeChanging] = useState(false);
  const configRef = useRef({ ...DEFAULT_THEME_CONFIG, ...themeConfig });
  const changeTimeoutRef = useRef<number | null>(null);
  
  // 更新配置引用
  configRef.current = { ...DEFAULT_THEME_CONFIG, ...themeConfig };

  /**
   * 计算Monaco主题 - 简化逻辑
   */
  const monacoTheme = useMemo<MonacoTheme>(() => {
    const config = configRef.current;
    
    try {
      switch (themeMode) {
        case 'light':
          return config.light;
        case 'dark':
          return config.dark;
        case 'system':
          const isSystemDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
          return isSystemDark ? config.dark : config.light;
        default:
          console.warn(`未知的主题模式: ${themeMode}, 回退到浅色主题`);
          return config.light;
      }
    } catch (error) {
      console.error('Monaco主题计算失败:', error);
      return 'vs'; // 错误恢复
    }
  }, [themeMode]);

  /**
   * 优化的主题状态管理
   */
  const themeStateManager = useCallback(() => {
    setIsThemeChanging(true);
    
    // 清理之前的定时器
    if (changeTimeoutRef.current) {
      clearTimeout(changeTimeoutRef.current);
    }
    
    // 短时间后重置状态，确保UI响应性
    changeTimeoutRef.current = setTimeout(() => {
      setIsThemeChanging(false);
      changeTimeoutRef.current = null;
    }, 100); // 减少延迟以提高响应性
  }, []);

  /**
   * 系统主题变化监听 - 统一事件处理
   */
  useEffect(() => {
    if (themeMode !== 'system') {
      globalEventManager.removeListeners('monaco-system-theme');
      return;
    }

    const handleSystemThemeChange = createSafeEventListener(() => {
      themeStateManager();
    });

    globalEventManager.addMediaQueryListener(
      'monaco-system-theme',
      '(prefers-color-scheme: dark)',
      handleSystemThemeChange
    );

    return () => {
      globalEventManager.removeListeners('monaco-system-theme');
    };
  }, [themeMode, themeStateManager]);

  /**
   * 全局主题变化事件监听 - 统一管理
   */
  useEffect(() => {
    const handleGlobalThemeChange = createSafeEventListener((event: CustomEvent) => {
      // 检查事件源，避免重复处理
      if (event.detail?.source === 'monaco') {
        return;
      }
      themeStateManager();
    });

    globalEventManager.addListener(
      'monaco-global-theme',
      window,
      'theme-changed',
      handleGlobalThemeChange
    );

    return () => {
      globalEventManager.removeListeners('monaco-global-theme');
    };
  }, [themeStateManager]);

  /**
   * 获取主题信息 - 简化实现
   */
  const getThemeInfo = useCallback((): ThemeInfo => {
    const isDark = monacoTheme.includes('dark') || monacoTheme.includes('hc-black');
    const isHighContrast = monacoTheme.startsWith('hc-');
    
    return {
      theme: monacoTheme,
      isDark,
      isLight: !isDark,
      isHighContrast,
      mode: themeMode,
      displayName: isHighContrast 
        ? (isDark ? '高对比度深色' : '高对比度浅色')
        : (isDark ? '深色主题' : '浅色主题')
    };
  }, [monacoTheme, themeMode]);

  /**
   * 应用主题到Monaco编辑器实例 - 优化性能和错误处理
   */
  const applyThemeToEditor = useCallback(async (editorInstance: MonacoEditorInstance | any) => {
    if (!editorInstance) {
      console.warn('编辑器实例为空，无法应用主题');
      return Promise.resolve();
    }

    // 防止频繁更新同样的主题
    try {
      const currentOptions = editorInstance.getOptions?.();
      if (currentOptions?.theme === monacoTheme) {
        return Promise.resolve();
      }
    } catch (error) {
      // 获取当前主题失败，继续执行更新
      console.debug('无法获取当前主题，继续更新:', error);
    }

    try {
      // 验证主题有效性
      if (!isValidMonacoTheme(monacoTheme)) {
        throw createMonacoError(`无效的Monaco主题: ${monacoTheme}`, 'INVALID_THEME');
      }

      // 批量更新编辑器选项，提高性能
      await new Promise<void>((resolve, reject) => {
        requestAnimationFrame(() => {
          try {
            editorInstance.updateOptions({ 
              theme: monacoTheme,
              // 可以添加其他主题相关选项
            });
            resolve();
          } catch (updateError) {
            const error = createMonacoError(
              '更新编辑器主题失败',
              'UPDATE_THEME_FAILED',
              { theme: monacoTheme, originalError: updateError }
            );
            console.error('应用Monaco主题失败:', error);
            reject(error);
          }
        });
      });
      
      // 触发主题应用成功事件
      window.dispatchEvent(new CustomEvent('monaco-theme-applied', {
        detail: { theme: monacoTheme, editorId: editorInstance.getId?.() }
      }));
      
    } catch (error) {
      console.error('应用Monaco主题失败:', error);
      // 尝试回退到默认主题
      try {
        editorInstance.updateOptions({ theme: DEFAULT_THEME_CONFIG.light });
        console.info('已回退到默认浅色主题');
      } catch (fallbackError) {
        console.error('主题回退也失败了:', fallbackError);
      }
      throw error;
    }
  }, [monacoTheme]);

  /**
   * 检查主题兼容性
   */
  const isThemeSupported = useCallback((theme: string): theme is MonacoTheme => {
    return isValidMonacoTheme(theme);
  }, []);

  // 组件卸载时清理 - 简化清理逻辑
  useEffect(() => {
    return () => {
      // 清理定时器
      if (changeTimeoutRef.current) {
        clearTimeout(changeTimeoutRef.current);
        changeTimeoutRef.current = null;
      }
      
      // 清理事件监听器
      globalEventManager.removeListeners('monaco-system-theme');
      globalEventManager.removeListeners('monaco-global-theme');
    };
  }, []);

  // 计算便捷属性
  const themeInfo = getThemeInfo();

  return {
    /** 当前Monaco主题 */
    monacoTheme,
    
    /** 主题是否正在变化中 */
    isThemeChanging,
    
    /** 获取详细主题信息 */
    getThemeInfo,
    
    /** 应用主题到编辑器实例 */
    applyThemeToEditor,
    
    /** 检查主题兼容性 */
    isThemeSupported,
    
    // 便捷属性
    /** 是否为深色主题 */
    isDark: themeInfo.isDark,
    
    /** 是否为浅色主题 */
    isLight: themeInfo.isLight,
    
    /** 是否为高对比度主题 */
    isHighContrast: themeInfo.isHighContrast
  };
};

/**
 * 清理所有Monaco主题相关资源
 */
export const cleanupMonacoTheme = () => {
  globalEventManager.removeListeners('monaco-system-theme');
  globalEventManager.removeListeners('monaco-global-theme');
};