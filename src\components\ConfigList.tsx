import React, { useState, useCallback } from 'react';
import { ConfigInfo } from '../types/config';

interface ConfigListProps {
  configurations: ConfigInfo[];
  selectedConfig: string | null;
  activeConfig: string | null;
  isLoading: boolean;
  error: string | null;
  onSelect: (configName: string) => void;
  onEdit: (configName: string) => void;
  onRename: (configName: string) => void;
  onDelete: (configName: string) => void;
  onActivate: (configName: string) => void;
  onRefresh: () => void;
  onCreateNew: () => void;
  className?: string;
}

export const ConfigList: React.FC<ConfigListProps> = ({
  configurations,
  selectedConfig,
  activeConfig,
  isLoading,
  error,
  onSelect,
  onEdit,
  onRename,
  onDelete,
  onActivate,
  onRefresh,
  onCreateNew,
  className = '',
}) => {
  const [hoveredConfig, setHoveredConfig] = useState<string | null>(null);

  // 格式化文件大小
  const formatSize = useCallback((bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  }, []);

  // 格式化日期
  const formatDate = useCallback((dateString: string): string => {
    try {
      const date = new Date(dateString);
      const now = new Date();
      const diffTime = Math.abs(now.getTime() - date.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      
      if (diffDays === 1) {
        return '今天 ' + date.toLocaleTimeString('zh-CN', { 
          hour: '2-digit', 
          minute: '2-digit' 
        });
      } else if (diffDays <= 7) {
        return `${diffDays} 天前`;
      } else {
        return date.toLocaleDateString('zh-CN');
      }
    } catch {
      return dateString;
    }
  }, []);

  // 处理配置项点击
  const handleConfigClick = useCallback((configName: string) => {
    onSelect(configName);
  }, [onSelect]);

  // 处理菜单操作
  const handleMenuAction = useCallback((action: string, configName: string, event: React.MouseEvent) => {
    event.stopPropagation();
    
    switch (action) {
      case 'edit':
        onEdit(configName);
        break;
      case 'rename':
        onRename(configName);
        break;
      case 'delete':
        onDelete(configName);
        break;
      case 'activate':
        onActivate(configName);
        break;
    }
  }, [onEdit, onRename, onDelete, onActivate]);

  return (
    <div className={`config-list ${className}`}>
      <div className="config-list-header">
        <div className="header-left">
          <h3>配置文件列表</h3>
          <span className="config-count">
            {configurations.length} 个配置
          </span>
        </div>
        <div className="header-actions">
          <button
            type="button"
            className="refresh-button"
            onClick={onRefresh}
            disabled={isLoading}
            title="刷新列表"
          >
            {isLoading ? '⟳' : '↻'}
          </button>
          <button
            type="button"
            className="create-button primary"
            onClick={onCreateNew}
            disabled={isLoading}
          >
            + 新建配置
          </button>
        </div>
      </div>

      <div className="config-list-content">
        {error && (
          <div className="error-message">
            <span className="error-icon">⚠️</span>
            <span>{error}</span>
            <button
              type="button"
              className="retry-button"
              onClick={onRefresh}
            >
              重试
            </button>
          </div>
        )}

        {isLoading && configurations.length === 0 && (
          <div className="loading-message">
            <div className="loading-spinner"></div>
            <span>加载配置列表中...</span>
          </div>
        )}

        {!isLoading && !error && configurations.length === 0 && (
          <div className="empty-message">
            <div className="empty-icon">📄</div>
            <h4>暂无配置文件</h4>
            <p>点击"新建配置"创建您的第一个配置文件</p>
            <button
              type="button"
              className="create-button primary"
              onClick={onCreateNew}
            >
              + 新建配置
            </button>
          </div>
        )}

        {configurations.length > 0 && (
          <div className="config-items">
            {configurations.map((config) => (
              <div
                key={config.name}
                className={`config-item ${
                  selectedConfig === config.name ? 'selected' : ''
                } ${
                  activeConfig === config.name ? 'active' : ''
                } ${
                  !config.is_valid ? 'invalid' : ''
                }`}
                onClick={() => handleConfigClick(config.name)}
                onMouseEnter={() => setHoveredConfig(config.name)}
                onMouseLeave={() => setHoveredConfig(null)}
              >
                <div className="config-main">
                  <div className="config-header">
                    <div className="config-name">
                      <span className="name-text">{config.name}</span>
                      {activeConfig === config.name && (
                        <span className="active-badge">已激活</span>
                      )}
                      {!config.is_valid && (
                        <span className="invalid-badge">格式错误</span>
                      )}
                    </div>
                    <div className="config-status">
                      {config.is_valid ? (
                        <span className="valid-icon" title="JSON格式正确">✓</span>
                      ) : (
                        <span className="invalid-icon" title="JSON格式错误">✗</span>
                      )}
                    </div>
                  </div>

                  <div className="config-meta">
                    <span className="meta-item">
                      <span className="meta-label">大小:</span>
                      <span className="meta-value">{formatSize(config.size)}</span>
                    </span>
                    <span className="meta-item">
                      <span className="meta-label">修改:</span>
                      <span className="meta-value">{formatDate(config.last_modified)}</span>
                    </span>
                  </div>
                </div>

                <div className="config-actions">
                  {(hoveredConfig === config.name || selectedConfig === config.name) && (
                    <>
                      <button
                        type="button"
                        className="action-button edit"
                        onClick={(e) => handleMenuAction('edit', config.name, e)}
                        title="编辑配置"
                      >
                        ✏️
                      </button>
                      <button
                        type="button"
                        className="action-button activate"
                        onClick={(e) => handleMenuAction('activate', config.name, e)}
                        title="激活配置"
                        disabled={!config.is_valid}
                      >
                        ▶️
                      </button>
                      <button
                        type="button"
                        className="action-button rename"
                        onClick={(e) => handleMenuAction('rename', config.name, e)}
                        title="重命名"
                      >
                        📝
                      </button>
                      <button
                        type="button"
                        className="action-button delete"
                        onClick={(e) => handleMenuAction('delete', config.name, e)}
                        title="删除配置"
                      >
                        🗑️
                      </button>
                    </>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      <style jsx>{`
        .config-list {
          background: white;
          border: 1px solid #e0e0e0;
          border-radius: 8px;
          overflow: hidden;
        }

        .config-list-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 16px 20px;
          background: #f8f9fa;
          border-bottom: 1px solid #e0e0e0;
        }

        .header-left {
          display: flex;
          align-items: center;
          gap: 12px;
        }

        .header-left h3 {
          margin: 0;
          color: #333;
          font-size: 16px;
          font-weight: 600;
        }

        .config-count {
          background: #e9ecef;
          color: #6c757d;
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: 500;
        }

        .header-actions {
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .refresh-button {
          background: transparent;
          border: 1px solid #ddd;
          border-radius: 4px;
          width: 32px;
          height: 32px;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 16px;
          color: #666;
        }

        .refresh-button:hover {
          background: #f5f5f5;
          border-color: #ccc;
        }

        .refresh-button:disabled {
          cursor: not-allowed;
          opacity: 0.6;
        }

        .create-button {
          background: #28a745;
          color: white;
          border: none;
          border-radius: 4px;
          padding: 6px 12px;
          cursor: pointer;
          font-size: 14px;
          font-weight: 500;
        }

        .create-button:hover {
          background: #218838;
        }

        .create-button:disabled {
          background: #6c757d;
          cursor: not-allowed;
        }

        .config-list-content {
          max-height: 400px;
          overflow-y: auto;
        }

        .error-message {
          padding: 20px;
          background: #fff5f5;
          border-bottom: 1px solid #feb2b2;
          color: #c53030;
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .error-icon {
          flex-shrink: 0;
        }

        .retry-button {
          background: #c53030;
          color: white;
          border: none;
          border-radius: 4px;
          padding: 4px 8px;
          cursor: pointer;
          font-size: 12px;
          margin-left: auto;
        }

        .retry-button:hover {
          background: #9c2626;
        }

        .loading-message {
          padding: 40px 20px;
          text-align: center;
          color: #666;
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 12px;
        }

        .loading-spinner {
          width: 24px;
          height: 24px;
          border: 3px solid #f3f3f3;
          border-top: 3px solid #007bff;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        .empty-message {
          padding: 40px 20px;
          text-align: center;
          color: #666;
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 12px;
        }

        .empty-icon {
          font-size: 48px;
          opacity: 0.5;
        }

        .empty-message h4 {
          margin: 0;
          color: #333;
          font-size: 18px;
        }

        .empty-message p {
          margin: 0;
          font-size: 14px;
          color: #666;
        }

        .config-items {
          display: flex;
          flex-direction: column;
        }

        .config-item {
          display: flex;
          align-items: center;
          padding: 12px 20px;
          border-bottom: 1px solid #f0f0f0;
          cursor: pointer;
          transition: all 0.2s ease;
          position: relative;
        }

        .config-item:hover {
          background: #f8f9fa;
        }

        .config-item.selected {
          background: #e3f2fd;
          border-left: 4px solid #2196f3;
        }

        .config-item.active {
          background: #f1f8e9;
          border-left: 4px solid #4caf50;
        }

        .config-item.invalid {
          background: #fff5f5;
          border-left: 4px solid #f44336;
        }

        .config-item:last-child {
          border-bottom: none;
        }

        .config-main {
          flex: 1;
          display: flex;
          flex-direction: column;
          gap: 4px;
        }

        .config-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
        }

        .config-name {
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .name-text {
          font-weight: 500;
          color: #333;
          font-size: 14px;
        }

        .active-badge {
          background: #4caf50;
          color: white;
          padding: 2px 6px;
          border-radius: 10px;
          font-size: 10px;
          font-weight: 500;
        }

        .invalid-badge {
          background: #f44336;
          color: white;
          padding: 2px 6px;
          border-radius: 10px;
          font-size: 10px;
          font-weight: 500;
        }

        .config-status {
          display: flex;
          align-items: center;
        }

        .valid-icon {
          color: #4caf50;
          font-weight: bold;
        }

        .invalid-icon {
          color: #f44336;
          font-weight: bold;
        }

        .config-meta {
          display: flex;
          align-items: center;
          gap: 16px;
          font-size: 12px;
          color: #666;
        }

        .meta-item {
          display: flex;
          align-items: center;
          gap: 4px;
        }

        .meta-label {
          color: #999;
        }

        .meta-value {
          font-weight: 500;
        }

        .config-actions {
          display: flex;
          align-items: center;
          gap: 4px;
          margin-left: 12px;
        }

        .action-button {
          background: transparent;
          border: none;
          border-radius: 4px;
          width: 28px;
          height: 28px;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          transition: all 0.2s ease;
        }

        .action-button:hover {
          background: rgba(0, 0, 0, 0.1);
        }

        .action-button:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        .action-button.edit:hover {
          background: rgba(33, 150, 243, 0.1);
        }

        .action-button.activate:hover {
          background: rgba(76, 175, 80, 0.1);
        }

        .action-button.rename:hover {
          background: rgba(255, 152, 0, 0.1);
        }

        .action-button.delete:hover {
          background: rgba(244, 67, 54, 0.1);
        }

        /* Dark mode */
        @media (prefers-color-scheme: dark) {
          .config-list {
            background: #2d3748;
            border-color: #4a5568;
          }

          .config-list-header {
            background: #1a202c;
            border-color: #4a5568;
          }

          .header-left h3 {
            color: #e2e8f0;
          }

          .config-count {
            background: #4a5568;
            color: #a0aec0;
          }

          .refresh-button {
            border-color: #4a5568;
            color: #a0aec0;
          }

          .refresh-button:hover {
            background: #4a5568;
          }

          .config-item {
            border-color: #4a5568;
          }

          .config-item:hover {
            background: #4a5568;
          }

          .config-item.selected {
            background: #2c5282;
          }

          .config-item.active {
            background: #2f855a;
          }

          .config-item.invalid {
            background: #742a2a;
          }

          .name-text {
            color: #e2e8f0;
          }

          .config-meta {
            color: #a0aec0;
          }

          .meta-label {
            color: #718096;
          }

          .loading-message,
          .empty-message {
            color: #a0aec0;
          }

          .empty-message h4 {
            color: #e2e8f0;
          }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
          .config-list-header {
            padding: 12px 16px;
          }

          .header-left {
            flex-direction: column;
            align-items: flex-start;
            gap: 4px;
          }

          .config-item {
            padding: 12px 16px;
          }

          .config-meta {
            flex-direction: column;
            align-items: flex-start;
            gap: 4px;
          }

          .config-actions {
            flex-direction: column;
            gap: 2px;
          }
        }
      `}</style>
    </div>
  );
};