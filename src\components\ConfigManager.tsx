import React, { useState, useCallback } from 'react';
import { useConfig } from '../hooks/useConfig';
import { DirectorySelector } from './DirectorySelector';
import { ConfigList } from './ConfigList';
import { ConfigEditor } from './ConfigEditor';
import { ConfigNameValidator } from '../utils/validation';

interface ConfigManagerProps {
  className?: string;
}

export const ConfigManager: React.FC<ConfigManagerProps> = ({ className = '' }) => {
  const {
    state,
    loadAppConfig,
    saveAppConfig,
    selectTargetDirectory,
    loadConfigurations,
    createConfiguration,
    readConfiguration,
    updateConfiguration,
    renameConfiguration,
    deleteConfiguration,
    validateJson,
    formatJson,
    activateConfiguration,
    updateCurrentConfigContent,
    selectConfiguration,
    clearCurrentConfig,
    setCreatingNew,
    setRenaming,
    showDeleteConfirm,
    hideDeleteConfirm,
  } = useConfig();

  // 本地状态
  const [newConfigName, setNewConfigName] = useState('');
  const [renameConfigName, setRenameConfigName] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);

  // 处理新建配置
  const handleCreateNew = useCallback(() => {
    setCreatingNew(true);
    setNewConfigName('');
    clearCurrentConfig();
  }, [setCreatingNew, clearCurrentConfig]);

  // 处理创建配置提交
  const handleCreateSubmit = useCallback(async () => {
    if (!newConfigName.trim()) return;

    const validation = ConfigNameValidator.validate(newConfigName);
    if (!validation.isValid) {
      alert(`配置名称无效:\n${validation.errors.join('\n')}`);
      return;
    }

    const finalName = newConfigName.endsWith('.json') ? newConfigName : `${newConfigName}.json`;
    const existingNames = state.configurations.map(c => c.name);
    
    if (existingNames.includes(finalName)) {
      alert('配置文件名已存在，请选择其他名称');
      return;
    }

    setIsProcessing(true);
    try {
      const defaultContent = JSON.stringify({
        name: newConfigName,
        version: "1.0.0",
        settings: {}
      }, null, 2);

      await createConfiguration(finalName, defaultContent);
      await readConfiguration(finalName);
      setCreatingNew(false);
      setNewConfigName('');
    } catch (error) {
      console.error('创建配置失败:', error);
    } finally {
      setIsProcessing(false);
    }
  }, [newConfigName, state.configurations, createConfiguration, readConfiguration, setCreatingNew]);

  // 处理取消创建
  const handleCreateCancel = useCallback(() => {
    setCreatingNew(false);
    setNewConfigName('');
  }, [setCreatingNew]);

  // 处理编辑配置
  const handleEdit = useCallback(async (configName: string) => {
    try {
      await readConfiguration(configName);
      selectConfiguration(configName);
    } catch (error) {
      console.error('读取配置失败:', error);
    }
  }, [readConfiguration, selectConfiguration]);

  // 处理重命名
  const handleRename = useCallback((configName: string) => {
    setRenaming(true);
    setRenameConfigName(configName.replace('.json', ''));
  }, [setRenaming]);

  // 处理重命名提交
  const handleRenameSubmit = useCallback(async () => {
    if (!renameConfigName.trim() || !state.selectedConfigName) return;

    const validation = ConfigNameValidator.validate(renameConfigName);
    if (!validation.isValid) {
      alert(`配置名称无效:\n${validation.errors.join('\n')}`);
      return;
    }

    const newName = renameConfigName.endsWith('.json') ? renameConfigName : `${renameConfigName}.json`;
    const existingNames = state.configurations.map(c => c.name);
    
    if (existingNames.includes(newName) && newName !== state.selectedConfigName) {
      alert('配置文件名已存在，请选择其他名称');
      return;
    }

    setIsProcessing(true);
    try {
      await renameConfiguration(state.selectedConfigName, newName);
      if (state.currentConfig && state.currentConfig.name === state.selectedConfigName) {
        // 更新当前编辑的配置名称
        await readConfiguration(newName);
      }
      selectConfiguration(newName);
      setRenaming(false);
      setRenameConfigName('');
    } catch (error) {
      console.error('重命名配置失败:', error);
    } finally {
      setIsProcessing(false);
    }
  }, [renameConfigName, state.selectedConfigName, state.currentConfig, state.configurations, renameConfiguration, readConfiguration, selectConfiguration, setRenaming]);

  // 处理取消重命名
  const handleRenameCancel = useCallback(() => {
    setRenaming(false);
    setRenameConfigName('');
  }, [setRenaming]);

  // 处理删除确认
  const handleDeleteConfirm = useCallback(async () => {
    if (!state.deleteConfigName) return;

    setIsProcessing(true);
    try {
      await deleteConfiguration(state.deleteConfigName);
      if (state.selectedConfigName === state.deleteConfigName) {
        selectConfiguration(null);
        clearCurrentConfig();
      }
      hideDeleteConfirm();
    } catch (error) {
      console.error('删除配置失败:', error);
    } finally {
      setIsProcessing(false);
    }
  }, [state.deleteConfigName, state.selectedConfigName, deleteConfiguration, selectConfiguration, clearCurrentConfig, hideDeleteConfirm]);

  // 处理保存配置
  const handleSaveConfig = useCallback(async (name: string, content: string) => {
    await updateConfiguration(name, content);
  }, [updateConfiguration]);

  // 处理关闭编辑器
  const handleCloseEditor = useCallback(() => {
    if (state.currentConfig?.isModified) {
      if (confirm('配置已修改但未保存，确定要关闭吗？')) {
        clearCurrentConfig();
      }
    } else {
      clearCurrentConfig();
    }
  }, [state.currentConfig, clearCurrentConfig]);

  // 处理激活配置
  const handleActivate = useCallback(async (configName: string) => {
    if (!state.appConfig?.target_directory) {
      alert('请先设置目标目录');
      return;
    }

    const config = state.configurations.find(c => c.name === configName);
    if (!config?.is_valid) {
      alert('配置文件格式错误，无法激活');
      return;
    }

    try {
      await activateConfiguration(configName);
    } catch (error) {
      console.error('激活配置失败:', error);
    }
  }, [state.appConfig, state.configurations, activateConfiguration]);

  return (
    <div className={`config-manager ${className}`}>
      <div className="config-manager-header">
        <h1>配置管理器</h1>
        <p>管理和切换不同的JSON配置文件</p>
      </div>

      <div className="config-manager-content">
        {/* 目标目录设置 */}
        <DirectorySelector
          appConfig={state.appConfig}
          onConfigUpdate={saveAppConfig}
          onSelectDirectory={selectTargetDirectory}
        />

        <div className="main-layout">
          {/* 左侧配置列表 */}
          <div className="sidebar">
            <ConfigList
              configurations={state.configurations}
              selectedConfig={state.selectedConfigName}
              activeConfig={state.appConfig?.last_active_config || null}
              isLoading={state.isLoadingConfigurations}
              error={state.configurationsError}
              onSelect={selectConfiguration}
              onEdit={handleEdit}
              onRename={handleRename}
              onDelete={showDeleteConfirm}
              onActivate={handleActivate}
              onRefresh={loadConfigurations}
              onCreateNew={handleCreateNew}
            />
          </div>

          {/* 右侧编辑器 */}
          <div className="main-content">
            {state.isCreatingNew && (
              <div className="create-config-panel">
                <div className="panel-header">
                  <h3>创建新配置</h3>
                </div>
                <div className="panel-content">
                  <div className="form-group">
                    <label htmlFor="newConfigName">配置名称:</label>
                    <input
                      id="newConfigName"
                      type="text"
                      value={newConfigName}
                      onChange={(e) => setNewConfigName(e.target.value)}
                      placeholder="输入配置文件名称"
                      autoFocus
                    />
                    <small className="form-hint">
                      将自动添加 .json 扩展名
                    </small>
                  </div>
                  <div className="form-actions">
                    <button
                      type="button"
                      className="create-button primary"
                      onClick={handleCreateSubmit}
                      disabled={!newConfigName.trim() || isProcessing}
                    >
                      {isProcessing ? '创建中...' : '创建'}
                    </button>
                    <button
                      type="button"
                      className="cancel-button"
                      onClick={handleCreateCancel}
                      disabled={isProcessing}
                    >
                      取消
                    </button>
                  </div>
                </div>
              </div>
            )}

            {state.isRenaming && (
              <div className="rename-config-panel">
                <div className="panel-header">
                  <h3>重命名配置</h3>
                </div>
                <div className="panel-content">
                  <div className="form-group">
                    <label htmlFor="renameConfigName">新名称:</label>
                    <input
                      id="renameConfigName"
                      type="text"
                      value={renameConfigName}
                      onChange={(e) => setRenameConfigName(e.target.value)}
                      placeholder="输入新的配置文件名称"
                      autoFocus
                    />
                    <small className="form-hint">
                      将自动添加 .json 扩展名
                    </small>
                  </div>
                  <div className="form-actions">
                    <button
                      type="button"
                      className="rename-button primary"
                      onClick={handleRenameSubmit}
                      disabled={!renameConfigName.trim() || isProcessing}
                    >
                      {isProcessing ? '重命名中...' : '重命名'}
                    </button>
                    <button
                      type="button"
                      className="cancel-button"
                      onClick={handleRenameCancel}
                      disabled={isProcessing}
                    >
                      取消
                    </button>
                  </div>
                </div>
              </div>
            )}

            {state.currentConfig && !state.isCreatingNew && !state.isRenaming && (
              <ConfigEditor
                configName={state.currentConfig.name}
                content={state.currentConfig.content}
                isModified={state.currentConfig.isModified}
                validationResult={state.currentConfig.validationResult}
                isSaving={state.isSaving}
                onContentChange={updateCurrentConfigContent}
                onSave={handleSaveConfig}
                onValidate={validateJson}
                onFormat={formatJson}
                onClose={handleCloseEditor}
              />
            )}

            {!state.currentConfig && !state.isCreatingNew && !state.isRenaming && (
              <div className="welcome-panel">
                <div className="welcome-content">
                  <h3>欢迎使用配置管理器</h3>
                  <p>选择左侧的配置进行编辑，或创建新的配置文件。</p>
                  <div className="welcome-actions">
                    <button
                      type="button"
                      className="create-button primary"
                      onClick={handleCreateNew}
                    >
                      + 创建新配置
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 删除确认对话框 */}
      {state.showDeleteConfirm && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h4>确认删除</h4>
            </div>
            <div className="modal-body">
              <p>
                确定要删除配置文件 <strong>{state.deleteConfigName}</strong> 吗？
              </p>
              <p className="warning-text">
                ⚠️ 此操作无法撤销，请谨慎操作。
              </p>
            </div>
            <div className="modal-actions">
              <button
                type="button"
                className="delete-button danger"
                onClick={handleDeleteConfirm}
                disabled={isProcessing}
              >
                {isProcessing ? '删除中...' : '确认删除'}
              </button>
              <button
                type="button"
                className="cancel-button"
                onClick={hideDeleteConfirm}
                disabled={isProcessing}
              >
                取消
              </button>
            </div>
          </div>
        </div>
      )}

      <style jsx>{`
        .config-manager {
          display: flex;
          flex-direction: column;
          height: 100vh;
          background: #fafafa;
        }

        .config-manager-header {
          padding: 20px;
          background: white;
          border-bottom: 1px solid #e0e0e0;
          text-align: center;
        }

        .config-manager-header h1 {
          margin: 0 0 8px 0;
          color: #333;
          font-size: 24px;
          font-weight: 600;
        }

        .config-manager-header p {
          margin: 0;
          color: #666;
          font-size: 14px;
        }

        .config-manager-content {
          flex: 1;
          padding: 20px;
          overflow: hidden;
          display: flex;
          flex-direction: column;
          gap: 20px;
        }

        .main-layout {
          flex: 1;
          display: flex;
          gap: 20px;
          overflow: hidden;
        }

        .sidebar {
          width: 350px;
          flex-shrink: 0;
        }

        .main-content {
          flex: 1;
          overflow: hidden;
        }

        .create-config-panel,
        .rename-config-panel {
          background: white;
          border: 1px solid #e0e0e0;
          border-radius: 8px;
          overflow: hidden;
        }

        .panel-header {
          padding: 16px 20px;
          background: #f8f9fa;
          border-bottom: 1px solid #e0e0e0;
        }

        .panel-header h3 {
          margin: 0;
          color: #333;
          font-size: 16px;
          font-weight: 600;
        }

        .panel-content {
          padding: 20px;
        }

        .form-group {
          margin-bottom: 20px;
        }

        .form-group label {
          display: block;
          margin-bottom: 8px;
          color: #333;
          font-weight: 500;
          font-size: 14px;
        }

        .form-group input {
          width: 100%;
          padding: 10px 12px;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 14px;
          outline: none;
          box-sizing: border-box;
        }

        .form-group input:focus {
          border-color: #007bff;
          box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        .form-hint {
          display: block;
          margin-top: 4px;
          color: #666;
          font-size: 12px;
        }

        .form-actions {
          display: flex;
          gap: 8px;
        }

        .create-button,
        .rename-button,
        .delete-button,
        .cancel-button {
          padding: 8px 16px;
          border: 1px solid #ddd;
          border-radius: 4px;
          cursor: pointer;
          font-size: 14px;
          font-weight: 500;
        }

        .create-button.primary,
        .rename-button.primary {
          background: #007bff;
          color: white;
          border-color: #007bff;
        }

        .create-button.primary:hover,
        .rename-button.primary:hover {
          background: #0056b3;
          border-color: #0056b3;
        }

        .delete-button.danger {
          background: #dc3545;
          color: white;
          border-color: #dc3545;
        }

        .delete-button.danger:hover {
          background: #c82333;
          border-color: #c82333;
        }

        .cancel-button {
          background: #6c757d;
          color: white;
          border-color: #6c757d;
        }

        .cancel-button:hover {
          background: #545b62;
          border-color: #545b62;
        }

        .create-button:disabled,
        .rename-button:disabled,
        .delete-button:disabled,
        .cancel-button:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        .welcome-panel {
          background: white;
          border: 1px solid #e0e0e0;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          height: 100%;
        }

        .welcome-content {
          text-align: center;
          max-width: 400px;
          padding: 40px 20px;
        }

        .welcome-content h3 {
          margin: 0 0 16px 0;
          color: #333;
          font-size: 20px;
          font-weight: 600;
        }

        .welcome-content p {
          margin: 0 0 24px 0;
          color: #666;
          font-size: 16px;
          line-height: 1.5;
        }

        .welcome-actions .create-button {
          padding: 12px 24px;
          font-size: 16px;
        }

        .modal-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 1000;
        }

        .modal-content {
          background: white;
          border-radius: 8px;
          min-width: 400px;
          max-width: 500px;
          overflow: hidden;
        }

        .modal-header {
          padding: 16px 20px;
          background: #f8f9fa;
          border-bottom: 1px solid #e0e0e0;
        }

        .modal-header h4 {
          margin: 0;
          color: #333;
          font-size: 16px;
          font-weight: 600;
        }

        .modal-body {
          padding: 20px;
        }

        .modal-body p {
          margin: 0 0 12px 0;
          color: #333;
          font-size: 14px;
          line-height: 1.5;
        }

        .modal-body p:last-child {
          margin-bottom: 0;
        }

        .warning-text {
          color: #dc3545;
          font-weight: 500;
        }

        .modal-actions {
          padding: 16px 20px;
          background: #f8f9fa;
          border-top: 1px solid #e0e0e0;
          display: flex;
          justify-content: flex-end;
          gap: 8px;
        }

        /* Dark mode */
        @media (prefers-color-scheme: dark) {
          .config-manager {
            background: #1a202c;
          }

          .config-manager-header {
            background: #2d3748;
            border-color: #4a5568;
          }

          .config-manager-header h1 {
            color: #e2e8f0;
          }

          .config-manager-header p {
            color: #a0aec0;
          }

          .create-config-panel,
          .rename-config-panel,
          .welcome-panel,
          .modal-content {
            background: #2d3748;
            border-color: #4a5568;
          }

          .panel-header,
          .modal-header,
          .modal-actions {
            background: #1a202c;
            border-color: #4a5568;
          }

          .panel-header h3,
          .modal-header h4 {
            color: #e2e8f0;
          }

          .form-group label {
            color: #e2e8f0;
          }

          .form-group input {
            background: #4a5568;
            border-color: #718096;
            color: #e2e8f0;
          }

          .form-group input:focus {
            border-color: #4299e1;
            box-shadow: 0 0 0 2px rgba(66, 153, 225, 0.25);
          }

          .form-hint {
            color: #a0aec0;
          }

          .welcome-content h3 {
            color: #e2e8f0;
          }

          .welcome-content p {
            color: #a0aec0;
          }

          .modal-body p {
            color: #e2e8f0;
          }

          .warning-text {
            color: #fc8181;
          }
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
          .main-layout {
            flex-direction: column;
          }

          .sidebar {
            width: 100%;
          }

          .main-content {
            height: 500px;
          }
        }

        @media (max-width: 768px) {
          .config-manager-content {
            padding: 10px;
          }

          .config-manager-header {
            padding: 15px;
          }

          .config-manager-header h1 {
            font-size: 20px;
          }

          .modal-content {
            min-width: 300px;
            margin: 20px;
          }
        }
      `}</style>
    </div>
  );
};