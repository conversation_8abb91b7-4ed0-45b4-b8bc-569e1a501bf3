use std::path::Path;
use std::fs;
use serde::{Deserialize, Serialize};
use crate::error_handler::<PERSON>rror<PERSON><PERSON><PERSON>;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct FileInfo {
    pub name: String,
    pub path: String,
    pub size: u64,
    pub is_file: bool,
    pub is_dir: bool,
    pub modified: String,
}

pub struct FileOperations;

impl FileOperations {
    // 检查路径是否存在
    pub fn path_exists(path: &str) -> bool {
        Path::new(path).exists()
    }

    // 检查路径是否为目录
    pub fn is_directory(path: &str) -> bool {
        Path::new(path).is_dir()
    }

    // 检查路径是否为文件
    pub fn is_file(path: &str) -> bool {
        Path::new(path).is_file()
    }

    // 创建目录（递归）
    pub fn create_directory(path: &str) -> Result<(), String> {
        fs::create_dir_all(path)
            .map_err(|e| ErrorHandler::directory_create_error(path, &e.to_string()).to_string())
    }

    // 读取文件内容
    pub fn read_file(path: &str) -> Result<String, String> {
        fs::read_to_string(path)
            .map_err(|e| ErrorHandler::file_read_error(path, &e.to_string()).to_string())
    }

    // 写入文件内容
    pub fn write_file(path: &str, content: &str) -> Result<(), String> {
        // 确保父目录存在
        if let Some(parent) = Path::new(path).parent() {
            Self::create_directory(parent.to_str().unwrap_or(""))?;
        }

        fs::write(path, content)
            .map_err(|e| ErrorHandler::file_write_error(path, &e.to_string()).to_string())
    }

    // 复制文件
    pub fn copy_file(source: &str, destination: &str) -> Result<(), String> {
        // 确保目标目录存在
        if let Some(parent) = Path::new(destination).parent() {
            Self::create_directory(parent.to_str().unwrap_or(""))?;
        }

        fs::copy(source, destination)
            .map(|_| ())
            .map_err(|e| ErrorHandler::file_operation_error("复制", &e.to_string()).to_string())
    }

    // 移动/重命名文件
    pub fn move_file(source: &str, destination: &str) -> Result<(), String> {
        // 确保目标目录存在
        if let Some(parent) = Path::new(destination).parent() {
            Self::create_directory(parent.to_str().unwrap_or(""))?;
        }

        fs::rename(source, destination)
            .map_err(|e| ErrorHandler::file_operation_error("移动", &e.to_string()).to_string())
    }

    // 删除文件
    pub fn delete_file(path: &str) -> Result<(), String> {
        fs::remove_file(path)
            .map_err(|e| format!("删除文件失败: {}", e))
    }

    // 删除目录（递归）
    pub fn delete_directory(path: &str) -> Result<(), String> {
        fs::remove_dir_all(path)
            .map_err(|e| format!("删除目录失败: {}", e))
    }

    // 列出目录内容
    pub fn list_directory(path: &str) -> Result<Vec<FileInfo>, String> {
        let entries = fs::read_dir(path)
            .map_err(|e| format!("读取目录失败: {}", e))?;

        let mut files = Vec::new();

        for entry in entries {
            let entry = entry.map_err(|e| format!("读取目录项失败: {}", e))?;
            let path = entry.path();
            let metadata = entry.metadata()
                .map_err(|e| format!("获取文件元数据失败: {}", e))?;

            let name = path.file_name()
                .and_then(|n| n.to_str())
                .ok_or("无效的文件名")?
                .to_string();

            let path_str = path.to_str()
                .ok_or("无效的文件路径")?
                .to_string();

            let modified = chrono::DateTime::<chrono::Utc>::from(metadata.modified()
                .map_err(|e| format!("获取修改时间失败: {}", e))?)
                .format("%Y-%m-%d %H:%M:%S")
                .to_string();

            files.push(FileInfo {
                name,
                path: path_str,
                size: metadata.len(),
                is_file: metadata.is_file(),
                is_dir: metadata.is_dir(),
                modified,
            });
        }

        // 按名称排序
        files.sort_by(|a, b| a.name.cmp(&b.name));

        Ok(files)
    }

    // 获取文件大小
    pub fn get_file_size(path: &str) -> Result<u64, String> {
        let metadata = fs::metadata(path)
            .map_err(|e| format!("获取文件元数据失败: {}", e))?;
        Ok(metadata.len())
    }

    // 获取文件修改时间
    pub fn get_file_modified_time(path: &str) -> Result<String, String> {
        let metadata = fs::metadata(path)
            .map_err(|e| format!("获取文件元数据失败: {}", e))?;

        let modified = chrono::DateTime::<chrono::Utc>::from(metadata.modified()
            .map_err(|e| format!("获取修改时间失败: {}", e))?)
            .format("%Y-%m-%d %H:%M:%S")
            .to_string();

        Ok(modified)
    }

    // 获取父目录路径
    pub fn get_parent_directory(path: &str) -> Option<String> {
        Path::new(path).parent()
            .and_then(|p| p.to_str())
            .map(|s| s.to_string())
    }

    // 获取文件名（不包含路径）
    pub fn get_file_name(path: &str) -> Option<String> {
        Path::new(path).file_name()
            .and_then(|n| n.to_str())
            .map(|s| s.to_string())
    }

    // 获取文件扩展名
    pub fn get_file_extension(path: &str) -> Option<String> {
        Path::new(path).extension()
            .and_then(|ext| ext.to_str())
            .map(|s| s.to_string())
    }

    // 连接路径
    pub fn join_path(base: &str, path: &str) -> String {
        Path::new(base).join(path)
            .to_str()
            .unwrap_or("")
            .to_string()
    }

    // 规范化路径
    pub fn normalize_path(path: &str) -> String {
        let path = Path::new(path);
        path.to_str().unwrap_or("").to_string()
    }

    // 检查文件是否可读
    pub fn is_readable(path: &str) -> bool {
        match fs::File::open(path) {
            Ok(_) => true,
            Err(_) => false,
        }
    }

    // 检查文件是否可写
    pub fn is_writable(path: &str) -> bool {
        let path_obj = Path::new(path);
        
        if path_obj.exists() {
            // 如果文件或目录存在，检查其权限
            if path_obj.is_file() {
                // 对于文件，尝试以追加模式打开（不会修改内容）
                match fs::OpenOptions::new().append(true).open(path) {
                    Ok(_) => true,
                    Err(_) => false,
                }
            } else if path_obj.is_dir() {
                // 对于目录，使用更安全的权限检查
                Self::check_directory_writable(path)
            } else {
                false
            }
        } else {
            // 如果路径不存在，检查父目录是否可写
            if let Some(parent) = path_obj.parent() {
                if parent.exists() && parent.is_dir() {
                    Self::check_directory_writable(parent.to_str().unwrap_or(""))
                } else {
                    false
                }
            } else {
                false
            }
        }
    }
    
    // 安全检查目录是否可写的辅助函数
    fn check_directory_writable(dir_path: &str) -> bool {
        use std::time::{SystemTime, UNIX_EPOCH};
        
        // 生成唯一的临时文件名，避免冲突
        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_nanos();
        let temp_file = format!("{}/temp_write_test_{}", dir_path, timestamp);
        
        // 尝试创建临时文件来测试写权限
        match fs::OpenOptions::new()
            .create_new(true)  // 确保不覆盖已存在的文件
            .write(true)
            .open(&temp_file) 
        {
            Ok(_) => {
                // 立即清理临时文件
                let _ = fs::remove_file(&temp_file);
                true
            },
            Err(_) => false,
        }
    }

    // 安全删除文件（移动到回收站或临时位置）
    pub fn safe_delete_file(path: &str) -> Result<String, String> {
        if !Self::path_exists(path) {
            return Err("文件不存在".to_string());
        }

        // 创建临时删除目录
        let temp_dir = std::env::temp_dir().join("ClaudeSwitch_deleted");
        Self::create_directory(temp_dir.to_str().unwrap_or(""))?;

        // 生成唯一的备份文件名
        let timestamp = chrono::Utc::now().format("%Y%m%d_%H%M%S").to_string();
        let file_name = Self::get_file_name(path).unwrap_or("unknown".to_string());
        let backup_name = format!("{}_{}", timestamp, file_name);
        let backup_path = temp_dir.join(backup_name);

        // 移动文件到临时位置
        Self::move_file(path, backup_path.to_str().unwrap_or(""))?;

        Ok(backup_path.to_str().unwrap_or("").to_string())
    }

    // 创建空文件
    pub fn create_empty_file(path: &str) -> Result<(), String> {
        Self::write_file(path, "")
    }

    // 获取文件的绝对路径
    pub fn get_absolute_path(path: &str) -> Result<String, String> {
        let path_buf = Path::new(path).canonicalize()
            .map_err(|e| format!("获取绝对路径失败: {}", e))?;
        
        Ok(path_buf.to_str().unwrap_or("").to_string())
    }
}