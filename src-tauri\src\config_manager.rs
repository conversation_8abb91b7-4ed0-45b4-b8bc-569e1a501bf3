use serde::{Deserialize, Serialize};
use std::path::{Path, PathBuf};
use std::fs;
use dirs;
use uuid::Uuid;
use chrono::{DateTime, Utc};

// 应用配置结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppConfig {
    pub target_directory: String,
    pub last_active_config: Option<String>,
    pub app_settings: AppSettings,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AppSettings {
    pub auto_backup: bool,
    pub confirm_delete: bool,
}

// 配置文件信息结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConfigInfo {
    pub name: String,
    pub is_valid: bool,
    pub last_modified: String,
    pub size: u64,
}

impl Default for AppConfig {
    fn default() -> Self {
        Self {
            target_directory: String::new(),
            last_active_config: None,
            app_settings: AppSettings::default(),
        }
    }
}

impl Default for AppSettings {
    fn default() -> Self {
        Self {
            auto_backup: true,
            confirm_delete: true,
        }
    }
}

pub struct ConfigManager {
    app_data_dir: PathBuf,
    config_dir: PathBuf,
    backup_dir: PathBuf,
}

impl ConfigManager {
    // 创建新的配置管理器实例
    pub fn new() -> Result<Self, String> {
        let app_data_dir = dirs::data_local_dir()
            .ok_or("无法获取应用数据目录")?
            .join("ClaudeSwitch");

        let config_dir = app_data_dir.join("configurations");
        let backup_dir = config_dir.join("backup");

        // 创建必要的目录
        fs::create_dir_all(&app_data_dir)
            .map_err(|e| format!("无法创建应用数据目录: {}", e))?;
        fs::create_dir_all(&config_dir)
            .map_err(|e| format!("无法创建配置目录: {}", e))?;
        fs::create_dir_all(&backup_dir)
            .map_err(|e| format!("无法创建备份目录: {}", e))?;

        Ok(Self {
            app_data_dir,
            config_dir,
            backup_dir,
        })
    }

    // 获取应用配置文件路径
    fn get_app_config_path(&self) -> PathBuf {
        self.app_data_dir.join("config.json")
    }

    // 加载应用配置
    pub fn load_app_config(&self) -> Result<AppConfig, String> {
        let config_path = self.get_app_config_path();
        
        if !config_path.exists() {
            return Ok(AppConfig::default());
        }

        let content = fs::read_to_string(&config_path)
            .map_err(|e| format!("无法读取应用配置: {}", e))?;

        serde_json::from_str(&content)
            .map_err(|e| format!("应用配置文件格式错误: {}", e))
    }

    // 保存应用配置
    pub fn save_app_config(&self, config: &AppConfig) -> Result<(), String> {
        let config_path = self.get_app_config_path();
        let content = serde_json::to_string_pretty(config)
            .map_err(|e| format!("序列化配置失败: {}", e))?;

        fs::write(&config_path, content)
            .map_err(|e| format!("保存应用配置失败: {}", e))
    }

    // 列出所有配置文件
    pub fn list_configurations(&self) -> Result<Vec<ConfigInfo>, String> {
        let entries = fs::read_dir(&self.config_dir)
            .map_err(|e| format!("无法读取配置目录: {}", e))?;

        let mut configs = Vec::new();

        for entry in entries {
            let entry = entry.map_err(|e| format!("读取目录项失败: {}", e))?;
            let path = entry.path();

            // 跳过非JSON文件和备份目录
            if !path.is_file() || 
               path.extension().map_or(true, |ext| ext != "json") ||
               path.file_name().map_or(true, |name| name == "backup") {
                continue;
            }

            let metadata = entry.metadata()
                .map_err(|e| format!("获取文件元数据失败: {}", e))?;

            let name = path.file_name()
                .and_then(|n| n.to_str())
                .ok_or("无效的文件名")?
                .to_string();

            // 检查JSON格式有效性
            let is_valid = match fs::read_to_string(&path) {
                Ok(content) => serde_json::from_str::<serde_json::Value>(&content).is_ok(),
                Err(_) => false,
            };

            let last_modified = DateTime::<Utc>::from(metadata.modified()
                .map_err(|e| format!("获取修改时间失败: {}", e))?)
                .format("%Y-%m-%d %H:%M:%S")
                .to_string();

            configs.push(ConfigInfo {
                name,
                is_valid,
                last_modified,
                size: metadata.len(),
            });
        }

        // 按修改时间排序
        configs.sort_by(|a, b| b.last_modified.cmp(&a.last_modified));

        Ok(configs)
    }

    // 创建新配置文件
    pub fn create_configuration(&self, name: &str, content: &str) -> Result<(), String> {
        if name.is_empty() {
            return Err("配置名称不能为空".to_string());
        }

        let file_name = if name.ends_with(".json") {
            name.to_string()
        } else {
            format!("{}.json", name)
        };

        let config_path = self.config_dir.join(&file_name);

        if config_path.exists() {
            return Err("配置文件已存在".to_string());
        }

        // 验证JSON格式
        serde_json::from_str::<serde_json::Value>(content)
            .map_err(|e| format!("JSON格式错误: {}", e))?;

        fs::write(&config_path, content)
            .map_err(|e| format!("创建配置文件失败: {}", e))
    }

    // 读取配置文件
    pub fn read_configuration(&self, name: &str) -> Result<String, String> {
        let config_path = self.config_dir.join(name);

        if !config_path.exists() {
            return Err("配置文件不存在".to_string());
        }

        fs::read_to_string(&config_path)
            .map_err(|e| format!("读取配置文件失败: {}", e))
    }

    // 更新配置文件
    pub fn update_configuration(&self, name: &str, content: &str) -> Result<(), String> {
        let config_path = self.config_dir.join(name);

        if !config_path.exists() {
            return Err("配置文件不存在".to_string());
        }

        // 验证JSON格式
        serde_json::from_str::<serde_json::Value>(content)
            .map_err(|e| format!("JSON格式错误: {}", e))?;

        fs::write(&config_path, content)
            .map_err(|e| format!("更新配置文件失败: {}", e))
    }

    // 重命名配置文件
    pub fn rename_configuration(&self, old_name: &str, new_name: &str) -> Result<(), String> {
        if new_name.is_empty() {
            return Err("新名称不能为空".to_string());
        }

        let old_path = self.config_dir.join(old_name);
        let new_file_name = if new_name.ends_with(".json") {
            new_name.to_string()
        } else {
            format!("{}.json", new_name)
        };
        let new_path = self.config_dir.join(&new_file_name);

        if !old_path.exists() {
            return Err("原配置文件不存在".to_string());
        }

        if new_path.exists() {
            return Err("新名称的配置文件已存在".to_string());
        }

        fs::rename(&old_path, &new_path)
            .map_err(|e| format!("重命名配置文件失败: {}", e))
    }

    // 删除配置文件
    pub fn delete_configuration(&self, name: &str) -> Result<(), String> {
        let config_path = self.config_dir.join(name);

        if !config_path.exists() {
            return Err("配置文件不存在".to_string());
        }

        fs::remove_file(&config_path)
            .map_err(|e| format!("删除配置文件失败: {}", e))
    }

    // 验证JSON格式
    pub fn validate_json(&self, content: &str) -> Result<bool, String> {
        match serde_json::from_str::<serde_json::Value>(content) {
            Ok(_) => Ok(true),
            Err(e) => Err(format!("JSON格式错误: {}", e)),
        }
    }

    // 激活配置
    pub fn activate_configuration(&self, config_name: &str, target_directory: &str) -> Result<(), String> {
        if target_directory.is_empty() {
            return Err("目标目录不能为空".to_string());
        }

        let source_path = self.config_dir.join(config_name);
        let target_path = Path::new(target_directory);

        if !source_path.exists() {
            return Err("源配置文件不存在".to_string());
        }

        // 确保目标目录存在
        if let Some(parent) = target_path.parent() {
            fs::create_dir_all(parent)
                .map_err(|e| format!("无法创建目标目录: {}", e))?;
        }

        // 读取源配置内容
        let content = fs::read_to_string(&source_path)
            .map_err(|e| format!("读取源配置文件失败: {}", e))?;

        // 写入目标文件
        fs::write(target_path, content)
            .map_err(|e| format!("写入目标文件失败: {}", e))?;

        Ok(())
    }

    // 备份现有配置
    pub fn backup_existing_config(&self, target_file: &str) -> Result<String, String> {
        let target_path = Path::new(target_file);

        if !target_path.exists() {
            return Err("目标文件不存在，无需备份".to_string());
        }

        let timestamp = Utc::now().format("%Y%m%d_%H%M%S").to_string();
        let backup_name = format!("backup_{}_{}.json", timestamp, Uuid::new_v4().simple());
        let backup_path = self.backup_dir.join(&backup_name);

        fs::copy(target_path, &backup_path)
            .map_err(|e| format!("备份文件失败: {}", e))?;

        Ok(backup_name)
    }

    // 获取当前激活的配置
    pub fn get_current_active_config(&self) -> Result<Option<String>, String> {
        let app_config = self.load_app_config()?;
        Ok(app_config.last_active_config)
    }
}