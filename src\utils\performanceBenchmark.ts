/**
 * 性能基准测试工具
 * 用于建立性能基线和持续监控
 */

export interface BenchmarkResult {
  name: string;
  duration: number;
  memoryDelta: number;
  timestamp: number;
  iterations: number;
}

export interface BenchmarkOptions {
  iterations?: number;
  warmupRuns?: number;
  timeout?: number;
}

/**
 * 性能基准测试类
 */
export class PerformanceBenchmark {
  private results: BenchmarkResult[] = [];
  private baselines: Map<string, BenchmarkResult> = new Map();

  /**
   * 运行基准测试
   */
  async runBenchmark(
    name: string,
    testFunction: () => Promise<void> | void,
    options: BenchmarkOptions = {}
  ): Promise<BenchmarkResult> {
    const {
      iterations = 10,
      warmupRuns = 3,
      timeout = 30000
    } = options;

    // 预热运行
    for (let i = 0; i < warmupRuns; i++) {
      await testFunction();
    }

    // 记录初始内存
    let initialMemory = 0;
    if ('memory' in performance) {
      initialMemory = (performance as any).memory.usedJSHeapSize;
    }

    // 执行基准测试
    const startTime = performance.now();
    
    try {
      for (let i = 0; i < iterations; i++) {
        await Promise.race([
          testFunction(),
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Test timeout')), timeout)
          )
        ]);
      }
    } catch (error) {
      console.error(`基准测试 "${name}" 失败:`, error);
      throw error;
    }

    const endTime = performance.now();
    const duration = (endTime - startTime) / iterations;

    // 计算内存变化
    let memoryDelta = 0;
    if ('memory' in performance) {
      const finalMemory = (performance as any).memory.usedJSHeapSize;
      memoryDelta = (finalMemory - initialMemory) / 1024 / 1024; // MB
    }

    const result: BenchmarkResult = {
      name,
      duration,
      memoryDelta,
      timestamp: Date.now(),
      iterations
    };

    this.results.push(result);
    return result;
  }

  /**
   * 设置性能基线
   */
  setBaseline(name: string, result: BenchmarkResult): void {
    this.baselines.set(name, result);
  }

  /**
   * 比较当前结果与基线
   */
  compareWithBaseline(name: string, current: BenchmarkResult): {
    durationChange: number;
    memoryChange: number;
    isRegression: boolean;
  } {
    const baseline = this.baselines.get(name);
    if (!baseline) {
      throw new Error(`未找到 "${name}" 的基线数据`);
    }

    const durationChange = ((current.duration - baseline.duration) / baseline.duration) * 100;
    const memoryChange = current.memoryDelta - baseline.memoryDelta;
    const isRegression = durationChange > 10 || memoryChange > 5; // 阈值：10%性能下降或5MB内存增长

    return {
      durationChange,
      memoryChange,
      isRegression
    };
  }

  /**
   * 生成性能报告
   */
  generateReport(): string {
    const report = ['=== 性能基准测试报告 ===\n'];
    
    this.results.forEach(result => {
      report.push(`测试: ${result.name}`);
      report.push(`  平均耗时: ${result.duration.toFixed(2)}ms`);
      report.push(`  内存变化: ${result.memoryDelta.toFixed(2)}MB`);
      report.push(`  迭代次数: ${result.iterations}`);
      report.push(`  时间戳: ${new Date(result.timestamp).toLocaleString()}`);
      
      // 与基线比较
      const baseline = this.baselines.get(result.name);
      if (baseline) {
        const comparison = this.compareWithBaseline(result.name, result);
        report.push(`  性能变化: ${comparison.durationChange > 0 ? '+' : ''}${comparison.durationChange.toFixed(1)}%`);
        report.push(`  内存变化: ${comparison.memoryChange > 0 ? '+' : ''}${comparison.memoryChange.toFixed(2)}MB`);
        report.push(`  状态: ${comparison.isRegression ? '⚠️ 性能回归' : '✅ 正常'}`);
      }
      
      report.push('');
    });

    return report.join('\n');
  }

  /**
   * 清空测试结果
   */
  clearResults(): void {
    this.results = [];
  }

  /**
   * 获取所有结果
   */
  getResults(): BenchmarkResult[] {
    return [...this.results];
  }
}

/**
 * 全局基准测试实例
 */
export const globalBenchmark = new PerformanceBenchmark();

/**
 * 常用基准测试用例
 */
export const CommonBenchmarks = {
  /**
   * 测试主题切换性能
   */
  async themeSwitch(themeProvider: any): Promise<void> {
    await themeProvider.updateThemeMode('dark');
    await new Promise(resolve => setTimeout(resolve, 100));
    await themeProvider.updateThemeMode('light');
    await new Promise(resolve => setTimeout(resolve, 100));
  },

  /**
   * 测试配置文件加载性能
   */
  async configLoad(configManager: any, configName: string): Promise<void> {
    await configManager.readConfiguration(configName);
  },

  /**
   * 测试Monaco编辑器渲染性能
   */
  async monacoRender(content: string): Promise<void> {
    const div = document.createElement('div');
    div.style.width = '800px';
    div.style.height = '600px';
    document.body.appendChild(div);
    
    // 模拟Monaco编辑器创建和内容设置
    await new Promise(resolve => {
      setTimeout(() => {
        div.innerHTML = content.slice(0, 1000); // 模拟部分渲染
        resolve(void 0);
      }, 10);
    });
    
    document.body.removeChild(div);
  },

  /**
   * 测试大量配置文件列表渲染
   */
  async configListRender(configCount: number): Promise<void> {
    const configs = Array.from({ length: configCount }, (_, i) => ({
      name: `config-${i}.json`,
      is_valid: true,
      last_modified: new Date().toISOString(),
      size: Math.random() * 10000
    }));

    // 模拟React组件渲染
    const container = document.createElement('div');
    document.body.appendChild(container);
    
    container.innerHTML = configs.map(config => 
      `<div class="config-item">
        <span>${config.name}</span>
        <span>${config.size} bytes</span>
      </div>`
    ).join('');
    
    document.body.removeChild(container);
  }
};

/**
 * 自动化性能回归测试
 */
export class PerformanceRegressionTester {
  private benchmark = new PerformanceBenchmark();
  private thresholds = {
    durationRegression: 15, // 15%性能下降阈值
    memoryRegression: 10    // 10MB内存增长阈值
  };

  /**
   * 运行回归测试套件
   */
  async runRegressionTests(): Promise<{
    passed: boolean;
    results: Array<{ name: string; passed: boolean; details: any }>;
  }> {
    const tests = [
      { name: 'theme-switch', test: CommonBenchmarks.themeSwitch },
      { name: 'config-load', test: (cb: any) => CommonBenchmarks.configLoad(cb, 'test.json') },
      { name: 'monaco-render', test: () => CommonBenchmarks.monacoRender('{}') },
      { name: 'config-list-100', test: () => CommonBenchmarks.configListRender(100) }
    ];

    const results = [];
    let allPassed = true;

    for (const test of tests) {
      try {
        const result = await this.benchmark.runBenchmark(test.name, test.test);
        const baseline = this.benchmark.baselines.get(test.name);
        
        let passed = true;
        let details = { result };

        if (baseline) {
          const comparison = this.benchmark.compareWithBaseline(test.name, result);
          passed = !comparison.isRegression;
          details = { result, comparison, baseline };
        }

        results.push({ name: test.name, passed, details });
        if (!passed) allPassed = false;

      } catch (error) {
        results.push({ 
          name: test.name, 
          passed: false, 
          details: { error: error.message } 
        });
        allPassed = false;
      }
    }

    return { passed: allPassed, results };
  }

  /**
   * 设置回归阈值
   */
  setThresholds(durationRegression: number, memoryRegression: number): void {
    this.thresholds = { durationRegression, memoryRegression };
  }
}