import { useReducer, useCallback } from 'react';
import {
  ConfigManagerState,
  ConfigAction,
  defaultConfigManagerState,
  NotificationConfig,
} from '../../types/config';

/**
 * 配置状态管理器
 * 统一处理所有配置相关的状态更新
 */
function configReducer(state: ConfigManagerState, action: ConfigAction): ConfigManagerState {
  switch (action.type) {
    case 'SET_APP_CONFIG':
      return { ...state, appConfig: action.payload, appConfigError: null };
    
    case 'SET_APP_CONFIG_LOADING':
      return { ...state, isLoadingAppConfig: action.payload };
    
    case 'SET_APP_CONFIG_ERROR':
      return { ...state, appConfigError: action.payload, isLoadingAppConfig: false };
    
    case 'SET_CONFIGURATIONS':
      return { ...state, configurations: action.payload, configurationsError: null };
    
    case 'SET_CONFIGURATIONS_LOADING':
      return { ...state, isLoadingConfigurations: action.payload };
    
    case 'SET_CONFIGURATIONS_ERROR':
      return { ...state, configurationsError: action.payload, isLoadingConfigurations: false };
    
    case 'SET_CURRENT_CONFIG':
      return {
        ...state,
        currentConfig: {
          name: action.payload.name,
          content: action.payload.content,
          isModified: false,
          validationResult: null,
          sessionId: action.payload.sessionId,
          loadedAt: action.payload.loadedAt,
        },
      };
    
    case 'UPDATE_CURRENT_CONFIG_CONTENT':
      return state.currentConfig ? {
        ...state,
        currentConfig: {
          ...state.currentConfig,
          content: action.payload,
          isModified: state.currentConfig.content !== action.payload,
        },
      } : state;
    
    case 'SET_CURRENT_CONFIG_VALIDATION':
      return state.currentConfig ? {
        ...state,
        currentConfig: {
          ...state.currentConfig,
          validationResult: action.payload,
        },
      } : state;
    
    case 'MARK_MODIFIED':
      return state.currentConfig ? {
        ...state,
        currentConfig: {
          ...state.currentConfig,
          isModified: action.payload,
        },
      } : state;
    
    case 'CLEAR_CURRENT_CONFIG':
      return { ...state, currentConfig: null };
    
    case 'SET_SELECTED_CONFIG':
      return { ...state, selectedConfigName: action.payload };
    
    case 'SET_CREATING_NEW':
      return { ...state, isCreatingNew: action.payload };
    
    case 'SET_RENAMING':
      return { ...state, isRenaming: action.payload };
    
    case 'SHOW_DELETE_CONFIRM':
      return {
        ...state,
        showDeleteConfirm: action.payload.show,
        deleteConfigName: action.payload.configName || null,
      };
    
    case 'SET_ACTIVATING':
      return { ...state, isActivating: action.payload };
    
    case 'SET_ACTIVATION_ERROR':
      return { ...state, activationError: action.payload, isActivating: false };
    
    case 'SET_SAVING':
      return { ...state, isSaving: action.payload };
    
    case 'SET_SAVING_ERROR':
      return { ...state, savingError: action.payload, isSaving: false };
    
    default:
      return state;
  }
}

/**
 * 配置状态管理Hook
 * 提供集中的状态管理和通知功能
 */
export function useConfigState() {
  const [state, dispatch] = useReducer(configReducer, defaultConfigManagerState);

  // 通知处理器
  const showNotification = useCallback((config: NotificationConfig) => {
    console.log(`[${config.type.toUpperCase()}] ${config.title}: ${config.message || ''}`);
  }, []);

  // UI 状态控制方法
  const setCreatingNew = useCallback((creating: boolean) => {
    dispatch({ type: 'SET_CREATING_NEW', payload: creating });
  }, []);

  const setRenaming = useCallback((renaming: boolean) => {
    dispatch({ type: 'SET_RENAMING', payload: renaming });
  }, []);

  const showDeleteConfirm = useCallback((configName: string) => {
    dispatch({ type: 'SHOW_DELETE_CONFIRM', payload: { show: true, configName } });
  }, []);

  const hideDeleteConfirm = useCallback(() => {
    dispatch({ type: 'SHOW_DELETE_CONFIRM', payload: { show: false } });
  }, []);

  const selectConfiguration = useCallback((name: string | null) => {
    // 简化选择逻辑，直接设置选中状态
    dispatch({ type: 'SET_SELECTED_CONFIG', payload: name });
  }, []);

  const clearCurrentConfig = useCallback(() => {
    // 强制清空所有相关状态
    dispatch({ type: 'CLEAR_CURRENT_CONFIG' });
    dispatch({ type: 'SET_SELECTED_CONFIG', payload: null });
    
    // 清理验证结果
    setTimeout(() => {
      dispatch({ type: 'SET_CURRENT_CONFIG_VALIDATION', payload: null });
    }, 10);
  }, []);

  return {
    state,
    dispatch,
    showNotification,
    setCreatingNew,
    setRenaming,
    showDeleteConfirm,
    hideDeleteConfirm,
    selectConfiguration,
    clearCurrentConfig,
  };
}