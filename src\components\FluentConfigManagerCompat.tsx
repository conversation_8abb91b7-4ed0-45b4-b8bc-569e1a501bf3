import React, { useState, useCallback } from 'react';
import {
  <PERSON>ton,
  Card,
  Text,
  Dialog,
  DialogTrigger,
  DialogSurface,
  DialogTitle,
  DialogBody,
  DialogActions,
  DialogContent,
  Field,
  Input,
  MessageBar,
  MessageBarBody,
  Toast,
  ToastTitle,
  ToastBody,
  useToastController,
  Toaster,
} from '@fluentui/react-components';
import {
  Settings24Regular,
  Add24Regular,
} from '@fluentui/react-icons';

import { useConfig } from '../hooks/useConfig';
import { FluentDirectorySelector } from './FluentDirectorySelector';
import { FluentConfigList } from './FluentConfigList';
import { useMainLayoutStyles, useWelcomeStyles } from '../styles/fluent-styles';
import { ConfigNameValidator } from '../utils/validation';
import { ThemeToggle } from './ThemeToggle';

interface FluentConfigManagerCompatProps {
  className?: string;
}

export const FluentConfigManagerCompat: React.FC<FluentConfigManagerCompatProps> = ({ className = '' }) => {
  const {
    state,
    saveAppConfig,
    selectTargetDirectory,
    loadConfigurations,
    createConfiguration,
    readConfiguration,
    updateConfiguration,
    renameConfiguration,
    deleteConfiguration,
    activateConfiguration,
    selectConfiguration,
    clearCurrentConfig,
    showDeleteConfirm,
    hideDeleteConfirm,
  } = useConfig();

  const styles = useMainLayoutStyles();
  const welcomeStyles = useWelcomeStyles();
  const { dispatchToast } = useToastController();

  // 本地状态
  const [newConfigName, setNewConfigName] = useState('');
  const [renameConfigName, setRenameConfigName] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [renameDialogOpen, setRenameDialogOpen] = useState(false);

  // 显示Toast通知
  const showToast = useCallback((title: string, body?: string, intent: 'success' | 'error' | 'warning' = 'success') => {
    dispatchToast(
      <Toast>
        <ToastTitle>{title}</ToastTitle>
        {body && <ToastBody>{body}</ToastBody>}
      </Toast>,
      { intent, timeout: 3000 }
    );
  }, [dispatchToast]);

  // 处理新建配置
  const handleCreateNew = useCallback(() => {
    setCreateDialogOpen(true);
    setNewConfigName('');
  }, []);

  // 处理创建配置提交
  const handleCreateSubmit = useCallback(async () => {
    if (!newConfigName.trim()) return;

    const validation = ConfigNameValidator.validate(newConfigName);
    if (!validation.isValid) {
      showToast('配置名称无效', validation.errors.join(', '), 'error');
      return;
    }

    const finalName = newConfigName.endsWith('.json') ? newConfigName : `${newConfigName}.json`;
    const existingNames = state.configurations.map(c => c.name);
    
    if (existingNames.includes(finalName)) {
      showToast('名称冲突', '配置文件名已存在，请选择其他名称', 'error');
      return;
    }

    setIsProcessing(true);
    try {
      const defaultContent = JSON.stringify({
        name: newConfigName,
        version: "1.0.0",
        settings: {}
      }, null, 2);

      await createConfiguration(finalName, defaultContent);
      await readConfiguration(finalName);
      setCreateDialogOpen(false);
      setNewConfigName('');
      showToast('配置创建成功', `已创建配置文件: ${finalName}`);
    } catch (error) {
      console.error('创建配置失败:', error);
      showToast('创建失败', '无法创建配置文件', 'error');
    } finally {
      setIsProcessing(false);
    }
  }, [newConfigName, state.configurations, createConfiguration, readConfiguration, showToast]);

  // 处理编辑配置
  const handleEdit = useCallback(async (configName: string) => {
    try {
      await readConfiguration(configName);
      selectConfiguration(configName);
    } catch (error) {
      console.error('读取配置失败:', error);
      showToast('读取失败', '无法读取配置文件', 'error');
    }
  }, [readConfiguration, selectConfiguration, showToast]);

  // 处理重命名
  const handleRename = useCallback((configName: string) => {
    setRenameDialogOpen(true);
    setRenameConfigName(configName.replace('.json', ''));
  }, []);

  // 处理重命名提交
  const handleRenameSubmit = useCallback(async () => {
    if (!renameConfigName.trim() || !state.selectedConfigName) return;

    const validation = ConfigNameValidator.validate(renameConfigName);
    if (!validation.isValid) {
      showToast('配置名称无效', validation.errors.join(', '), 'error');
      return;
    }

    const newName = renameConfigName.endsWith('.json') ? renameConfigName : `${renameConfigName}.json`;
    const existingNames = state.configurations.map(c => c.name);
    
    if (existingNames.includes(newName) && newName !== state.selectedConfigName) {
      showToast('名称冲突', '配置文件名已存在，请选择其他名称', 'error');
      return;
    }

    setIsProcessing(true);
    try {
      await renameConfiguration(state.selectedConfigName, newName);
      if (state.currentConfig && state.currentConfig.name === state.selectedConfigName) {
        await readConfiguration(newName);
      }
      selectConfiguration(newName);
      setRenameDialogOpen(false);
      setRenameConfigName('');
      showToast('重命名成功', `已重命名为: ${newName}`);
    } catch (error) {
      console.error('重命名配置失败:', error);
      showToast('重命名失败', '无法重命名配置文件', 'error');
    } finally {
      setIsProcessing(false);
    }
  }, [renameConfigName, state.selectedConfigName, state.currentConfig, state.configurations, renameConfiguration, readConfiguration, selectConfiguration, showToast]);

  // 处理删除确认
  const handleDeleteConfirm = useCallback(async () => {
    if (!state.deleteConfigName) return;

    setIsProcessing(true);
    try {
      await deleteConfiguration(state.deleteConfigName);
      if (state.selectedConfigName === state.deleteConfigName) {
        selectConfiguration(null);
        clearCurrentConfig();
      }
      hideDeleteConfirm();
      showToast('删除成功', `已删除配置: ${state.deleteConfigName}`);
    } catch (error) {
      console.error('删除配置失败:', error);
      showToast('删除失败', '无法删除配置文件', 'error');
    } finally {
      setIsProcessing(false);
    }
  }, [state.deleteConfigName, state.selectedConfigName, deleteConfiguration, selectConfiguration, clearCurrentConfig, hideDeleteConfirm, showToast]);

  // 处理激活配置
  const handleActivate = useCallback(async (configName: string) => {
    if (!state.appConfig?.target_directory) {
      showToast('目录未设置', '请先设置目标目录', 'warning');
      return;
    }

    const config = state.configurations.find(c => c.name === configName);
    if (!config?.is_valid) {
      showToast('格式错误', '配置文件格式错误，无法激活', 'error');
      return;
    }

    try {
      await activateConfiguration(configName);
      showToast('激活成功', `配置 ${configName} 已激活`);
    } catch (error) {
      console.error('激活配置失败:', error);
      showToast('激活失败', '无法激活配置文件', 'error');
    }
  }, [state.appConfig, state.configurations, activateConfiguration, showToast]);

  return (
    <div className={`${styles.root} ${className}`}>
      <Toaster />
      
      {/* 头部 */}
      <header className={styles.header}>
        <Text className={styles.headerTitle}>配置切换工具</Text>
        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          {state.appConfig?.target_directory && (
            <Text size={200} style={{ color: 'var(--colorNeutralForeground3)' }}>
              目标: {state.appConfig.target_directory}
            </Text>
          )}
          {state.appConfig?.last_active_config && (
            <Text size={200} style={{ color: 'var(--colorBrandForeground1)' }}>
              激活: {state.appConfig.last_active_config}
            </Text>
          )}
          <ThemeToggle />
        </div>
      </header>

      {/* 主内容 */}
      <div className={styles.content}>
        {/* 左侧边栏 */}
        <div className={styles.sidebar}>
          <FluentDirectorySelector
            appConfig={state.appConfig}
            onConfigUpdate={saveAppConfig}
            onSelectDirectory={selectTargetDirectory}
          />
          
          <div style={{ marginTop: '16px', height: 'calc(100% - 200px)' }}>
            <FluentConfigList
              configurations={state.configurations}
              selectedConfig={state.selectedConfigName}
              activeConfig={state.appConfig?.last_active_config || null}
              isLoading={state.isLoadingConfigurations}
              error={state.configurationsError}
              onSelect={selectConfiguration}
              onEdit={handleEdit}
              onRename={handleRename}
              onDelete={showDeleteConfirm}
              onActivate={handleActivate}
              onRefresh={loadConfigurations}
              onCreateNew={handleCreateNew}
            />
          </div>
        </div>

        {/* 主内容区 */}
        <div className={styles.mainContent}>
          <Card className={welcomeStyles.container}>
            <div className={welcomeStyles.content}>
              <Settings24Regular className={welcomeStyles.icon} />
              <Text className={welcomeStyles.title}>欢迎使用配置管理器</Text>
              <Text className={welcomeStyles.description}>
                基于Microsoft Fluent Design 2重新设计的配置管理界面。
                支持深色模式、主题切换和现代化的用户体验。
              </Text>
              <Button
                appearance="primary"
                icon={<Add24Regular />}
                size="large"
                onClick={handleCreateNew}
              >
                创建新配置
              </Button>
            </div>
          </Card>
        </div>
      </div>

      {/* 创建配置对话框 */}
      <Dialog open={createDialogOpen} onOpenChange={(_, data) => setCreateDialogOpen(data.open)}>
        <DialogSurface>
          <DialogBody>
            <DialogTitle>创建新配置</DialogTitle>
            <DialogContent>
              <Field label="配置名称" required>
                <Input
                  value={newConfigName}
                  onChange={(_, data) => setNewConfigName(data.value)}
                  placeholder="例如: my-config"
                />
              </Field>
              <Text size={200} style={{ color: 'var(--colorNeutralForeground3)', marginTop: '8px' }}>
                将自动添加 .json 扩展名
              </Text>
            </DialogContent>
            <DialogActions>
              <DialogTrigger disableButtonEnhancement>
                <Button appearance="secondary">取消</Button>
              </DialogTrigger>
              <Button 
                appearance="primary" 
                onClick={handleCreateSubmit}
                disabled={!newConfigName.trim() || isProcessing}
              >
                {isProcessing ? '创建中...' : '创建'}
              </Button>
            </DialogActions>
          </DialogBody>
        </DialogSurface>
      </Dialog>

      {/* 重命名配置对话框 */}
      <Dialog open={renameDialogOpen} onOpenChange={(_, data) => setRenameDialogOpen(data.open)}>
        <DialogSurface>
          <DialogBody>
            <DialogTitle>重命名配置</DialogTitle>
            <DialogContent>
              <Field label="新名称" required>
                <Input
                  value={renameConfigName}
                  onChange={(_, data) => setRenameConfigName(data.value)}
                  placeholder="输入新的配置名称"
                />
              </Field>
              <Text size={200} style={{ color: 'var(--colorNeutralForeground3)', marginTop: '8px' }}>
                将自动添加 .json 扩展名
              </Text>
            </DialogContent>
            <DialogActions>
              <DialogTrigger disableButtonEnhancement>
                <Button appearance="secondary">取消</Button>
              </DialogTrigger>
              <Button 
                appearance="primary" 
                onClick={handleRenameSubmit}
                disabled={!renameConfigName.trim() || isProcessing}
              >
                {isProcessing ? '重命名中...' : '重命名'}
              </Button>
            </DialogActions>
          </DialogBody>
        </DialogSurface>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog open={state.showDeleteConfirm} onOpenChange={(_, data) => !data.open && hideDeleteConfirm()}>
        <DialogSurface>
          <DialogBody>
            <DialogTitle>确认删除</DialogTitle>
            <DialogContent>
              <MessageBar intent="warning">
                <MessageBarBody>
                  确定要删除配置文件 <strong>{state.deleteConfigName}</strong> 吗？此操作无法撤销。
                </MessageBarBody>
              </MessageBar>
            </DialogContent>
            <DialogActions>
              <DialogTrigger disableButtonEnhancement>
                <Button appearance="secondary">取消</Button>
              </DialogTrigger>
              <Button 
                appearance="primary" 
                onClick={handleDeleteConfirm}
                disabled={isProcessing}
              >
                {isProcessing ? '删除中...' : '确认删除'}
              </Button>
            </DialogActions>
          </DialogBody>
        </DialogSurface>
      </Dialog>
    </div>
  );
};