// Fluent UI动画系统
import { makeStyles, tokens } from '@fluentui/react-components';

// 淡入淡出动画
export const useFadeAnimations = makeStyles({
  fadeIn: {
    animationName: {
      '0%': { opacity: 0, transform: 'translateY(10px)' },
      '100%': { opacity: 1, transform: 'translateY(0)' },
    },
    animationDuration: tokens.durationNormal,
    animationTimingFunction: tokens.curveEasyEase,
    animationFillMode: 'both',
  },
  
  fadeOut: {
    animationName: {
      '0%': { opacity: 1, transform: 'translateY(0)' },
      '100%': { opacity: 0, transform: 'translateY(-10px)' },
    },
    animationDuration: tokens.durationFast,
    animationTimingFunction: tokens.curveEasyEase,
    animationFillMode: 'both',
  },
});

// 滑动动画
export const useSlideAnimations = makeStyles({
  slideInLeft: {
    animationName: {
      '0%': { opacity: 0, transform: 'translateX(-20px)' },
      '100%': { opacity: 1, transform: 'translateX(0)' },
    },
    animationDuration: tokens.durationNormal,
    animationTimingFunction: tokens.curveEasyEase,
    animationFillMode: 'both',
  },
  
  slideInRight: {
    animationName: {
      '0%': { opacity: 0, transform: 'translateX(20px)' },
      '100%': { opacity: 1, transform: 'translateX(0)' },
    },
    animationDuration: tokens.durationNormal,
    animationTimingFunction: tokens.curveEasyEase,
    animationFillMode: 'both',
  },
  
  slideInUp: {
    animationName: {
      '0%': { opacity: 0, transform: 'translateY(20px)' },
      '100%': { opacity: 1, transform: 'translateY(0)' },
    },
    animationDuration: tokens.durationNormal,
    animationTimingFunction: tokens.curveEasyEase,
    animationFillMode: 'both',
  },
});

// 缩放动画
export const useScaleAnimations = makeStyles({
  scaleIn: {
    animationName: {
      '0%': { opacity: 0, transform: 'scale(0.9)' },
      '100%': { opacity: 1, transform: 'scale(1)' },
    },
    animationDuration: tokens.durationSlow,
    animationTimingFunction: tokens.curveEasyEase,
    animationFillMode: 'both',
  },
  
  scaleOut: {
    animationName: {
      '0%': { opacity: 1, transform: 'scale(1)' },
      '100%': { opacity: 0, transform: 'scale(0.9)' },
    },
    animationDuration: tokens.durationFast,
    animationTimingFunction: tokens.curveEasyEase,
    animationFillMode: 'both',
  },
});

// 弹跳动画
export const useBounceAnimations = makeStyles({
  bounce: {
    animationName: {
      '0%, 20%, 53%, 80%, 100%': { 
        transform: 'translate3d(0,0,0)' 
      },
      '40%, 43%': { 
        transform: 'translate3d(0,-30px,0) scaleY(1.1)' 
      },
      '70%': { 
        transform: 'translate3d(0,-15px,0) scaleY(1.05)' 
      },
      '90%': { 
        transform: 'translate3d(0,-4px,0) scaleY(1.02)' 
      },
    },
    animationDuration: tokens.durationSlow,
    animationTimingFunction: tokens.curveEasyEase,
  },
  
  pulse: {
    animationName: {
      '0%': { transform: 'scale(1)' },
      '50%': { transform: 'scale(1.05)' },
      '100%': { transform: 'scale(1)' },
    },
    animationDuration: tokens.durationSlow,
    animationTimingFunction: tokens.curveEasyEase,
    animationIterationCount: 'infinite',
  },
});

// 微交互动画
export const useMicroAnimations = makeStyles({
  hover: {
    transition: `all ${tokens.durationFast} ${tokens.curveEasyEase}`,
    ':hover': {
      transform: 'translateY(-2px)',
      boxShadow: tokens.shadow16,
    },
  },
  
  press: {
    transition: `all ${tokens.durationFaster} ${tokens.curveEasyEase}`,
    ':active': {
      transform: 'translateY(1px) scale(0.98)',
    },
  },
  
  focus: {
    ':focus-visible': {
      outline: `2px solid ${tokens.colorBrandStroke1}`,
      outlineOffset: '2px',
      borderRadius: tokens.borderRadiusMedium,
    },
  },
});

// 加载动画
export const useLoadingAnimations = makeStyles({
  spin: {
    animationName: {
      '0%': { transform: 'rotate(0deg)' },
      '100%': { transform: 'rotate(360deg)' },
    },
    animationDuration: '1s',
    animationTimingFunction: 'linear',
    animationIterationCount: 'infinite',
  },
  
  shimmer: {
    position: 'relative',
    overflow: 'hidden',
    
    '::after': {
      content: '""',
      position: 'absolute',
      top: 0,
      right: 0,
      bottom: 0,
      left: 0,
      background: `linear-gradient(90deg, transparent, ${tokens.colorNeutralBackground1Hover}, transparent)`,
      transform: 'translateX(-100%)',
      animationName: {
        '0%': { transform: 'translateX(-100%)' },
        '100%': { transform: 'translateX(100%)' },
      },
      animationDuration: '1.5s',
      animationTimingFunction: tokens.curveEasyEase,
      animationIterationCount: 'infinite',
    },
  },
});

// 状态转换动画
export const useTransitionAnimations = makeStyles({
  smoothTransition: {
    transition: `all ${tokens.durationNormal} ${tokens.curveEasyEase}`,
  },
  
  fastTransition: {
    transition: `all ${tokens.durationFast} ${tokens.curveEasyEase}`,
  },
  
  slowTransition: {
    transition: `all ${tokens.durationSlow} ${tokens.curveEasyEase}`,
  },
  
  // 状态指示器动画
  statusSuccess: {
    animationName: {
      '0%': { backgroundColor: tokens.colorNeutralBackground1 },
      '50%': { backgroundColor: tokens.colorPaletteGreenBackground1 },
      '100%': { backgroundColor: tokens.colorNeutralBackground1 },
    },
    animationDuration: tokens.durationSlow,
    animationTimingFunction: tokens.curveEasyEase,
  },
  
  statusError: {
    animationName: {
      '0%': { backgroundColor: tokens.colorNeutralBackground1 },
      '50%': { backgroundColor: tokens.colorPaletteRedBackground1 },
      '100%': { backgroundColor: tokens.colorNeutralBackground1 },
    },
    animationDuration: tokens.durationSlow,
    animationTimingFunction: tokens.curveEasyEase,
  },
});

// 组合动画样式
export const useAnimationUtils = makeStyles({
  // 延迟动画，用于错开动画时间
  animationDelay1: { animationDelay: '0.1s' },
  animationDelay2: { animationDelay: '0.2s' },
  animationDelay3: { animationDelay: '0.3s' },
  animationDelay4: { animationDelay: '0.4s' },
  animationDelay5: { animationDelay: '0.5s' },
  
  // 减少动画（用于可访问性）
  reducedMotion: {
    '@media (prefers-reduced-motion: reduce)': {
      animationDuration: '0.01ms !important',
      animationIterationCount: '1 !important',
      transitionDuration: '0.01ms !important',
    },
  },
});