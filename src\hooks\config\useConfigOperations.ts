import { useCallback } from 'react';
import { TauriAPI } from '../../types/tauri-api';
import { ConfigManagerState, NotificationConfig } from '../../types/config';

/**
 * CRUD操作Hook参数接口
 */
interface UseConfigOperationsParams {
  state: ConfigManagerState;
  dispatch: (action: any) => void;
  showNotification: (config: NotificationConfig) => void;
  loadConfigurations: () => Promise<void>;
}

/**
 * 配置CRUD操作Hook
 * 专门负责配置的创建、更新、重命名、删除和激活操作
 */
export function useConfigOperations({ 
  state, 
  dispatch, 
  showNotification, 
  loadConfigurations 
}: UseConfigOperationsParams) {
  // 创建新配置
  const createConfiguration = useCallback(async (name: string, content: string) => {
    dispatch({ type: 'SET_SAVING', payload: true });
    try {
      await TauriAPI.createConfiguration(name, content);
      await loadConfigurations();
      showNotification({
        type: 'success',
        title: '配置创建成功',
        message: `配置文件 "${name}" 已创建`,
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      dispatch({ type: 'SET_SAVING_ERROR', payload: errorMessage });
      showNotification({
        type: 'error',
        title: '创建配置失败',
        message: errorMessage,
      });
      throw error;
    } finally {
      dispatch({ type: 'SET_SAVING', payload: false });
    }
  }, [dispatch, showNotification, loadConfigurations]);

  // 更新配置内容
  const updateConfiguration = useCallback(async (name: string, content: string) => {
    dispatch({ type: 'SET_SAVING', payload: true });
    try {
      await TauriAPI.updateConfiguration(name, content);
      if (state.currentConfig && state.currentConfig.name === name) {
        dispatch({ type: 'MARK_MODIFIED', payload: false });
      }
      await loadConfigurations();
      showNotification({
        type: 'success',
        title: '配置保存成功',
        message: `配置文件 "${name}" 已保存`,
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      dispatch({ type: 'SET_SAVING_ERROR', payload: errorMessage });
      showNotification({
        type: 'error',
        title: '保存配置失败',
        message: errorMessage,
      });
      throw error;
    } finally {
      dispatch({ type: 'SET_SAVING', payload: false });
    }
  }, [state.currentConfig, dispatch, showNotification, loadConfigurations]);

  // 重命名配置
  const renameConfiguration = useCallback(async (oldName: string, newName: string) => {
    try {
      await TauriAPI.renameConfiguration(oldName, newName);
      await loadConfigurations();
      showNotification({
        type: 'success',
        title: '配置重命名成功',
        message: `配置文件已重命名为 "${newName}"`,
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      showNotification({
        type: 'error',
        title: '重命名配置失败',
        message: errorMessage,
      });
      throw error;
    }
  }, [showNotification, loadConfigurations]);

  // 删除配置
  const deleteConfiguration = useCallback(async (name: string) => {
    try {
      await TauriAPI.deleteConfiguration(name);
      if (state.currentConfig && state.currentConfig.name === name) {
        dispatch({ type: 'CLEAR_CURRENT_CONFIG' });
      }
      await loadConfigurations();
      showNotification({
        type: 'success',
        title: '配置删除成功',
        message: `配置文件 "${name}" 已删除`,
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      showNotification({
        type: 'error',
        title: '删除配置失败',
        message: errorMessage,
      });
      throw error;
    }
  }, [state.currentConfig, dispatch, showNotification, loadConfigurations]);

  // 激活配置
  const activateConfiguration = useCallback(async (name: string) => {
    if (!state.appConfig?.target_directory) {
      showNotification({
        type: 'error',
        title: '激活失败',
        message: '请先设置目标目录',
      });
      return;
    }

    dispatch({ type: 'SET_ACTIVATING', payload: true });
    try {
      await TauriAPI.activateConfiguration(name, state.appConfig.target_directory);
      
      // 更新应用配置
      const updatedConfig = { ...state.appConfig, last_active_config: name };
      dispatch({ type: 'SET_APP_CONFIG', payload: updatedConfig });
      
      showNotification({
        type: 'success',
        title: '配置激活成功',
        message: `配置 "${name}" 已激活到目标目录`,
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      dispatch({ type: 'SET_ACTIVATION_ERROR', payload: errorMessage });
      showNotification({
        type: 'error',
        title: '配置激活失败',
        message: errorMessage,
      });
      throw error;
    } finally {
      dispatch({ type: 'SET_ACTIVATING', payload: false });
    }
  }, [state.appConfig, dispatch, showNotification]);

  // 批量操作支持
  const batchDeleteConfigurations = useCallback(async (names: string[]) => {
    const results = [];
    for (const name of names) {
      try {
        await deleteConfiguration(name);
        results.push({ name, success: true });
      } catch (error) {
        results.push({ 
          name, 
          success: false, 
          error: error instanceof Error ? error.message : String(error) 
        });
      }
    }
    return results;
  }, [deleteConfiguration]);

  // 配置复制
  const duplicateConfiguration = useCallback(async (sourceName: string, targetName: string) => {
    try {
      // 先读取源配置内容
      const content = await TauriAPI.readConfiguration(sourceName);
      // 创建新配置
      await createConfiguration(targetName, content);
      showNotification({
        type: 'success',
        title: '配置复制成功',
        message: `配置 "${sourceName}" 已复制为 "${targetName}"`,
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      showNotification({
        type: 'error',
        title: '配置复制失败',
        message: errorMessage,
      });
      throw error;
    }
  }, [createConfiguration, showNotification]);

  return {
    createConfiguration,
    updateConfiguration,
    renameConfiguration,
    deleteConfiguration,
    activateConfiguration,
    batchDeleteConfigurations,
    duplicateConfiguration,
  };
}