# CLAUDE.md

此文件为 Claude Code (claude.ai/code) 在此仓库中工作时提供指导。

## 项目概述

这是一个使用 Tauri + React + TypeScript 构建的桌面应用程序。应用程序采用混合架构：
- 前端：React + TypeScript + Vite
- 后端：Rust (Tauri)
- 打包：Tauri 用于生成原生桌面应用

## 开发命令

### 前端开发
- `npm run dev` - 启动 Vite 开发服务器
- `npm run build` - 构建前端（TypeScript 编译 + Vite 构建）
- `npm run preview` - 预览构建结果

### Tauri 开发
- `npm run tauri dev` - 启动 Tauri 开发模式（同时启动前端和 Rust 后端）
- `npm run tauri build` - 构建完整的桌面应用程序
- `npm run tauri` - 直接使用 Tauri CLI

### Rust 后端
- `cd src-tauri && cargo build` - 构建 Rust 后端
- `cd src-tauri && cargo test` - 运行 Rust 测试
- `cd src-tauri && cargo clippy` - 运行 Rust linter

## 项目架构

### 目录结构
- `src/` - React 前端源码
  - `App.tsx` - 主应用组件
  - `main.tsx` - React 应用入口
- `src-tauri/` - Rust 后端源码
  - `src/main.rs` - Rust 应用入口点
  - `src/lib.rs` - 主要业务逻辑和 Tauri 命令
  - `tauri.conf.json` - Tauri 应用配置
- `public/` - 静态资源
- `dist/` - 构建输出（自动生成）

### 技术栈
- **前端框架**: React 18 + TypeScript
- **构建工具**: Vite 6
- **桌面框架**: Tauri 2
- **后端语言**: Rust
- **开发服务器**: Vite dev server (端口 1420)

### Tauri 通信
- 前端通过 `@tauri-apps/api/core` 的 `invoke` 函数调用 Rust 命令
- Rust 端使用 `#[tauri::command]` 宏定义可调用函数
- 当前实现了 `greet` 命令作为示例

## 配置要点

### TypeScript 配置
- 启用严格模式
- 目标 ES2020
- 使用 React JSX 转换

### Tauri 配置
- 应用标识符: `com.charleschou.app`
- 开发服务器: http://localhost:1420
- 窗口默认尺寸: 800x600
- 构建前自动执行 `npm run build`

### 开发环境
- Node.js 包管理使用 npm
- Rust 工具链需要安装
- Windows 平台开发（基于项目配置）