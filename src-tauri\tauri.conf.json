{"$schema": "https://schema.tauri.app/config/2", "productName": "claude-switch", "version": "0.1.0", "identifier": "com.charleschou.app", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:8080", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "claude-switch", "width": 800, "height": 600}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}