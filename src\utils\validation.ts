import { ValidationResult, ValidationError, ValidationErrorType } from '../types/config';

/**
 * 前端JSON验证工具类
 * 提供客户端快速验证和格式化功能
 */
export class ClientJsonValidator {
  /**
   * 快速验证JSON格式
   */
  static quickValidate(content: string): boolean {
    if (!content || content.trim() === '') {
      return false;
    }

    try {
      JSON.parse(content);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 详细验证JSON格式并返回详细信息
   */
  static validateJson(content: string): ValidationResult {
    const result: ValidationResult = {
      is_valid: false,
      errors: [],
      warnings: [],
    };

    // 检查空内容
    if (!content || content.trim() === '') {
      result.errors.push({
        line: 1,
        column: 1,
        message: 'JSON内容不能为空',
        error_type: 'InvalidFormat',
      });
      return result;
    }

    try {
      const parsed = JSON.parse(content);
      result.is_valid = true;

      // 格式化JSON
      try {
        result.formatted_json = JSON.stringify(parsed, null, 2);
      } catch (e) {
        result.warnings.push('无法格式化JSON');
      }

      // 检查常见问题
      result.warnings.push(...this.checkCommonIssues(parsed));

    } catch (error) {
      result.errors.push(this.parseJsonError(error as SyntaxError, content));
    }

    return result;
  }

  /**
   * 解析JSON错误信息
   */
  private static parseJsonError(error: SyntaxError, content: string): ValidationError {
    const message = error.message;
    const position = this.extractErrorPosition(message, content);

    let errorType: ValidationErrorType = 'SyntaxError';
    let friendlyMessage = message;

    // 解析常见错误类型
    if (message.includes('Unexpected token')) {
      errorType = 'SyntaxError';
      if (message.includes('Unexpected token }')) {
        friendlyMessage = 'JSON语法错误: 多余的右花括号 }';
      } else if (message.includes('Unexpected token ]')) {
        friendlyMessage = 'JSON语法错误: 多余的右方括号 ]';
      } else if (message.includes('Unexpected token ,')) {
        friendlyMessage = 'JSON语法错误: 多余的逗号';
      } else {
        friendlyMessage = `JSON语法错误: 意外的字符 ${this.extractUnexpectedToken(message)}`;
      }
    } else if (message.includes('Unexpected end of JSON input')) {
      errorType = 'SyntaxError';
      friendlyMessage = 'JSON语法错误: 文件意外结束，可能缺少右花括号或右方括号';
    } else if (message.includes('Expected property name')) {
      errorType = 'InvalidFormat';
      friendlyMessage = 'JSON格式错误: 期望属性名称';
    } else if (message.includes('Expected double-quoted property name')) {
      errorType = 'InvalidFormat';
      friendlyMessage = 'JSON格式错误: 属性名称必须使用双引号';
    }

    return {
      line: position.line,
      column: position.column,
      message: friendlyMessage,
      error_type: errorType,
    };
  }

  /**
   * 从错误信息中提取位置信息
   */
  private static extractErrorPosition(message: string, content: string): { line: number; column: number } {
    // 尝试从错误信息中提取位置
    const positionMatch = message.match(/at position (\d+)/);
    if (positionMatch) {
      const position = parseInt(positionMatch[1], 10);
      return this.getLineAndColumn(content, position);
    }

    // JSON.parse 错误通常不包含具体位置，我们尝试其他方法
    const lineMatch = message.match(/line (\d+)/);
    if (lineMatch) {
      return { line: parseInt(lineMatch[1], 10), column: 1 };
    }

    return { line: 1, column: 1 };
  }

  /**
   * 根据字符位置计算行列号
   */
  private static getLineAndColumn(content: string, position: number): { line: number; column: number } {
    const lines = content.substring(0, position).split('\n');
    const line = lines.length;
    const column = lines[lines.length - 1].length + 1;
    return { line, column };
  }

  /**
   * 提取意外的标记
   */
  private static extractUnexpectedToken(message: string): string {
    const match = message.match(/Unexpected token (.)/);
    return match ? match[1] : '未知字符';
  }

  /**
   * 检查常见问题
   */
  private static checkCommonIssues(value: any): string[] {
    const warnings: string[] = [];

    if (typeof value === 'object' && value !== null) {
      if (Array.isArray(value)) {
        if (value.length === 0) {
          warnings.push('配置数组为空');
        } else if (value.length > 1000) {
          warnings.push(`数组元素过多 (${value.length}个)，可能影响性能`);
        }
      } else {
        const keys = Object.keys(value);
        if (keys.length === 0) {
          warnings.push('配置对象为空');
        }

        // 检查嵌套深度
        const depth = this.calculateDepth(value);
        if (depth > 10) {
          warnings.push(`JSON嵌套层次过深 (${depth}层)，可能影响性能`);
        }

        // 检查长字符串
        this.checkLongStrings(value, warnings);
      }
    } else {
      warnings.push('根级别应该是对象或数组');
    }

    return warnings;
  }

  /**
   * 计算嵌套深度
   */
  private static calculateDepth(obj: any): number {
    if (typeof obj !== 'object' || obj === null) {
      return 0;
    }

    let maxDepth = 0;
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        const depth = this.calculateDepth(obj[key]);
        maxDepth = Math.max(maxDepth, depth);
      }
    }

    return maxDepth + 1;
  }

  /**
   * 检查长字符串
   */
  private static checkLongStrings(obj: any, warnings: string[], path: string = ''): void {
    if (typeof obj === 'string') {
      if (obj.length > 10000) {
        const fullPath = path || '根级别';
        warnings.push(`字段 '${fullPath}' 的值过长 (${obj.length}字符)`);
      }
    } else if (typeof obj === 'object' && obj !== null) {
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          const newPath = path ? `${path}.${key}` : key;
          this.checkLongStrings(obj[key], warnings, newPath);
        }
      }
    }
  }

  /**
   * 格式化JSON字符串
   */
  static formatJson(content: string, indent: number = 2): string {
    try {
      const parsed = JSON.parse(content);
      return JSON.stringify(parsed, null, indent);
    } catch (error) {
      throw new Error(`JSON格式错误: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 压缩JSON字符串
   */
  static minifyJson(content: string): string {
    try {
      const parsed = JSON.parse(content);
      return JSON.stringify(parsed);
    } catch (error) {
      throw new Error(`JSON格式错误: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 比较两个JSON是否相等（忽略格式）
   */
  static jsonEquals(content1: string, content2: string): boolean {
    try {
      const obj1 = JSON.parse(content1);
      const obj2 = JSON.parse(content2);
      return JSON.stringify(obj1) === JSON.stringify(obj2);
    } catch {
      return false;
    }
  }

  /**
   * 检查JSON路径是否存在
   */
  static hasPath(content: string, path: string): boolean {
    try {
      const obj = JSON.parse(content);
      const keys = path.split('.');
      let current = obj;

      for (const key of keys) {
        if (typeof current !== 'object' || current === null || !(key in current)) {
          return false;
        }
        current = current[key];
      }

      return true;
    } catch {
      return false;
    }
  }

  /**
   * 获取JSON路径的值
   */
  static getValueAtPath(content: string, path: string): any {
    try {
      const obj = JSON.parse(content);
      const keys = path.split('.');
      let current = obj;

      for (const key of keys) {
        if (typeof current !== 'object' || current === null || !(key in current)) {
          return undefined;
        }
        current = current[key];
      }

      return current;
    } catch {
      return undefined;
    }
  }

  /**
   * 设置JSON路径的值
   */
  static setValueAtPath(content: string, path: string, value: any): string {
    try {
      const obj = JSON.parse(content);
      const keys = path.split('.');
      let current = obj;

      // 创建嵌套对象路径
      for (let i = 0; i < keys.length - 1; i++) {
        const key = keys[i];
        if (typeof current[key] !== 'object' || current[key] === null) {
          current[key] = {};
        }
        current = current[key];
      }

      // 设置最终值
      current[keys[keys.length - 1]] = value;

      return JSON.stringify(obj, null, 2);
    } catch (error) {
      throw new Error(`设置JSON路径值失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
}

/**
 * 配置名称验证
 */
export class ConfigNameValidator {
  // 无效字符正则表达式（Windows文件名限制）
  private static readonly INVALID_CHARS = /[<>:"/\\|?*\x00-\x1f]/;
  private static readonly RESERVED_NAMES = [
    'CON', 'PRN', 'AUX', 'NUL',
    'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9',
    'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'
  ];

  /**
   * 验证配置名称
   */
  static validate(name: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 检查空名称
    if (!name || name.trim() === '') {
      errors.push('配置名称不能为空');
      return { isValid: false, errors };
    }

    const trimmedName = name.trim();

    // 检查长度
    if (trimmedName.length > 255) {
      errors.push('配置名称不能超过255个字符');
    }

    // 检查无效字符
    if (this.INVALID_CHARS.test(trimmedName)) {
      errors.push('配置名称包含无效字符 (< > : " / \\ | ? *)');
    }

    // 检查以点开头或结尾
    if (trimmedName.startsWith('.') || trimmedName.endsWith('.')) {
      errors.push('配置名称不能以点开头或结尾');
    }

    // 检查以空格结尾
    if (trimmedName.endsWith(' ')) {
      errors.push('配置名称不能以空格结尾');
    }

    // 检查保留名称
    const baseName = trimmedName.replace(/\.[^.]*$/, '').toUpperCase();
    if (this.RESERVED_NAMES.includes(baseName)) {
      errors.push(`"${baseName}" 是系统保留名称，不能使用`);
    }

    return { isValid: errors.length === 0, errors };
  }

  /**
   * 清理配置名称（自动修复一些问题）
   */
  static sanitize(name: string): string {
    if (!name) return '';

    let sanitized = name.trim();

    // 替换无效字符为下划线
    sanitized = sanitized.replace(this.INVALID_CHARS, '_');

    // 移除开头和结尾的点
    sanitized = sanitized.replace(/^\.+|\.+$/g, '');

    // 移除结尾的空格
    sanitized = sanitized.replace(/\s+$/, '');

    // 确保不是保留名称
    const baseName = sanitized.replace(/\.[^.]*$/, '').toUpperCase();
    if (this.RESERVED_NAMES.includes(baseName)) {
      sanitized = `config_${sanitized}`;
    }

    // 限制长度
    if (sanitized.length > 255) {
      sanitized = sanitized.substring(0, 255);
    }

    return sanitized;
  }

  /**
   * 生成唯一的配置名称
   */
  static generateUniqueName(baseName: string, existingNames: string[]): string {
    let sanitized = this.sanitize(baseName);
    if (!sanitized) {
      sanitized = 'config';
    }

    // 添加.json扩展名如果没有
    if (!sanitized.endsWith('.json')) {
      sanitized += '.json';
    }

    let uniqueName = sanitized;
    let counter = 1;

    while (existingNames.includes(uniqueName)) {
      const nameWithoutExt = sanitized.replace(/\.json$/, '');
      uniqueName = `${nameWithoutExt}_${counter}.json`;
      counter++;
    }

    return uniqueName;
  }
}

/**
 * 工具函数
 */
export const ValidationUtils = {
  /**
   * 检查字符串是否为有效的JSON
   */
  isValidJson: (str: string): boolean => ClientJsonValidator.quickValidate(str),

  /**
   * 安全解析JSON
   */
  safeJsonParse: <T = any>(str: string, defaultValue: T): T => {
    try {
      return JSON.parse(str);
    } catch {
      return defaultValue;
    }
  },

  /**
   * 安全序列化JSON
   */
  safeJsonStringify: (obj: any, pretty: boolean = false): string => {
    try {
      return pretty ? JSON.stringify(obj, null, 2) : JSON.stringify(obj);
    } catch {
      return '';
    }
  },

  /**
   * 检查配置名称是否有效
   */
  isValidConfigName: (name: string): boolean => ConfigNameValidator.validate(name).isValid,

  /**
   * 清理配置名称
   */
  sanitizeConfigName: (name: string): string => ConfigNameValidator.sanitize(name),
};