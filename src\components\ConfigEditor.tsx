import React, { useState, useCallback, useEffect, useRef } from 'react';
import { ValidationResult, configTemplates } from '../types/config';
import { ClientJsonValidator } from '../utils/validation';

interface ConfigEditorProps {
  configName: string;
  content: string;
  isModified: boolean;
  validationResult: ValidationResult | null;
  isSaving: boolean;
  onContentChange: (content: string) => void;
  onSave: (name: string, content: string) => Promise<void>;
  onValidate: (content: string) => Promise<ValidationResult>;
  onFormat: (content: string) => Promise<string>;
  onClose: () => void;
  className?: string;
}

export const ConfigEditor: React.FC<ConfigEditorProps> = ({
  configName,
  content,
  isModified,
  validationResult,
  isSaving,
  onContentChange,
  onSave,
  onValidate,
  onFormat,
  onClose,
  className = '',
}) => {
  const [localContent, setLocalContent] = useState(content);
  const [showTemplates, setShowTemplates] = useState(false);
  const [isFormatting, setIsFormatting] = useState(false);
  const [saveStatus, setSaveStatus] = useState<'saved' | 'saving' | 'error' | null>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // 同步外部内容变化
  useEffect(() => {
    setLocalContent(content);
  }, [content]);

  // 处理内容变化
  const handleContentChange = useCallback((value: string) => {
    setLocalContent(value);
    onContentChange(value);
    setSaveStatus(null);
  }, [onContentChange]);

  // 处理保存
  const handleSave = useCallback(async () => {
    if (!localContent.trim()) {
      return;
    }

    try {
      setSaveStatus('saving');
      await onSave(configName, localContent);
      setSaveStatus('saved');
      setTimeout(() => setSaveStatus(null), 2000);
    } catch (error) {
      setSaveStatus('error');
      setTimeout(() => setSaveStatus(null), 3000);
    }
  }, [configName, localContent, onSave]);

  // 处理格式化
  const handleFormat = useCallback(async () => {
    if (!localContent.trim()) {
      return;
    }

    setIsFormatting(true);
    try {
      const formatted = await onFormat(localContent);
      handleContentChange(formatted);
    } catch (error) {
      // 格式化失败，尝试客户端格式化
      try {
        const clientFormatted = ClientJsonValidator.formatJson(localContent);
        handleContentChange(clientFormatted);
      } catch (clientError) {
        // 显示错误信息
        console.error('格式化失败:', clientError);
      }
    } finally {
      setIsFormatting(false);
    }
  }, [localContent, onFormat, handleContentChange]);

  // 处理验证
  const handleValidate = useCallback(async () => {
    if (localContent.trim()) {
      await onValidate(localContent);
    }
  }, [localContent, onValidate]);

  // 处理模板选择
  const handleTemplateSelect = useCallback((templateContent: string) => {
    handleContentChange(templateContent);
    setShowTemplates(false);
  }, [handleContentChange]);

  // 处理键盘快捷键
  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    if (event.ctrlKey || event.metaKey) {
      switch (event.key) {
        case 's':
          event.preventDefault();
          handleSave();
          break;
        case 'f':
          event.preventDefault();
          handleFormat();
          break;
        case 'Enter':
          event.preventDefault();
          handleValidate();
          break;
      }
    }
  }, [handleSave, handleFormat, handleValidate]);

  // 插入文本到光标位置
  const insertAtCursor = useCallback((insertText: string) => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const newContent = localContent.substring(0, start) + 
                      insertText + 
                      localContent.substring(end);
    
    handleContentChange(newContent);
    
    // 设置光标位置
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(start + insertText.length, start + insertText.length);
    }, 0);
  }, [localContent, handleContentChange]);

  // 错误行高亮计算
  const getErrorLines = useCallback((): Set<number> => {
    if (!validationResult?.errors) return new Set();
    return new Set(validationResult.errors.map(error => error.line));
  }, [validationResult]);

  const errorLines = getErrorLines();

  return (
    <div className={`config-editor ${className}`}>
      <div className="editor-header">
        <div className="header-left">
          <h3 className="editor-title">
            编辑配置: {configName}
            {isModified && <span className="modified-indicator">*</span>}
          </h3>
          <div className="editor-status">
            {validationResult?.is_valid === true && (
              <span className="status-valid">✓ 格式正确</span>
            )}
            {validationResult?.is_valid === false && (
              <span className="status-invalid">✗ 格式错误</span>
            )}
            {saveStatus === 'saved' && (
              <span className="status-saved">✓ 已保存</span>
            )}
            {saveStatus === 'saving' && (
              <span className="status-saving">保存中...</span>
            )}
            {saveStatus === 'error' && (
              <span className="status-error">保存失败</span>
            )}
          </div>
        </div>
        
        <div className="header-actions">
          <button
            type="button"
            className="template-button"
            onClick={() => setShowTemplates(!showTemplates)}
            title="选择模板"
          >
            📋 模板
          </button>
          <button
            type="button"
            className="validate-button"
            onClick={handleValidate}
            title="验证JSON (Ctrl+Enter)"
          >
            🔍 验证
          </button>
          <button
            type="button"
            className="format-button"
            onClick={handleFormat}
            disabled={isFormatting}
            title="格式化 (Ctrl+F)"
          >
            {isFormatting ? '⟳' : '✨'} 格式化
          </button>
          <button
            type="button"
            className="save-button primary"
            onClick={handleSave}
            disabled={isSaving || !localContent.trim()}
            title="保存 (Ctrl+S)"
          >
            {isSaving ? '⟳' : '💾'} 保存
          </button>
          <button
            type="button"
            className="close-button"
            onClick={onClose}
            title="关闭编辑器"
          >
            ✕
          </button>
        </div>
      </div>

      {showTemplates && (
        <div className="templates-panel">
          <div className="templates-header">
            <h4>选择模板</h4>
            <button
              type="button"
              className="close-templates"
              onClick={() => setShowTemplates(false)}
            >
              ✕
            </button>
          </div>
          <div className="templates-list">
            {configTemplates.map((template, index) => (
              <div
                key={index}
                className="template-item"
                onClick={() => handleTemplateSelect(template.content)}
              >
                <div className="template-name">{template.name}</div>
                <div className="template-description">{template.description}</div>
                <div className="template-category">{template.category}</div>
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="editor-content">
        <div className="editor-main">
          <div className="textarea-container">
            <textarea
              ref={textareaRef}
              className="config-textarea"
              value={localContent}
              onChange={(e) => handleContentChange(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="在此输入JSON配置内容..."
              spellCheck={false}
            />
            {errorLines.size > 0 && (
              <div className="error-highlights">
                {/* 这里可以添加错误行高亮的实现 */}
              </div>
            )}
          </div>
          
          <div className="editor-sidebar">
            <div className="quick-actions">
              <h4>快速操作</h4>
              <button
                type="button"
                className="quick-button"
                onClick={() => insertAtCursor('{\n  \n}')}
              >
                {} 对象
              </button>
              <button
                type="button"
                className="quick-button"
                onClick={() => insertAtCursor('[\n  \n]')}
              >
                [] 数组
              </button>
              <button
                type="button"
                className="quick-button"
                onClick={() => insertAtCursor('"key": "value"')}
              >
                "key": "value"
              </button>
              <button
                type="button"
                className="quick-button"
                onClick={() => insertAtCursor('true')}
              >
                true
              </button>
              <button
                type="button"
                className="quick-button"
                onClick={() => insertAtCursor('false')}
              >
                false
              </button>
              <button
                type="button"
                className="quick-button"
                onClick={() => insertAtCursor('null')}
              >
                null
              </button>
            </div>

            {validationResult && (
              <div className="validation-panel">
                <h4>验证结果</h4>
                
                {validationResult.errors.length > 0 && (
                  <div className="errors-section">
                    <h5>错误 ({validationResult.errors.length})</h5>
                    <div className="error-list">
                      {validationResult.errors.map((error, index) => (
                        <div key={index} className="error-item">
                          <div className="error-location">
                            行 {error.line}, 列 {error.column}
                          </div>
                          <div className="error-message">
                            {error.message}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {validationResult.warnings.length > 0 && (
                  <div className="warnings-section">
                    <h5>警告 ({validationResult.warnings.length})</h5>
                    <div className="warning-list">
                      {validationResult.warnings.map((warning, index) => (
                        <div key={index} className="warning-item">
                          {warning}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="editor-footer">
        <div className="footer-info">
          <span>字符数: {localContent.length}</span>
          <span>行数: {localContent.split('\n').length}</span>
          {isModified && <span className="modified-text">已修改</span>}
        </div>
        <div className="footer-shortcuts">
          <span>Ctrl+S: 保存</span>
          <span>Ctrl+F: 格式化</span>
          <span>Ctrl+Enter: 验证</span>
        </div>
      </div>

      <style jsx>{`
        .config-editor {
          background: white;
          border: 1px solid #e0e0e0;
          border-radius: 8px;
          display: flex;
          flex-direction: column;
          height: 600px;
          overflow: hidden;
        }

        .editor-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 12px 16px;
          background: #f8f9fa;
          border-bottom: 1px solid #e0e0e0;
          flex-shrink: 0;
        }

        .header-left {
          display: flex;
          flex-direction: column;
          gap: 4px;
        }

        .editor-title {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #333;
          display: flex;
          align-items: center;
          gap: 4px;
        }

        .modified-indicator {
          color: #ff6b35;
          font-weight: bold;
        }

        .editor-status {
          display: flex;
          align-items: center;
          gap: 12px;
          font-size: 12px;
        }

        .status-valid {
          color: #28a745;
          font-weight: 500;
        }

        .status-invalid {
          color: #dc3545;
          font-weight: 500;
        }

        .status-saved {
          color: #28a745;
          font-weight: 500;
        }

        .status-saving {
          color: #007bff;
          font-weight: 500;
        }

        .status-error {
          color: #dc3545;
          font-weight: 500;
        }

        .header-actions {
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .template-button,
        .validate-button,
        .format-button,
        .save-button,
        .close-button {
          padding: 6px 12px;
          border: 1px solid #ddd;
          border-radius: 4px;
          background: white;
          cursor: pointer;
          font-size: 12px;
          display: flex;
          align-items: center;
          gap: 4px;
        }

        .save-button.primary {
          background: #007bff;
          color: white;
          border-color: #007bff;
        }

        .template-button:hover,
        .validate-button:hover,
        .format-button:hover {
          background: #f5f5f5;
          border-color: #ccc;
        }

        .save-button:hover {
          background: #0056b3;
          border-color: #0056b3;
        }

        .close-button {
          background: #dc3545;
          color: white;
          border-color: #dc3545;
        }

        .close-button:hover {
          background: #c82333;
          border-color: #c82333;
        }

        .save-button:disabled,
        .format-button:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        .templates-panel {
          background: #fff;
          border-bottom: 1px solid #e0e0e0;
          max-height: 200px;
          overflow-y: auto;
          flex-shrink: 0;
        }

        .templates-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 8px 16px;
          background: #f8f9fa;
          border-bottom: 1px solid #e0e0e0;
        }

        .templates-header h4 {
          margin: 0;
          font-size: 14px;
          color: #333;
        }

        .close-templates {
          background: none;
          border: none;
          cursor: pointer;
          font-size: 16px;
          color: #666;
        }

        .templates-list {
          padding: 8px 0;
        }

        .template-item {
          padding: 8px 16px;
          cursor: pointer;
          border-bottom: 1px solid #f0f0f0;
        }

        .template-item:hover {
          background: #f8f9fa;
        }

        .template-item:last-child {
          border-bottom: none;
        }

        .template-name {
          font-weight: 500;
          color: #333;
          font-size: 14px;
        }

        .template-description {
          color: #666;
          font-size: 12px;
          margin-top: 2px;
        }

        .template-category {
          color: #999;
          font-size: 11px;
          margin-top: 2px;
        }

        .editor-content {
          flex: 1;
          display: flex;
          overflow: hidden;
        }

        .editor-main {
          flex: 1;
          display: flex;
          overflow: hidden;
        }

        .textarea-container {
          flex: 1;
          position: relative;
          overflow: hidden;
        }

        .config-textarea {
          width: 100%;
          height: 100%;
          border: none;
          outline: none;
          resize: none;
          padding: 16px;
          font-family: 'Consolas', 'Courier New', monospace;
          font-size: 14px;
          line-height: 1.5;
          background: #fafafa;
          color: #333;
          tab-size: 2;
        }

        .editor-sidebar {
          width: 250px;
          background: #f8f9fa;
          border-left: 1px solid #e0e0e0;
          overflow-y: auto;
          flex-shrink: 0;
        }

        .quick-actions {
          padding: 16px;
          border-bottom: 1px solid #e0e0e0;
        }

        .quick-actions h4 {
          margin: 0 0 12px 0;
          font-size: 14px;
          color: #333;
        }

        .quick-button {
          display: block;
          width: 100%;
          padding: 6px 8px;
          margin-bottom: 4px;
          background: white;
          border: 1px solid #ddd;
          border-radius: 4px;
          cursor: pointer;
          font-size: 12px;
          text-align: left;
          font-family: 'Consolas', 'Courier New', monospace;
        }

        .quick-button:hover {
          background: #f0f0f0;
          border-color: #ccc;
        }

        .validation-panel {
          padding: 16px;
        }

        .validation-panel h4 {
          margin: 0 0 12px 0;
          font-size: 14px;
          color: #333;
        }

        .errors-section,
        .warnings-section {
          margin-bottom: 16px;
        }

        .errors-section h5,
        .warnings-section h5 {
          margin: 0 0 8px 0;
          font-size: 12px;
          font-weight: 600;
        }

        .errors-section h5 {
          color: #dc3545;
        }

        .warnings-section h5 {
          color: #ffc107;
        }

        .error-list,
        .warning-list {
          display: flex;
          flex-direction: column;
          gap: 8px;
        }

        .error-item,
        .warning-item {
          padding: 8px;
          border-radius: 4px;
          font-size: 12px;
        }

        .error-item {
          background: #fff5f5;
          border-left: 3px solid #dc3545;
        }

        .warning-item {
          background: #fffbf0;
          border-left: 3px solid #ffc107;
        }

        .error-location {
          font-weight: 500;
          color: #dc3545;
          margin-bottom: 2px;
        }

        .error-message {
          color: #333;
        }

        .editor-footer {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 8px 16px;
          background: #f8f9fa;
          border-top: 1px solid #e0e0e0;
          font-size: 12px;
          color: #666;
          flex-shrink: 0;
        }

        .footer-info {
          display: flex;
          align-items: center;
          gap: 16px;
        }

        .modified-text {
          color: #ff6b35;
          font-weight: 500;
        }

        .footer-shortcuts {
          display: flex;
          align-items: center;
          gap: 12px;
        }

        /* Dark mode */
        @media (prefers-color-scheme: dark) {
          .config-editor {
            background: #2d3748;
            border-color: #4a5568;
          }

          .editor-header,
          .templates-header,
          .editor-footer {
            background: #1a202c;
            border-color: #4a5568;
          }

          .editor-title {
            color: #e2e8f0;
          }

          .config-textarea {
            background: #1a202c;
            color: #e2e8f0;
          }

          .editor-sidebar {
            background: #2d3748;
            border-color: #4a5568;
          }

          .quick-actions h4,
          .validation-panel h4 {
            color: #e2e8f0;
          }

          .quick-button {
            background: #4a5568;
            border-color: #718096;
            color: #e2e8f0;
          }

          .quick-button:hover {
            background: #718096;
          }

          .template-item {
            border-color: #4a5568;
          }

          .template-item:hover {
            background: #4a5568;
          }

          .template-name {
            color: #e2e8f0;
          }

          .template-description {
            color: #a0aec0;
          }

          .template-category {
            color: #718096;
          }

          .error-item {
            background: #742a2a;
          }

          .warning-item {
            background: #975a16;
          }

          .footer-info,
          .footer-shortcuts {
            color: #a0aec0;
          }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
          .config-editor {
            height: 500px;
          }

          .editor-sidebar {
            width: 200px;
          }

          .header-actions {
            flex-wrap: wrap;
            gap: 4px;
          }

          .footer-shortcuts {
            display: none;
          }
        }
      `}</style>
    </div>
  );
};