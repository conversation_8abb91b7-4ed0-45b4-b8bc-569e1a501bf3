import React, { useRef, useCallback, useEffect, useMemo } from 'react';
import Editor from '@monaco-editor/react';
import type { editor } from 'monaco-editor';
import { 
  Button, 
  Toast, 
  ToastTitle, 
  useToastController,
  Text,
  Spinner
} from '@fluentui/react-components';
import { 
  Save24Regular,
  ArrowCounterclockwise24Regular 
} from '@fluentui/react-icons';
import { useMonacoTheme } from '../hooks/useMonacoThemeSimple';
import { 
  MonacoLanguage, 
  isValidMonacoLanguage 
} from '../types/monaco';

interface EditorContainerProps {
  fileName: string | null;
  fileContent: string;
  onSave: (content: string) => Promise<void>;
  onContentChange?: (content: string, hasChanges: boolean) => void;
  isLoading?: boolean;
}

/**
 * Monaco编辑器容器组件
 * 
 * 功能特性:
 * - 智能主题切换和系统主题跟随
 * - 完整的编辑器错误处理和恢复机制
 * - 自动保存和快捷键支持
 * - 多语言文件类型识别
 * - 实时内容变化追踪
 */
export const EditorContainer: React.FC<EditorContainerProps> = ({
  fileName,
  fileContent,
  onSave,
  onContentChange,
  isLoading = false
}) => {
  const editorRef = useRef<editor.IStandaloneCodeEditor | null>(null);
  const mountedRef = useRef(true);
  const { dispatchToast } = useToastController();
  
  // 使用优化的Monaco主题管理
  const { 
    monacoTheme, 
    isThemeChanging, 
    applyThemeToEditor, 
    getThemeInfo 
  } = useMonacoTheme();

  // 编辑器状态管理
  const [currentContent, setCurrentContent] = React.useState(fileContent);
  const [hasChanges, setHasChanges] = React.useState(false);
  const [isSaving, setIsSaving] = React.useState(false);
  const [editorError, setEditorError] = React.useState<string | null>(null);

  // 组件卸载清理
  useEffect(() => {
    return () => {
      mountedRef.current = false;
    };
  }, []);

  // 当文件内容或文件名变化时更新编辑器
  useEffect(() => {
    if (mountedRef.current) {
      // 强制更新编辑器内容，解决文件切换时的内容串扰问题
      setCurrentContent(fileContent);
      setHasChanges(false);
      setEditorError(null);
      
      // 如果编辑器实例存在，强制设置其内容
      if (editorRef.current && typeof editorRef.current.setValue === 'function') {
        try {
          editorRef.current.setValue(fileContent);
          // 重置编辑器的撤销/重做历史
          if (editorRef.current.getModel) {
            const model = editorRef.current.getModel();
            if (model && model.pushStackElement) {
              model.pushStackElement();
            }
          }
        } catch (error) {
          console.error('更新编辑器内容失败:', error);
        }
      }
    }
  }, [fileContent, fileName]);

  // 优化的主题应用逻辑
  useEffect(() => {
    if (editorRef.current && mountedRef.current) {
      try {
        applyThemeToEditor(editorRef.current);
      } catch (error) {
        console.error('应用编辑器主题失败:', error);
        setEditorError('主题应用失败，请尝试刷新页面');
      }
    }
  }, [monacoTheme, applyThemeToEditor]);

  // 编辑器选项配置
  const editorOptions = useMemo(() => ({
    automaticLayout: true,
    minimap: { enabled: window.innerWidth > 1024 }, // 根据屏幕尺寸自适应小地图
    scrollBeyondLastLine: false,
    fontSize: 14,
    fontFamily: 'var(--fontFamilyMonospace)',
    tabSize: 2,
    insertSpaces: true,
    wordWrap: 'on' as const,
    lineNumbers: 'on' as const,
    folding: true,
    bracketMatching: 'always' as const,
    autoIndent: 'advanced' as const,
    formatOnPaste: true,
    formatOnType: true,
    // 性能优化选项
    renderLineHighlight: 'line' as const,
    cursorBlinking: 'smooth' as const,
    smoothScrolling: true,
    // 错误处理
    glyphMargin: true,
    contextmenu: true,
    // 无障碍支持
    ariaLabel: `正在编辑文件: ${fileName || '未知文件'}`
  }), [fileName]);

  // 处理编辑器内容变化
  const handleEditorChange = useCallback((value: string | undefined) => {
    if (!mountedRef.current) return;
    
    try {
      const newContent = value || '';
      const hasChanges = newContent !== fileContent;
      
      // 只有在内容真正发生变化时才更新状态
      if (newContent !== currentContent) {
        setCurrentContent(newContent);
        setHasChanges(hasChanges);
        setEditorError(null);
        
        onContentChange?.(newContent, hasChanges);
      }
    } catch (error) {
      console.error('处理编辑器内容变化失败:', error);
      setEditorError('内容更新失败');
    }
  }, [fileContent, currentContent, onContentChange]);

  // 处理编辑器挂载
  const handleEditorDidMount = useCallback((
    editorInstance: editor.IStandaloneCodeEditor, 
    monaco: typeof import('monaco-editor')
  ) => {
    if (!mountedRef.current) return undefined;
    
    try {
      editorRef.current = editorInstance;

      // 配置JSON语言支持和诊断
      if (monaco.languages?.json?.jsonDefaults) {
        monaco.languages.json.jsonDefaults.setDiagnosticsOptions({
          validate: true,
          allowComments: false,
          schemaValidation: 'error',
          enableSchemaRequest: true
        });
      }

      // 添加键盘快捷键
      const disposables: any[] = [];
      
      // 保存快捷键 (Ctrl+S 或 Cmd+S)
      const saveCommand = editorInstance.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, async () => {
        try {
          const currentValue = editorInstance.getValue();
          if (currentValue !== fileContent && !isSaving) {
            await onSave(currentValue);
          }
        } catch (error) {
          console.error('快捷键保存失败:', error);
        }
      });
      if (saveCommand) disposables.push(saveCommand);
      
      // 格式化快捷键 (Shift+Alt+F)
      const formatCommand = editorInstance.addCommand(monaco.KeyMod.Shift | monaco.KeyMod.Alt | monaco.KeyCode.KeyF, () => {
        try {
          editorInstance.trigger('keyboard', 'editor.action.formatDocument', {});
        } catch (error) {
          console.error('格式化文档失败:', error);
        }
      });
      if (formatCommand) disposables.push(formatCommand);

      // 初始主题应用
      applyThemeToEditor(editorInstance);
      
      // 清理函数
      return () => {
        disposables.forEach(d => {
          if (d && typeof d.dispose === 'function') {
            d.dispose();
          }
        });
      };
    } catch (error) {
      console.error('编辑器挂载失败:', error);
      setEditorError('编辑器初始化失败');
      return undefined;
    }
  }, [applyThemeToEditor, onSave, fileContent, isSaving]);

  // 显示Toast通知 - 增强错误处理
  const showToast = useCallback((title: string, intent: 'success' | 'error' | 'warning' = 'success') => {
    try {
      dispatchToast(
        <Toast>
          <ToastTitle>{title}</ToastTitle>
        </Toast>,
        { intent, timeout: intent === 'error' ? 5000 : 3000 } // 错误消息显示更长时间
      );
    } catch (error) {
      console.error('显示Toast通知失败:', error);
    }
  }, [dispatchToast]);

  // 保存文件 - 增强错误处理和状态管理
  const handleSave = useCallback(async () => {
    if (!hasChanges || isSaving || !mountedRef.current) return;

    setIsSaving(true);
    setEditorError(null);
    
    try {
      await onSave(currentContent);
      
      if (mountedRef.current) {
        setHasChanges(false);
        showToast('文件保存成功');
      }
    } catch (error) {
      console.error('保存文件失败:', error);
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      
      if (mountedRef.current) {
        setEditorError(`保存失败: ${errorMessage}`);
        showToast(`文件保存失败: ${errorMessage}`, 'error');
      }
    } finally {
      if (mountedRef.current) {
        setIsSaving(false);
      }
    }
  }, [hasChanges, isSaving, currentContent, onSave, showToast]);

  // 重置更改 - 增强安全性
  const handleReset = useCallback(() => {
    if (!mountedRef.current) return;
    
    try {
      setCurrentContent(fileContent);
      setHasChanges(false);
      setEditorError(null);
      
      if (editorRef.current) {
        editorRef.current.setValue(fileContent);
      }
      
      showToast('已重置到原始内容', 'warning');
    } catch (error) {
      console.error('重置文件内容失败:', error);
      setEditorError('重置失败');
    }
  }, [fileContent, showToast]);

  // 获取文件语言类型 - 扩展支持更多语言
  const getLanguage = useCallback((fileName: string): MonacoLanguage => {
    try {
      const ext = fileName.split('.').pop()?.toLowerCase();
      if (!ext) return 'plaintext';
      
      const languageMap: Record<string, MonacoLanguage> = {
        // 配置文件
        'json': 'json',
        'jsonc': 'json' as MonacoLanguage, // JSON with comments
        // JavaScript/TypeScript
        'js': 'javascript',
        'jsx': 'javascript',
        'ts': 'typescript',
        'tsx': 'typescript',
        'mjs': 'javascript',
        'cjs': 'javascript',
        // 标记语言
        'html': 'html',
        'htm': 'html',
        'xml': 'xml',
        'svg': 'xml',
        // 样式表
        'css': 'css',
        'scss': 'scss',
        'sass': 'sass',
        'less': 'less',
        // 数据格式
        'yaml': 'yaml',
        'yml': 'yaml',
        'toml': 'toml',
        'ini': 'ini',
        // 编程语言
        'py': 'python',
        'java': 'java',
        'c': 'c',
        'cpp': 'cpp',
        'h': 'c',
        'hpp': 'cpp',
        'cs': 'csharp',
        'php': 'php',
        'rb': 'ruby',
        'go': 'go',
        'rs': 'rust',
        // 脚本和配置
        'sh': 'shell',
        'bash': 'shell',
        'zsh': 'shell',
        'fish': 'shell',
        'ps1': 'powershell',
        'bat': 'bat',
        'cmd': 'bat',
        // 文档
        'md': 'markdown',
        'markdown': 'markdown',
        'tex': 'latex',
        // 其他
        'sql': 'sql',
        'dockerfile': 'dockerfile',
        'makefile': 'makefile'
      };
      
      const language = languageMap[ext] || 'plaintext';
      
      // 验证语言类型
      if (!isValidMonacoLanguage(language)) {
        console.warn(`不支持的语言类型: ${language}, 回退到plaintext`);
        return 'plaintext';
      }
      
      return language;
    } catch (error) {
      console.warn('获取文件语言类型失败:', error);
      return 'plaintext';
    }
  }, []);

  if (!fileName) {
    return null;
  }

  return (
    <div style={{ 
      height: '100%', 
      display: 'flex', 
      flexDirection: 'column',
      backgroundColor: 'var(--colorNeutralBackground1)'
    }}>
      {/* 编辑器工具栏 */}
      <div style={{
        padding: '8px 16px',
        borderBottom: '1px solid var(--colorNeutralStroke2)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        backgroundColor: 'var(--colorNeutralBackground2)'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <Text weight="semibold">{fileName}</Text>
          
          {/* 文件状态指示器 */}
          {hasChanges && (
            <div 
              style={{
                width: '8px',
                height: '8px',
                borderRadius: '50%',
                backgroundColor: 'var(--colorBrandBackground)'
              }}
              title="文件已修改"
            />
          )}
          
          {/* 主题变化指示器 */}
          {isThemeChanging && (
            <Spinner size="extra-tiny" title="主题切换中..." />
          )}
          
          {/* 错误状态指示器 */}
          {editorError && (
            <Text 
              size={200} 
              style={{ 
                color: 'var(--colorPaletteRedForeground1)',
                fontStyle: 'italic'
              }}
              title={editorError}
            >
              错误
            </Text>
          )}
          
          {/* 主题信息 */}
          <Text 
            size={100} 
            style={{ 
              color: 'var(--colorNeutralForeground3)',
              marginLeft: 'auto'
            }}
            title={`当前主题: ${getThemeInfo().displayName}`}
          >
            {getThemeInfo().displayName}
          </Text>
        </div>
        
        <div style={{ display: 'flex', gap: '8px' }}>
          <Button
            size="small"
            appearance="subtle"
            icon={<ArrowCounterclockwise24Regular />}
            onClick={handleReset}
            disabled={!hasChanges || isLoading || isSaving}
            title="重置更改 - 恢复到原始内容"
          >
            重置
          </Button>
          <Button
            size="small"
            appearance="primary"
            icon={isSaving ? <Spinner size="tiny" /> : <Save24Regular />}
            onClick={handleSave}
            disabled={!hasChanges || isSaving || isLoading || !!editorError}
            title={
              editorError 
                ? `无法保存: ${editorError}` 
                : hasChanges 
                  ? "保存文件 (Ctrl+S)" 
                  : "没有变更需要保存"
            }
          >
            {isSaving ? '保存中...' : '保存'}
          </Button>
        </div>
      </div>

      {/* Monaco Editor */}
      <div style={{ flex: 1, position: 'relative' }}>
        {/* 错误状态覆盖层 */}
        {editorError && (
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            padding: '12px 16px',
            backgroundColor: 'var(--colorPaletteRedBackground1)',
            borderBottom: '1px solid var(--colorPaletteRedBorder1)',
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            zIndex: 15
          }}>
            <Text 
              size={200} 
              style={{ color: 'var(--colorPaletteRedForeground1)' }}
            >
              编辑器错误: {editorError}
            </Text>
            <Button
              size="small"
              appearance="subtle"
              onClick={() => setEditorError(null)}
              title="关闭错误提示"
            >
              ×
            </Button>
          </div>
        )}
        
        {/* 加载状态 */}
        {isLoading ? (
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'var(--colorNeutralBackground1)',
            zIndex: 10,
            gap: '16px'
          }}>
            <Spinner size="medium" />
            <Text size={300}>正在加载编辑器...</Text>
          </div>
        ) : (
          <Editor
            height="100%"
            language={getLanguage(fileName)}
            value={currentContent}
            theme={monacoTheme}
            options={editorOptions}
            onChange={handleEditorChange}
            onMount={handleEditorDidMount}
            loading={
              <div style={{ 
                padding: '40px', 
                textAlign: 'center',
                color: 'var(--colorNeutralForeground3)',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                gap: '16px'
              }}>
                <Spinner size="medium" />
                <Text size={300}>正在加载Monaco编辑器...</Text>
                <Text size={200} style={{ color: 'var(--colorNeutralForeground4)' }}>
                  首次加载可能需要几秒钟
                </Text>
              </div>
            }
          />
        )}
      </div>
    </div>
  );
};