use serde::{Deserialize, Serialize};
use std::fmt;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct AppError {
    pub code: String,
    pub message: String,
}

impl fmt::Display for AppError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "[{}] {}", self.code, self.message)
    }
}

impl std::error::Error for AppError {}

impl From<AppError> for String {
    fn from(error: AppError) -> Self {
        error.to_string()
    }
}

// 错误码常量定义
pub mod error_codes {
    pub const FILE_NOT_FOUND: &str = "E001";
    pub const FILE_READ_ERROR: &str = "E002";
    pub const FILE_WRITE_ERROR: &str = "E003";
    pub const DIRECTORY_CREATE_ERROR: &str = "E004";
    pub const PERMISSION_DENIED: &str = "E005";
    pub const INVALID_PATH: &str = "E006";
    pub const CONFIG_PARSE_ERROR: &str = "E007";
    pub const CONFIG_NOT_FOUND: &str = "E008";
    pub const CONFIG_ALREADY_EXISTS: &str = "E009";
    pub const VALIDATION_ERROR: &str = "E010";
    pub const DIALOG_CANCELLED: &str = "E011";
    pub const INVALID_CHARACTER: &str = "E012";
    pub const METADATA_ERROR: &str = "E013";
    pub const FILE_OPERATION_ERROR: &str = "E014";
    pub const BACKUP_ERROR: &str = "E015";
}

pub struct ErrorHandler;

impl ErrorHandler {
    pub fn new_error(code: &str, message: &str) -> AppError {
        AppError {
            code: code.to_string(),
            message: message.to_string(),
        }
    }

    // 文件操作相关错误
    pub fn file_not_found(path: &str) -> AppError {
        Self::new_error(error_codes::FILE_NOT_FOUND, &format!("文件不存在: {}", path))
    }

    pub fn file_read_error(path: &str, detail: &str) -> AppError {
        Self::new_error(error_codes::FILE_READ_ERROR, &format!("读取文件失败 {}: {}", path, detail))
    }

    pub fn file_write_error(path: &str, detail: &str) -> AppError {
        Self::new_error(error_codes::FILE_WRITE_ERROR, &format!("写入文件失败 {}: {}", path, detail))
    }

    pub fn directory_create_error(path: &str, detail: &str) -> AppError {
        Self::new_error(error_codes::DIRECTORY_CREATE_ERROR, &format!("创建目录失败 {}: {}", path, detail))
    }

    pub fn permission_denied(path: &str) -> AppError {
        Self::new_error(error_codes::PERMISSION_DENIED, &format!("权限不足: {}", path))
    }

    pub fn invalid_path(path: &str) -> AppError {
        Self::new_error(error_codes::INVALID_PATH, &format!("无效路径: {}", path))
    }

    // 配置相关错误
    pub fn config_parse_error(detail: &str) -> AppError {
        Self::new_error(error_codes::CONFIG_PARSE_ERROR, &format!("配置解析失败: {}", detail))
    }

    pub fn config_not_found(name: &str) -> AppError {
        Self::new_error(error_codes::CONFIG_NOT_FOUND, &format!("配置不存在: {}", name))
    }

    pub fn config_already_exists(name: &str) -> AppError {
        Self::new_error(error_codes::CONFIG_ALREADY_EXISTS, &format!("配置已存在: {}", name))
    }

    pub fn validation_error(detail: &str) -> AppError {
        Self::new_error(error_codes::VALIDATION_ERROR, &format!("验证失败: {}", detail))
    }

    // 对话框相关错误
    pub fn dialog_cancelled() -> AppError {
        Self::new_error(error_codes::DIALOG_CANCELLED, "用户取消了对话框操作")
    }

    pub fn invalid_character(detail: &str) -> AppError {
        Self::new_error(error_codes::INVALID_CHARACTER, &format!("包含无效字符: {}", detail))
    }

    // 其他通用错误
    pub fn metadata_error(path: &str, detail: &str) -> AppError {
        Self::new_error(error_codes::METADATA_ERROR, &format!("获取文件元数据失败 {}: {}", path, detail))
    }

    pub fn file_operation_error(operation: &str, detail: &str) -> AppError {
        Self::new_error(error_codes::FILE_OPERATION_ERROR, &format!("文件操作失败 {}: {}", operation, detail))
    }

    pub fn backup_error(detail: &str) -> AppError {
        Self::new_error(error_codes::BACKUP_ERROR, &format!("备份失败: {}", detail))
    }
}

// 辅助宏，简化错误创建
#[macro_export]
macro_rules! app_error {
    ($code:expr, $msg:expr) => {
        crate::error_handler::ErrorHandler::new_error($code, $msg)
    };
    ($code:expr, $fmt:expr, $($arg:tt)*) => {
        crate::error_handler::ErrorHandler::new_error($code, &format!($fmt, $($arg)*))
    };
}