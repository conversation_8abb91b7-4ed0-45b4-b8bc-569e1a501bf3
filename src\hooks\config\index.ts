/**
 * 配置管理Hook模块统一导出
 * 
 * 架构说明：
 * - useConfig: 主要的组合Hook，提供完整的配置管理功能
 * - useConfigState: 共享状态管理，可单独使用
 * - useAppSettings: 应用设置专用Hook
 * - useConfigList: 配置列表管理专用Hook
 * - useConfigEditor: 配置编辑器专用Hook
 * - useConfigOperations: CRUD操作专用Hook
 */

// 主要Hook - 推荐使用
export { useConfig } from './useConfig';

// 专门化Hook - 按需使用
export { useConfigState } from './useConfigState';
export { useAppSettings } from './useAppSettings';
export { useConfigList } from './useConfigList';
export { useConfigEditor } from './useConfigEditor';
export { useConfigOperations } from './useConfigOperations';