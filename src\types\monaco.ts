/**
 * Monaco Editor 相关类型定义
 * 提供严格的类型安全和完整的编辑器API支持
 */

/**
 * Monaco Editor 支持的主题类型
 */
export type MonacoTheme = 'vs' | 'vs-dark' | 'hc-black' | 'hc-light';

/**
 * Monaco Editor 支持的语言类型
 */
export type MonacoLanguage = 
  | 'plaintext'
  | 'json'
  | 'javascript'
  | 'typescript'
  | 'html'
  | 'css'
  | 'scss'
  | 'sass'
  | 'less'
  | 'yaml'
  | 'xml'
  | 'markdown'
  | 'python'
  | 'java'
  | 'c'
  | 'cpp'
  | 'csharp'
  | 'php'
  | 'ruby'
  | 'go'
  | 'rust'
  | 'shell'
  | 'powershell'
  | 'bat'
  | 'sql'
  | 'latex'
  | 'dockerfile'
  | 'makefile'
  | 'toml'
  | 'ini';

/**
 * Monaco Editor 实例接口
 */
export interface MonacoEditorInstance {
  getValue(): string;
  setValue(value: string): void;
  updateOptions(options: Partial<MonacoEditorOptions>): void;
  addCommand(keybinding: number, handler: () => void): string | null;
  trigger(source: string, handlerId: string, payload?: any): void;
  dispose(): void;
  focus(): void;
  hasTextFocus(): boolean;
  getModel(): MonacoTextModel | null;
  setModel(model: MonacoTextModel | null): void;
}

/**
 * Monaco 文本模型接口
 */
export interface MonacoTextModel {
  getValue(): string;
  setValue(value: string): void;
  getLanguageId(): string;
  dispose(): void;
}

/**
 * Monaco Editor 选项接口
 */
export interface MonacoEditorOptions {
  automaticLayout?: boolean;
  minimap?: {
    enabled: boolean;
    side?: 'right' | 'left';
    showSlider?: 'always' | 'mouseover';
    renderCharacters?: boolean;
    maxColumn?: number;
  };
  scrollBeyondLastLine?: boolean;
  fontSize?: number;
  fontFamily?: string;
  tabSize?: number;
  insertSpaces?: boolean;
  wordWrap?: 'off' | 'on' | 'wordWrapColumn' | 'bounded';
  lineNumbers?: 'on' | 'off' | 'relative' | 'interval';
  folding?: boolean;
  bracketMatching?: 'always' | 'near' | 'never';
  autoIndent?: 'none' | 'keep' | 'brackets' | 'advanced' | 'full';
  formatOnPaste?: boolean;
  formatOnType?: boolean;
  renderLineHighlight?: 'none' | 'gutter' | 'line' | 'all';
  cursorBlinking?: 'blink' | 'smooth' | 'phase' | 'expand' | 'solid';
  smoothScrolling?: boolean;
  glyphMargin?: boolean;
  contextmenu?: boolean;
  ariaLabel?: string;
  theme?: MonacoTheme;
  readOnly?: boolean;
  selectOnLineNumbers?: boolean;
  mouseWheelZoom?: boolean;
}

/**
 * Monaco 编辑器挂载回调参数
 */
export interface MonacoEditorMountParams {
  editor: MonacoEditorInstance;
  monaco: MonacoGlobal;
}

/**
 * Monaco 全局对象接口
 */
export interface MonacoGlobal {
  editor: {
    defineTheme(themeName: string, themeData: any): void;
    setTheme(theme: MonacoTheme): void;
    createModel(value: string, language?: string, uri?: any): MonacoTextModel;
  };
  languages: {
    json?: {
      jsonDefaults: {
        setDiagnosticsOptions(options: JsonDiagnosticsOptions): void;
      };
    };
    typescript?: {
      typescriptDefaults: any;
      javascriptDefaults: any;
    };
    registerCompletionItemProvider?: (languageId: string, provider: any) => any;
    setMonarchTokensProvider?: (languageId: string, provider: any) => any;
  };
  KeyMod: {
    CtrlCmd: number;
    Shift: number;
    Alt: number;
    WinCtrl: number;
  };
  KeyCode: {
    KeyS: number;
    KeyF: number;
    Enter: number;
    Escape: number;
    Tab: number;
    Backspace: number;
    Delete: number;
    [key: string]: number;
  };
}

/**
 * JSON 诊断选项接口
 */
export interface JsonDiagnosticsOptions {
  validate?: boolean;
  allowComments?: boolean;
  schemaValidation?: 'error' | 'warning' | 'ignore';
  enableSchemaRequest?: boolean;
  schemaRequest?: 'error' | 'warning' | 'ignore';
  trailingCommas?: 'error' | 'warning' | 'ignore';
}

/**
 * Monaco Editor 错误类型
 */
export interface MonacoError extends Error {
  name: 'MonacoError';
  code?: string;
  details?: any;
}

/**
 * 编辑器状态接口
 */
export interface EditorState {
  content: string;
  hasChanges: boolean;
  isSaving: boolean;
  isLoading: boolean;
  error: string | null;
  language: MonacoLanguage;
  theme: MonacoTheme;
}

/**
 * 编辑器事件处理器接口
 */
export interface EditorEventHandlers {
  onContentChange?: (content: string, hasChanges: boolean) => void;
  onSave?: (content: string) => Promise<void>;
  onError?: (error: MonacoError) => void;
  onThemeChange?: (theme: MonacoTheme) => void;
  onLanguageChange?: (language: MonacoLanguage) => void;
}

/**
 * 编辑器配置接口
 */
export interface EditorConfig {
  fileName: string | null;
  fileContent: string;
  language?: MonacoLanguage;
  theme?: MonacoTheme;
  options?: Partial<MonacoEditorOptions>;
  isLoading?: boolean;
  readOnly?: boolean;
  autoSave?: boolean;
  autoSaveDelay?: number;
}

/**
 * 主题配置接口
 */
export interface ThemeConfiguration {
  light: MonacoTheme;
  dark: MonacoTheme;
  highContrast?: {
    light: MonacoTheme;
    dark: MonacoTheme;
  };
}

/**
 * 主题信息接口
 */
export interface ThemeInfo {
  theme: MonacoTheme;
  isDark: boolean;
  isLight: boolean;
  isHighContrast: boolean;
  mode: 'light' | 'dark' | 'system';
  displayName: string;
}

/**
 * 工具函数：创建Monaco错误
 */
export const createMonacoError = (message: string, code?: string, details?: any): MonacoError => {
  const error = new Error(message) as MonacoError;
  error.name = 'MonacoError';
  error.code = code;
  error.details = details;
  return error;
};

/**
 * 工具函数：检查是否为Monaco错误
 */
export const isMonacoError = (error: any): error is MonacoError => {
  return error && error.name === 'MonacoError';
};

/**
 * 工具函数：验证Monaco主题
 */
export const isValidMonacoTheme = (theme: string): theme is MonacoTheme => {
  const validThemes: MonacoTheme[] = ['vs', 'vs-dark', 'hc-black', 'hc-light'];
  return validThemes.includes(theme as MonacoTheme);
};

/**
 * 工具函数：验证Monaco语言
 */
export const isValidMonacoLanguage = (language: string): language is MonacoLanguage => {
  const validLanguages: MonacoLanguage[] = [
    'plaintext', 'json', 'javascript', 'typescript', 'html', 'css', 
    'scss', 'sass', 'less', 'yaml', 'xml', 'markdown', 'python', 
    'java', 'c', 'cpp', 'csharp', 'php', 'ruby', 'go', 'rust', 
    'shell', 'powershell', 'bat', 'sql', 'latex', 'dockerfile', 
    'makefile', 'toml', 'ini'
  ];
  return validLanguages.includes(language as MonacoLanguage);
};