use serde_json::{Value, Error as JsonError};
use serde::{Deserialize, Serialize};
use std::fmt;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ValidationError {
    pub line: usize,
    pub column: usize,
    pub message: String,
    pub error_type: ValidationErrorType,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum ValidationErrorType {
    SyntaxError,
    InvalidFormat,
    MissingField,
    InvalidValue,
    TypeMismatch,
}

impl fmt::Display for ValidationError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(
            f,
            "第{}行第{}列: {} ({})",
            self.line,
            self.column,
            self.message,
            match self.error_type {
                ValidationErrorType::SyntaxError => "语法错误",
                ValidationErrorType::InvalidFormat => "格式错误",
                ValidationErrorType::MissingField => "缺少字段",
                ValidationErrorType::InvalidValue => "无效值",
                ValidationErrorType::TypeMismatch => "类型不匹配",
            }
        )
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationResult {
    pub is_valid: bool,
    pub errors: Vec<ValidationError>,
    pub warnings: Vec<String>,
    pub formatted_json: Option<String>,
}

pub struct JsonValidator;

impl JsonValidator {
    // 基础JSON格式验证
    pub fn validate_json_format(content: &str) -> ValidationResult {
        let mut result = ValidationResult {
            is_valid: false,
            errors: Vec::new(),
            warnings: Vec::new(),
            formatted_json: None,
        };

        // 检查空内容
        if content.trim().is_empty() {
            result.errors.push(ValidationError {
                line: 1,
                column: 1,
                message: "JSON内容不能为空".to_string(),
                error_type: ValidationErrorType::InvalidFormat,
            });
            return result;
        }

        // 尝试解析JSON
        match serde_json::from_str::<Value>(content) {
            Ok(value) => {
                result.is_valid = true;
                
                // 格式化JSON
                match serde_json::to_string_pretty(&value) {
                    Ok(formatted) => {
                        result.formatted_json = Some(formatted);
                    }
                    Err(_) => {
                        result.warnings.push("无法格式化JSON".to_string());
                    }
                }

                // 检查常见问题
                result.warnings.extend(Self::check_common_issues(&value));
            }
            Err(e) => {
                result.errors.push(Self::parse_json_error(e, content));
            }
        }

        result
    }

    // 解析JSON错误信息
    fn parse_json_error(error: JsonError, content: &str) -> ValidationError {
        let error_msg = error.to_string();
        
        // 尝试从错误信息中提取行列信息
        let (line, column) = Self::extract_error_position(&error_msg, content);
        
        let (message, error_type) = if error_msg.contains("expected") {
            ("JSON语法错误: 期望的字符不匹配".to_string(), ValidationErrorType::SyntaxError)
        } else if error_msg.contains("trailing") {
            ("JSON语法错误: 存在多余的字符".to_string(), ValidationErrorType::SyntaxError)
        } else if error_msg.contains("EOF") {
            ("JSON语法错误: 文件意外结束".to_string(), ValidationErrorType::SyntaxError)
        } else if error_msg.contains("invalid") {
            ("JSON语法错误: 无效的字符或格式".to_string(), ValidationErrorType::InvalidFormat)
        } else {
            (format!("JSON解析错误: {}", error_msg), ValidationErrorType::SyntaxError)
        };

        ValidationError {
            line,
            column,
            message,
            error_type,
        }
    }

    // 从错误信息中提取位置信息
    fn extract_error_position(error_msg: &str, _content: &str) -> (usize, usize) {
        // 简单的位置估算，实际实现可能更复杂
        if let Some(pos_str) = error_msg.split("at line ").nth(1) {
            if let Some(line_str) = pos_str.split(' ').next() {
                if let Ok(line) = line_str.parse::<usize>() {
                    return (line, 1);
                }
            }
        }

        // 如果无法解析位置，尝试其他方法
        if error_msg.contains("column") {
            // 可以添加更复杂的列位置解析逻辑
        }

        // 默认返回第1行第1列
        (1, 1)
    }

    // 检查常见问题
    fn check_common_issues(value: &Value) -> Vec<String> {
        let mut warnings = Vec::new();

        match value {
            Value::Object(obj) => {
                // 检查空对象
                if obj.is_empty() {
                    warnings.push("配置对象为空".to_string());
                }

                // 检查重复的键（在解析过程中已经处理）
                // JSON标准不允许重复键，serde_json会自动处理

                // 检查过深的嵌套
                let depth = Self::calculate_depth(value);
                if depth > 10 {
                    warnings.push(format!("JSON嵌套层次过深 ({}层)，可能影响性能", depth));
                }

                // 检查超长字符串
                Self::check_long_strings(obj, &mut warnings);
            }
            Value::Array(arr) => {
                if arr.is_empty() {
                    warnings.push("配置数组为空".to_string());
                } else if arr.len() > 1000 {
                    warnings.push(format!("数组元素过多 ({}个)，可能影响性能", arr.len()));
                }
            }
            _ => {
                warnings.push("根级别应该是对象或数组".to_string());
            }
        }

        warnings
    }

    // 计算JSON嵌套深度
    fn calculate_depth(value: &Value) -> usize {
        match value {
            Value::Object(obj) => {
                1 + obj.values()
                    .map(|v| Self::calculate_depth(v))
                    .max()
                    .unwrap_or(0)
            }
            Value::Array(arr) => {
                1 + arr.iter()
                    .map(|v| Self::calculate_depth(v))
                    .max()
                    .unwrap_or(0)
            }
            _ => 0,
        }
    }

    // 检查超长字符串
    fn check_long_strings(obj: &serde_json::Map<String, Value>, warnings: &mut Vec<String>) {
        for (key, value) in obj {
            match value {
                Value::String(s) => {
                    if s.len() > 10000 {
                        warnings.push(format!("字段 '{}' 的值过长 ({}字符)", key, s.len()));
                    }
                }
                Value::Object(nested) => {
                    Self::check_long_strings(nested, warnings);
                }
                Value::Array(arr) => {
                    for item in arr {
                        if let Value::Object(nested) = item {
                            Self::check_long_strings(nested, warnings);
                        }
                    }
                }
                _ => {}
            }
        }
    }

    // 验证特定配置格式
    pub fn validate_config_structure(content: &str, required_fields: &[&str]) -> ValidationResult {
        let mut result = Self::validate_json_format(content);

        if !result.is_valid {
            return result;
        }

        // 解析JSON值
        if let Ok(value) = serde_json::from_str::<Value>(content) {
            if let Value::Object(obj) = &value {
                // 检查必需字段
                for field in required_fields {
                    if !obj.contains_key(*field) {
                        result.errors.push(ValidationError {
                            line: 1,
                            column: 1,
                            message: format!("缺少必需字段: '{}'", field),
                            error_type: ValidationErrorType::MissingField,
                        });
                        result.is_valid = false;
                    }
                }
            } else {
                result.errors.push(ValidationError {
                    line: 1,
                    column: 1,
                    message: "配置必须是一个JSON对象".to_string(),
                    error_type: ValidationErrorType::TypeMismatch,
                });
                result.is_valid = false;
            }
        }

        result
    }

    // 快速验证JSON格式（仅检查语法）
    pub fn quick_validate(content: &str) -> bool {
        serde_json::from_str::<Value>(content).is_ok()
    }

    // 格式化JSON字符串
    pub fn format_json(content: &str) -> Result<String, String> {
        match serde_json::from_str::<Value>(content) {
            Ok(value) => {
                serde_json::to_string_pretty(&value)
                    .map_err(|e| format!("格式化JSON失败: {}", e))
            }
            Err(e) => Err(format!("JSON格式错误: {}", e))
        }
    }

    // 压缩JSON字符串
    pub fn minify_json(content: &str) -> Result<String, String> {
        match serde_json::from_str::<Value>(content) {
            Ok(value) => {
                serde_json::to_string(&value)
                    .map_err(|e| format!("压缩JSON失败: {}", e))
            }
            Err(e) => Err(format!("JSON格式错误: {}", e))
        }
    }

    // 比较两个JSON是否相等（忽略格式）
    pub fn json_equals(content1: &str, content2: &str) -> Result<bool, String> {
        let value1: Value = serde_json::from_str(content1)
            .map_err(|e| format!("第一个JSON格式错误: {}", e))?;
        let value2: Value = serde_json::from_str(content2)
            .map_err(|e| format!("第二个JSON格式错误: {}", e))?;

        Ok(value1 == value2)
    }

    // 获取JSON大小统计
    pub fn get_json_stats(content: &str) -> Result<JsonStats, String> {
        let value: Value = serde_json::from_str(content)
            .map_err(|e| format!("JSON格式错误: {}", e))?;

        Ok(JsonStats {
            total_size: content.len(),
            max_depth: Self::calculate_depth(&value),
            object_count: Self::count_objects(&value),
            array_count: Self::count_arrays(&value),
            string_count: Self::count_strings(&value),
            number_count: Self::count_numbers(&value),
            boolean_count: Self::count_booleans(&value),
            null_count: Self::count_nulls(&value),
        })
    }

    // 统计不同类型的值
    fn count_objects(value: &Value) -> usize {
        match value {
            Value::Object(obj) => {
                1 + obj.values().map(|v| Self::count_objects(v)).sum::<usize>()
            }
            Value::Array(arr) => {
                arr.iter().map(|v| Self::count_objects(v)).sum()
            }
            _ => 0,
        }
    }

    fn count_arrays(value: &Value) -> usize {
        match value {
            Value::Array(arr) => {
                1 + arr.iter().map(|v| Self::count_arrays(v)).sum::<usize>()
            }
            Value::Object(obj) => {
                obj.values().map(|v| Self::count_arrays(v)).sum()
            }
            _ => 0,
        }
    }

    fn count_strings(value: &Value) -> usize {
        match value {
            Value::String(_) => 1,
            Value::Object(obj) => {
                obj.values().map(|v| Self::count_strings(v)).sum()
            }
            Value::Array(arr) => {
                arr.iter().map(|v| Self::count_strings(v)).sum()
            }
            _ => 0,
        }
    }

    fn count_numbers(value: &Value) -> usize {
        match value {
            Value::Number(_) => 1,
            Value::Object(obj) => {
                obj.values().map(|v| Self::count_numbers(v)).sum()
            }
            Value::Array(arr) => {
                arr.iter().map(|v| Self::count_numbers(v)).sum()
            }
            _ => 0,
        }
    }

    fn count_booleans(value: &Value) -> usize {
        match value {
            Value::Bool(_) => 1,
            Value::Object(obj) => {
                obj.values().map(|v| Self::count_booleans(v)).sum()
            }
            Value::Array(arr) => {
                arr.iter().map(|v| Self::count_booleans(v)).sum()
            }
            _ => 0,
        }
    }

    fn count_nulls(value: &Value) -> usize {
        match value {
            Value::Null => 1,
            Value::Object(obj) => {
                obj.values().map(|v| Self::count_nulls(v)).sum()
            }
            Value::Array(arr) => {
                arr.iter().map(|v| Self::count_nulls(v)).sum()
            }
            _ => 0,
        }
    }
}

#[derive(Debug, Clone)]
pub struct JsonStats {
    pub total_size: usize,
    pub max_depth: usize,
    pub object_count: usize,
    pub array_count: usize,
    pub string_count: usize,
    pub number_count: usize,
    pub boolean_count: usize,
    pub null_count: usize,
}