/**
 * 事件管理器 - 统一管理事件监听器，防止内存泄漏
 */

type EventListener = (...args: any[]) => void;
type CleanupFunction = () => void;

/**
 * 事件监听器管理类
 * 提供统一的事件监听器注册、清理机制
 */
export class EventManager {
  private listeners = new Map<string, Set<CleanupFunction>>();

  /**
   * 添加事件监听器
   * @param key 监听器标识key
   * @param target 事件目标
   * @param eventType 事件类型
   * @param listener 监听器函数
   * @param options 事件选项
   * @returns 清理函数
   */
  addListener<T extends EventTarget>(
    key: string,
    target: T,
    eventType: string,
    listener: EventListener,
    options?: boolean | AddEventListenerOptions
  ): CleanupFunction {
    // 添加事件监听器
    target.addEventListener(eventType, listener, options);

    // 创建清理函数
    const cleanup = () => {
      target.removeEventListener(eventType, listener, options);
    };

    // 注册到管理器
    if (!this.listeners.has(key)) {
      this.listeners.set(key, new Set());
    }
    this.listeners.get(key)!.add(cleanup);

    return cleanup;
  }

  /**
   * 添加媒体查询监听器
   * @param key 监听器标识key
   * @param query 媒体查询字符串
   * @param listener 监听器函数
   * @returns 清理函数
   */
  addMediaQueryListener(
    key: string,
    query: string,
    listener: (event: MediaQueryListEvent) => void
  ): CleanupFunction {
    const mediaQuery = window.matchMedia(query);
    
    // 兼容旧版本浏览器
    if (mediaQuery.addEventListener) {
      mediaQuery.addEventListener('change', listener);
    } else {
      // 旧版本浏览器使用 addListener
      (mediaQuery as any).addListener(listener);
    }

    const cleanup = () => {
      if (mediaQuery.removeEventListener) {
        mediaQuery.removeEventListener('change', listener);
      } else {
        (mediaQuery as any).removeListener(listener);
      }
    };

    // 注册到管理器
    if (!this.listeners.has(key)) {
      this.listeners.set(key, new Set());
    }
    this.listeners.get(key)!.add(cleanup);

    return cleanup;
  }

  /**
   * 移除指定key的所有监听器
   * @param key 监听器标识key
   */
  removeListeners(key: string): void {
    const cleanupFunctions = this.listeners.get(key);
    if (cleanupFunctions) {
      cleanupFunctions.forEach(cleanup => cleanup());
      this.listeners.delete(key);
    }
  }

  /**
   * 清理所有监听器
   */
  cleanup(): void {
    this.listeners.forEach((cleanupFunctions) => {
      cleanupFunctions.forEach(cleanup => cleanup());
    });
    this.listeners.clear();
  }

  /**
   * 获取当前监听器数量（用于调试）
   */
  getListenerCount(): number {
    let count = 0;
    this.listeners.forEach((cleanupFunctions) => {
      count += cleanupFunctions.size;
    });
    return count;
  }

  /**
   * 检查是否有指定key的监听器
   * @param key 监听器标识key
   */
  hasListeners(key: string): boolean {
    return this.listeners.has(key) && this.listeners.get(key)!.size > 0;
  }
}

/**
 * 全局事件管理器实例
 */
export const globalEventManager = new EventManager();

/**
 * 在组件卸载时自动清理事件监听器的Hook工具
 * @param key 组件唯一标识
 * @returns 事件管理器实例
 */
export function useEventManager(key: string): EventManager {
  const manager = new EventManager();

  // 重写cleanup方法，确保在组件卸载时自动调用
  const originalCleanup = manager.cleanup.bind(manager);
  manager.cleanup = () => {
    originalCleanup();
    globalEventManager.removeListeners(key);
  };

  return manager;
}

/**
 * 创建安全的事件监听器
 * 自动处理错误和清理
 */
export function createSafeEventListener<T extends Event>(
  handler: (event: T) => void,
  errorHandler?: (error: Error) => void
): (event: T) => void {
  return (event: T) => {
    try {
      handler(event);
    } catch (error) {
      console.error('事件监听器执行失败:', error);
      if (errorHandler) {
        errorHandler(error as Error);
      }
    }
  };
}