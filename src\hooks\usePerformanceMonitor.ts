import React, { useEffect, useRef, useState, useCallback } from 'react';

/**
 * 性能监控指标接口
 */
export interface PerformanceMetrics {
  renderTime: number;
  memoryUsage: number;
  eventListenerCount: number;
  lastUpdateTime: number;
}

/**
 * 性能监控钩子
 * 用于实时监控组件性能和资源使用情况
 */
export const usePerformanceMonitor = (componentName: string) => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    renderTime: 0,
    memoryUsage: 0,
    eventListenerCount: 0,
    lastUpdateTime: Date.now()
  });
  
  const renderStartTime = useRef<number>(0);
  const observerRef = useRef<PerformanceObserver | null>(null);

  // 开始性能测量
  const startMeasure = useCallback(() => {
    renderStartTime.current = performance.now();
  }, []);

  // 结束性能测量
  const endMeasure = useCallback(() => {
    if (renderStartTime.current > 0) {
      const renderTime = performance.now() - renderStartTime.current;
      
      setMetrics(prev => ({
        ...prev,
        renderTime,
        lastUpdateTime: Date.now()
      }));
      
      renderStartTime.current = 0;
    }
  }, []);

  // 监控内存使用
  const measureMemory = useCallback(async () => {
    if ('memory' in performance) {
      const memInfo = (performance as any).memory;
      setMetrics(prev => ({
        ...prev,
        memoryUsage: memInfo.usedJSHeapSize / 1024 / 1024 // MB
      }));
    }
  }, []);

  // 监控事件监听器数量
  const countEventListeners = useCallback(() => {
    // 统计全局事件管理器中的监听器数量
    import('../utils/eventManager').then(({ globalEventManager }) => {
      setMetrics(prev => ({
        ...prev,
        eventListenerCount: globalEventManager.getListenerCount()
      }));
    });
  }, []);

  // 设置性能观察器
  useEffect(() => {
    if ('PerformanceObserver' in window) {
      observerRef.current = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.name.includes(componentName)) {
            setMetrics(prev => ({
              ...prev,
              renderTime: entry.duration
            }));
          }
        });
      });

      observerRef.current.observe({ entryTypes: ['measure'] });
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [componentName]);

  // 定期更新指标
  useEffect(() => {
    const interval = setInterval(() => {
      measureMemory();
      countEventListeners();
    }, 2000);

    return () => clearInterval(interval);
  }, [measureMemory, countEventListeners]);

  // 性能警告检查
  const checkPerformanceWarnings = useCallback(() => {
    const warnings: string[] = [];
    
    if (metrics.renderTime > 16) { // 超过一帧时间
      warnings.push(`渲染时间过长: ${metrics.renderTime.toFixed(2)}ms`);
    }
    
    if (metrics.memoryUsage > 100) { // 内存使用超过100MB
      warnings.push(`内存使用过高: ${metrics.memoryUsage.toFixed(2)}MB`);
    }
    
    if (metrics.eventListenerCount > 50) { // 事件监听器过多
      warnings.push(`事件监听器过多: ${metrics.eventListenerCount}个`);
    }
    
    if (warnings.length > 0) {
      console.warn(`[${componentName}] 性能警告:`, warnings);
    }
    
    return warnings;
  }, [componentName, metrics]);

  // 输出性能报告
  const generateReport = useCallback(() => {
    const report = {
      component: componentName,
      timestamp: new Date().toISOString(),
      metrics: { ...metrics },
      warnings: checkPerformanceWarnings()
    };
    
    console.log(`[性能报告] ${componentName}:`, report);
    return report;
  }, [componentName, metrics, checkPerformanceWarnings]);

  return {
    metrics,
    startMeasure,
    endMeasure,
    measureMemory,
    checkPerformanceWarnings,
    generateReport
  };
};

/**
 * 性能边界HOC
 * 自动监控包装组件的性能
 */
export function withPerformanceMonitor<P extends object>(
  Component: React.ComponentType<P>,
  componentName?: string
) {
  const PerformanceMonitoredComponent = (props: P) => {
    const name = componentName || Component.displayName || Component.name || 'Unknown';
    const { startMeasure, endMeasure, generateReport } = usePerformanceMonitor(name);

    useEffect(() => {
      startMeasure();
      return () => {
        endMeasure();
      };
    });

    // 定期生成性能报告
    useEffect(() => {
      const interval = setInterval(generateReport, 30000); // 每30秒
      return () => clearInterval(interval);
    }, [generateReport]);

    return React.createElement(Component, props);
  };

  PerformanceMonitoredComponent.displayName = `withPerformanceMonitor(${componentName || Component.displayName || Component.name})`;
  return PerformanceMonitoredComponent;
}