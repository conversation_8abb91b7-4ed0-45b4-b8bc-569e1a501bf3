import React from 'react';
import {
  But<PERSON>,
  Text,
  Dialog,
  DialogTrigger,
  DialogSurface,
  DialogTitle,
  DialogBody,
  DialogActions,
  DialogContent,
  Field,
  Input,
  MessageBar,
  MessageBarBody,
} from '@fluentui/react-components';

interface OperationDialogsProps {
  // 创建配置对话框状态
  createDialogOpen: boolean;
  newConfigName: string;
  isProcessing: boolean;
  onCreateDialogOpenChange: (open: boolean) => void;
  onNewConfigNameChange: (name: string) => void;
  onCreateSubmit: () => void;

  // 重命名配置对话框状态
  renameDialogOpen: boolean;
  renameConfigName: string;
  renameTargetName: string;
  onRenameDialogOpenChange: (open: boolean) => void;
  onRenameTargetNameChange: (name: string) => void;
  onRenameSubmit: () => void;

  // 删除确认对话框状态
  showDeleteConfirm: boolean;
  deleteConfigName: string | null;
  onDeleteDialogOpenChange: (open: boolean) => void;
  onDeleteConfirm: () => void;
}

/**
 * 操作对话框集合组件
 * 包含创建、重命名、删除等操作的对话框
 */
export const OperationDialogs: React.FC<OperationDialogsProps> = ({
  createDialogOpen,
  newConfigName,
  isProcessing,
  onCreateDialogOpenChange,
  onNewConfigNameChange,
  onCreateSubmit,
  renameDialogOpen,
  renameConfigName,
  renameTargetName,
  onRenameDialogOpenChange,
  onRenameTargetNameChange,
  onRenameSubmit,
  showDeleteConfirm,
  deleteConfigName,
  onDeleteDialogOpenChange,
  onDeleteConfirm,
}) => {
  return (
    <>
      {/* 创建配置对话框 */}
      <Dialog open={createDialogOpen} onOpenChange={(_, data) => onCreateDialogOpenChange(data.open)}>
        <DialogSurface>
          <DialogBody>
            <DialogTitle>创建新配置</DialogTitle>
            <DialogContent>
              <Field label="配置名称" required>
                <Input
                  value={newConfigName}
                  onChange={(_, data) => onNewConfigNameChange(data.value)}
                  placeholder="例如: development, production"
                />
              </Field>
              <Text size={200} style={{ color: 'var(--colorNeutralForeground3)', marginTop: '8px' }}>
                将自动添加 .json 扩展名
              </Text>
            </DialogContent>
            <DialogActions>
              <DialogTrigger disableButtonEnhancement>
                <Button appearance="secondary">取消</Button>
              </DialogTrigger>
              <Button 
                appearance="primary" 
                onClick={onCreateSubmit}
                disabled={!newConfigName.trim() || isProcessing}
              >
                {isProcessing ? '创建中...' : '创建'}
              </Button>
            </DialogActions>
          </DialogBody>
        </DialogSurface>
      </Dialog>

      {/* 重命名配置对话框 */}
      <Dialog open={renameDialogOpen} onOpenChange={(_, data) => onRenameDialogOpenChange(data.open)}>
        <DialogSurface>
          <DialogBody>
            <DialogTitle>重命名配置</DialogTitle>
            <DialogContent>
              <Field label="当前名称">
                <Input value={renameConfigName} disabled />
              </Field>
              <Field label="新名称" required style={{ marginTop: '16px' }}>
                <Input
                  value={renameTargetName}
                  onChange={(_, data) => onRenameTargetNameChange(data.value)}
                  placeholder="输入新的配置名称"
                />
              </Field>
              <Text size={200} style={{ color: 'var(--colorNeutralForeground3)', marginTop: '8px' }}>
                将自动添加 .json 扩展名
              </Text>
            </DialogContent>
            <DialogActions>
              <DialogTrigger disableButtonEnhancement>
                <Button appearance="secondary">取消</Button>
              </DialogTrigger>
              <Button 
                appearance="primary" 
                onClick={onRenameSubmit}
                disabled={!renameTargetName.trim() || isProcessing}
              >
                {isProcessing ? '重命名中...' : '重命名'}
              </Button>
            </DialogActions>
          </DialogBody>
        </DialogSurface>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog open={showDeleteConfirm} onOpenChange={(_, data) => !data.open && onDeleteDialogOpenChange(false)}>
        <DialogSurface>
          <DialogBody>
            <DialogTitle>确认删除</DialogTitle>
            <DialogContent>
              <MessageBar intent="warning">
                <MessageBarBody>
                  确定要删除配置文件 <strong>{deleteConfigName}</strong> 吗？此操作无法撤销。
                </MessageBarBody>
              </MessageBar>
            </DialogContent>
            <DialogActions>
              <DialogTrigger disableButtonEnhancement>
                <Button appearance="secondary">取消</Button>
              </DialogTrigger>
              <Button 
                appearance="primary" 
                onClick={onDeleteConfirm}
                disabled={isProcessing}
              >
                {isProcessing ? '删除中...' : '确认删除'}
              </Button>
            </DialogActions>
          </DialogBody>
        </DialogSurface>
      </Dialog>
    </>
  );
};