import React, { useState, useCallback } from 'react';
import {
  Button,
  Text,
  Dialog,
  DialogTrigger,
  DialogSurface,
  DialogTitle,
  DialogBody,
  DialogActions,
  DialogContent,
  Field,
  Input,
  MessageBar,
  MessageBarBody,
  Toast,
  ToastTitle,
  ToastBody,
  useToastController,
  Toaster,
} from '@fluentui/react-components';

import { useConfig } from '../hooks/useConfig';
import { FluentConfigList } from './FluentConfigList';
import { EditorContainer } from './EditorContainer';
import { useMainLayoutStyles } from '../styles/fluent-styles';
import { ConfigNameValidator } from '../utils/validation';
import { ThemeToggle } from './ThemeToggle';

export const FluentConfigManagerSimple: React.FC = () => {
  const {
    state,
    loadConfigurations,
    createConfiguration,
    readConfiguration,
    updateConfiguration,
    renameConfiguration,
    deleteConfiguration,
    activateConfiguration,
    selectConfiguration,
    clearCurrentConfig,
    showDeleteConfirm,
    hideDeleteConfirm,
  } = useConfig();

  const styles = useMainLayoutStyles();
  const { dispatchToast } = useToastController();

  // 本地状态
  const [newConfigName, setNewConfigName] = useState('');
  const [renameConfigName, setRenameConfigName] = useState('');
  const [renameTargetName, setRenameTargetName] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [renameDialogOpen, setRenameDialogOpen] = useState(false);
  
  // 编辑状态管理
  const [editingFiles, setEditingFiles] = useState<Set<string>>(new Set());

  // 显示Toast通知
  const showToast = useCallback((title: string, body?: string, intent: 'success' | 'error' | 'warning' = 'success') => {
    dispatchToast(
      <Toast>
        <ToastTitle>{title}</ToastTitle>
        {body && <ToastBody>{body}</ToastBody>}
      </Toast>,
      { intent, timeout: 3000 }
    );
  }, [dispatchToast]);

  // 处理新建配置
  const handleCreateNew = useCallback(() => {
    setCreateDialogOpen(true);
    setNewConfigName('');
  }, []);

  // 处理创建配置提交
  const handleCreateSubmit = useCallback(async () => {
    if (!newConfigName.trim()) return;

    const validation = ConfigNameValidator.validate(newConfigName);
    if (!validation.isValid) {
      showToast('配置名称无效', validation.errors.join(', '), 'error');
      return;
    }

    const finalName = newConfigName.endsWith('.json') ? newConfigName : `${newConfigName}.json`;
    const existingNames = state.configurations.map(c => c.name);
    
    if (existingNames.includes(finalName)) {
      showToast('名称冲突', '配置文件名已存在，请选择其他名称', 'error');
      return;
    }

    setIsProcessing(true);
    try {
      const defaultContent = JSON.stringify({
        name: newConfigName,
        version: "1.0.0",
        settings: {}
      }, null, 2);

      await createConfiguration(finalName, defaultContent);
      await readConfiguration(finalName);
      setCreateDialogOpen(false);
      setNewConfigName('');
      showToast('配置创建成功', `已创建配置文件: ${finalName}`);
    } catch (error) {
      console.error('创建配置失败:', error);
      showToast('创建失败', '无法创建配置文件', 'error');
    } finally {
      setIsProcessing(false);
    }
  }, [newConfigName, state.configurations, createConfiguration, readConfiguration, showToast]);

  // 处理编辑配置
  const handleEdit = useCallback(async (configName: string) => {
    try {
      // 先清空当前配置，避免内容串扰
      clearCurrentConfig();
      selectConfiguration(null);
      
      // 稍微延迟加载新配置，确保状态清空
      await new Promise(resolve => setTimeout(resolve, 50));
      
      await readConfiguration(configName);
      selectConfiguration(configName);
      showToast('配置已加载', `已打开配置文件: ${configName}`, 'success');
    } catch (error) {
      console.error('读取配置失败:', error);
      showToast('读取失败', '无法读取配置文件', 'error');
    }
  }, [readConfiguration, selectConfiguration, clearCurrentConfig, showToast]);

  // 处理重命名
  const handleRename = useCallback((configName: string) => {
    setRenameConfigName(configName);
    setRenameTargetName(configName.replace('.json', ''));
    setRenameDialogOpen(true);
  }, []);

  // 处理重命名提交
  const handleRenameSubmit = useCallback(async () => {
    if (!renameTargetName.trim() || !renameConfigName) return;

    const validation = ConfigNameValidator.validate(renameTargetName);
    if (!validation.isValid) {
      showToast('配置名称无效', validation.errors.join(', '), 'error');
      return;
    }

    const newName = renameTargetName.endsWith('.json') ? renameTargetName : `${renameTargetName}.json`;
    const existingNames = state.configurations.map(c => c.name);
    
    if (existingNames.includes(newName) && newName !== renameConfigName) {
      showToast('名称冲突', '配置文件名已存在，请选择其他名称', 'error');
      return;
    }

    if (newName === renameConfigName) {
      setRenameDialogOpen(false);
      return;
    }

    setIsProcessing(true);
    try {
      await renameConfiguration(renameConfigName, newName);
      
      // 如果当前选中的文件被重命名，更新选中状态
      if (state.selectedConfigName === renameConfigName) {
        selectConfiguration(newName);
        // 重新读取重命名后的文件
        await readConfiguration(newName);
      }
      
      setRenameDialogOpen(false);
      setRenameConfigName('');
      setRenameTargetName('');
      showToast('重命名成功', `已重命名为: ${newName}`);
    } catch (error) {
      console.error('重命名配置失败:', error);
      showToast('重命名失败', '无法重命名配置文件', 'error');
    } finally {
      setIsProcessing(false);
    }
  }, [renameTargetName, renameConfigName, state.configurations, state.selectedConfigName, renameConfiguration, selectConfiguration, readConfiguration, showToast]);

  // 处理删除确认
  const handleDeleteConfirm = useCallback(async () => {
    if (!state.deleteConfigName) return;

    setIsProcessing(true);
    try {
      await deleteConfiguration(state.deleteConfigName);
      if (state.selectedConfigName === state.deleteConfigName) {
        selectConfiguration(null);
        clearCurrentConfig();
      }
      hideDeleteConfirm();
      showToast('删除成功', `已删除配置: ${state.deleteConfigName}`);
    } catch (error) {
      console.error('删除配置失败:', error);
      showToast('删除失败', '无法删除配置文件', 'error');
    } finally {
      setIsProcessing(false);
    }
  }, [state.deleteConfigName, state.selectedConfigName, deleteConfiguration, selectConfiguration, clearCurrentConfig, hideDeleteConfirm, showToast]);

  // 处理激活配置
  const handleActivate = useCallback(async (configName: string) => {
    if (!state.appConfig?.target_directory) {
      showToast('目录未设置', '请先设置目标目录', 'warning');
      return;
    }

    const config = state.configurations.find(c => c.name === configName);
    if (!config?.is_valid) {
      showToast('格式错误', '配置文件格式错误，无法激活', 'error');
      return;
    }

    try {
      await activateConfiguration(configName);
      showToast('激活成功', `配置 ${configName} 已激活`);
    } catch (error) {
      console.error('激活配置失败:', error);
      showToast('激活失败', '无法激活配置文件', 'error');
    }
  }, [state.appConfig, state.configurations, activateConfiguration, showToast]);

  // 处理文件内容变化
  const handleContentChange = useCallback((fileName: string, hasChanges: boolean) => {
    setEditingFiles(prev => {
      const newSet = new Set(prev);
      if (hasChanges) {
        newSet.add(fileName);
      } else {
        newSet.delete(fileName);
      }
      return newSet;
    });
  }, []);

  // 处理保存文件内容
  const handleSaveContent = useCallback(async (content: string) => {
    if (!state.selectedConfigName) {
      throw new Error('没有选中的配置文件');
    }

    try {
      // 验证JSON格式
      JSON.parse(content);
    } catch (error) {
      throw new Error('JSON格式错误，请检查语法');
    }

    try {
      await updateConfiguration(state.selectedConfigName, content);
      // 移除编辑状态
      setEditingFiles(prev => {
        const newSet = new Set(prev);
        newSet.delete(state.selectedConfigName!);
        return newSet;
      });
      showToast('保存成功', `配置文件 ${state.selectedConfigName} 已保存`);
    } catch (error) {
      console.error('保存配置失败:', error);
      throw new Error('保存失败，请重试');
    }
  }, [state.selectedConfigName, updateConfiguration, showToast]);

  return (
    <div className={styles.root}>
      <Toaster />
      
      {/* 头部 */}
      <header className={styles.header}>
        <Text className={styles.headerTitle}>ClaudeSwitch 配置切换工具</Text>
        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          <Text size={200} style={{ color: 'var(--colorNeutralForeground3)' }}>
            基于Microsoft Fluent 2重新设计
          </Text>
          <ThemeToggle />
        </div>
      </header>

      {/* 主内容 */}
      <div className={styles.content}>
        {/* 左侧边栏 */}
        <div className={styles.sidebar}>
          <FluentConfigList
            configurations={state.configurations}
            selectedConfig={state.selectedConfigName}
            activeConfig={state.appConfig?.last_active_config || null}
            editingFiles={editingFiles}
            isLoading={state.isLoadingConfigurations}
            error={state.configurationsError}
            onSelect={selectConfiguration}
            onEdit={handleEdit}
            onRename={handleRename}
            onDelete={showDeleteConfirm}
            onActivate={handleActivate}
            onRefresh={loadConfigurations}
            onCreateNew={handleCreateNew}
          />
        </div>

        {/* 主内容区 */}
        <div className={styles.mainContent}>
          {state.selectedConfigName && state.currentConfig ? (
            <EditorContainer
              fileName={state.selectedConfigName}
              fileContent={state.currentConfig.content || '{}'}
              onSave={handleSaveContent}
              onContentChange={(_, hasChanges) => {
                if (state.selectedConfigName) {
                  handleContentChange(state.selectedConfigName, hasChanges);
                }
              }}
              isLoading={state.isSaving}
            />
          ) : (
            <div style={{
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'var(--colorNeutralForeground3)',
              fontSize: '16px',
              textAlign: 'center'
            }}>
              <Text size={400}>请从左侧选择一个配置文件进行编辑</Text>
            </div>
          )}
        </div>
      </div>

      {/* 创建配置对话框 */}
      <Dialog open={createDialogOpen} onOpenChange={(_, data) => setCreateDialogOpen(data.open)}>
        <DialogSurface>
          <DialogBody>
            <DialogTitle>创建新配置</DialogTitle>
            <DialogContent>
              <Field label="配置名称" required>
                <Input
                  value={newConfigName}
                  onChange={(_, data) => setNewConfigName(data.value)}
                  placeholder="例如: development, production"
                />
              </Field>
              <Text size={200} style={{ color: 'var(--colorNeutralForeground3)', marginTop: '8px' }}>
                将自动添加 .json 扩展名
              </Text>
            </DialogContent>
            <DialogActions>
              <DialogTrigger disableButtonEnhancement>
                <Button appearance="secondary">取消</Button>
              </DialogTrigger>
              <Button 
                appearance="primary" 
                onClick={handleCreateSubmit}
                disabled={!newConfigName.trim() || isProcessing}
              >
                {isProcessing ? '创建中...' : '创建'}
              </Button>
            </DialogActions>
          </DialogBody>
        </DialogSurface>
      </Dialog>

      {/* 重命名配置对话框 */}
      <Dialog open={renameDialogOpen} onOpenChange={(_, data) => setRenameDialogOpen(data.open)}>
        <DialogSurface>
          <DialogBody>
            <DialogTitle>重命名配置</DialogTitle>
            <DialogContent>
              <Field label="当前名称">
                <Input value={renameConfigName} disabled />
              </Field>
              <Field label="新名称" required style={{ marginTop: '16px' }}>
                <Input
                  value={renameTargetName}
                  onChange={(_, data) => setRenameTargetName(data.value)}
                  placeholder="输入新的配置名称"
                />
              </Field>
              <Text size={200} style={{ color: 'var(--colorNeutralForeground3)', marginTop: '8px' }}>
                将自动添加 .json 扩展名
              </Text>
            </DialogContent>
            <DialogActions>
              <DialogTrigger disableButtonEnhancement>
                <Button appearance="secondary">取消</Button>
              </DialogTrigger>
              <Button 
                appearance="primary" 
                onClick={handleRenameSubmit}
                disabled={!renameTargetName.trim() || isProcessing}
              >
                {isProcessing ? '重命名中...' : '重命名'}
              </Button>
            </DialogActions>
          </DialogBody>
        </DialogSurface>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog open={state.showDeleteConfirm} onOpenChange={(_, data) => !data.open && hideDeleteConfirm()}>
        <DialogSurface>
          <DialogBody>
            <DialogTitle>确认删除</DialogTitle>
            <DialogContent>
              <MessageBar intent="warning">
                <MessageBarBody>
                  确定要删除配置文件 <strong>{state.deleteConfigName}</strong> 吗？此操作无法撤销。
                </MessageBarBody>
              </MessageBar>
            </DialogContent>
            <DialogActions>
              <DialogTrigger disableButtonEnhancement>
                <Button appearance="secondary">取消</Button>
              </DialogTrigger>
              <Button 
                appearance="primary" 
                onClick={handleDeleteConfirm}
                disabled={isProcessing}
              >
                {isProcessing ? '删除中...' : '确认删除'}
              </Button>
            </DialogActions>
          </DialogBody>
        </DialogSurface>
      </Dialog>
    </div>
  );
};