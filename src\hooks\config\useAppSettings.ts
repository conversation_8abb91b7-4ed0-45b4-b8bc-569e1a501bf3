import { useCallback } from 'react';
import { open } from '@tauri-apps/plugin-dialog';
import { TauriAPI } from '../../types/tauri-api';
import { AppConfig, defaultAppConfig, NotificationConfig } from '../../types/config';

/**
 * 应用设置管理Hook参数接口
 */
interface UseAppSettingsParams {
  dispatch: (action: any) => void;
  showNotification: (config: NotificationConfig) => void;
}

/**
 * 应用设置管理Hook
 * 专门负责应用配置的加载、保存和目录选择
 */
export function useAppSettings({ dispatch, showNotification }: UseAppSettingsParams) {
  // 加载应用配置
  const loadAppConfig = useCallback(async () => {
    dispatch({ type: 'SET_APP_CONFIG_LOADING', payload: true });
    try {
      const config = await TauriAPI.loadAppConfig();
      dispatch({ type: 'SET_APP_CONFIG', payload: config || defaultAppConfig });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      dispatch({ type: 'SET_APP_CONFIG_ERROR', payload: errorMessage });
      showNotification({
        type: 'error',
        title: '加载应用配置失败',
        message: errorMessage,
      });
    } finally {
      dispatch({ type: 'SET_APP_CONFIG_LOADING', payload: false });
    }
  }, [dispatch, showNotification]);

  // 保存应用配置
  const saveAppConfig = useCallback(async (config: AppConfig) => {
    try {
      await TauriAPI.saveAppConfig(config);
      dispatch({ type: 'SET_APP_CONFIG', payload: config });
      showNotification({
        type: 'success',
        title: '应用配置已保存',
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      showNotification({
        type: 'error',
        title: '保存应用配置失败',
        message: errorMessage,
      });
      throw error;
    }
  }, [dispatch, showNotification]);

  // 选择目标目录
  const selectTargetDirectory = useCallback(async () => {
    try {
      const selected = await open({
        directory: true,
        multiple: false,
        title: '选择目标目录',
      });
      
      if (selected && typeof selected === 'string') {
        return selected;
      }
      return null;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      showNotification({
        type: 'error',
        title: '选择目录失败',
        message: errorMessage,
      });
      return null;
    }
  }, [showNotification]);

  return {
    loadAppConfig,
    saveAppConfig,
    selectTargetDirectory,
  };
}