import { useState, useEffect, useCallback, useRef } from 'react';
import { flushSync } from 'react-dom';
import { Theme } from '@fluentui/react-components';
import { 
  getTheme, 
  ThemeMode 
} from '../theme';
import { globalEventManager, createSafeEventListener } from '../utils/eventManager';

// 主题存储键名
const THEME_STORAGE_KEY = 'claude-switch-theme';


// 统一的CSS变量更新函数
const updateCSSVariables = (theme: Theme): Promise<void> => {
  return new Promise((resolve) => {
    const root = document.documentElement;
    
    // 批量更新CSS变量，减少重绘次数
    const updates: Array<[string, string]> = [];
    
    Object.entries(theme).forEach(([key, value]) => {
      if (typeof value === 'string') {
        updates.push([`--${key}`, value]);
      }
    });
    
    // 使用一次性批量更新，确保原子性
    requestAnimationFrame(() => {
      try {
        updates.forEach(([property, value]) => {
          root.style.setProperty(property, value);
        });
        resolve();
      } catch (error) {
        console.warn('CSS变量更新失败:', error);
        resolve(); // 即使失败也要resolve，避免阻塞
      }
    });
  });
};

export const useTheme = () => {
  // 从localStorage读取主题设置，默认为system
  const [themeMode, setThemeMode] = useState<ThemeMode>(() => {
    try {
      const saved = localStorage.getItem(THEME_STORAGE_KEY);
      return (saved as ThemeMode) || 'system';
    } catch {
      return 'system';
    }
  });

  const [currentTheme, setCurrentTheme] = useState<Theme>(() => getTheme(themeMode));
  const updateCountRef = useRef(0);

  // 简化的主题应用函数
  const applyTheme = useCallback(async (mode: ThemeMode, source: 'user' | 'system' = 'user') => {    
    try {
      const newTheme = getTheme(mode);
      
      // 同步更新React状态
      flushSync(() => {
        setThemeMode(mode);
        setCurrentTheme(newTheme);
      });

      // 应用CSS变量
      await updateCSSVariables(newTheme);
      
      // 触发主题变化事件
      window.dispatchEvent(new CustomEvent('theme-changed', { 
        detail: { mode, theme: newTheme, source } 
      }));
      
      // 保存到localStorage（仅用户操作时）
      if (source === 'user') {
        try {
          localStorage.setItem(THEME_STORAGE_KEY, mode);
        } catch (error) {
          console.warn('无法保存主题设置到localStorage:', error);
        }
      }
    } catch (error) {
      console.error('主题应用失败:', error);
    }
  }, []);

  // 系统主题变化监听 - 简化事件管理
  useEffect(() => {
    if (themeMode !== 'system') {
      globalEventManager.removeListeners('system-theme');
      return;
    }

    const handleSystemThemeChange = createSafeEventListener(() => {
      if (themeMode === 'system') {
        applyTheme('system', 'system');
      }
    });

    globalEventManager.addMediaQueryListener(
      'system-theme',
      '(prefers-color-scheme: dark)',
      handleSystemThemeChange
    );

    return () => {
      globalEventManager.removeListeners('system-theme');
    };
  }, [themeMode, applyTheme]);

  // 直接的主题更新函数
  const updateThemeMode = useCallback((mode: ThemeMode) => {
    applyTheme(mode, 'user');
  }, [applyTheme]);

  // 切换到下一个主题
  const toggleTheme = useCallback(() => {
    const nextMode: ThemeMode = 
      themeMode === 'light' ? 'dark' : 
      themeMode === 'dark' ? 'system' : 'light';
    updateThemeMode(nextMode);
  }, [themeMode, updateThemeMode]);

  // 获取当前主题信息
  const getThemeInfo = useCallback(() => {
    const isDark = themeMode === 'dark' || 
      (themeMode === 'system' && window.matchMedia('(prefers-color-scheme: dark)').matches);
    
    return {
      mode: themeMode,
      isDark,
      displayName: 
        themeMode === 'light' ? '浅色模式' :
        themeMode === 'dark' ? '深色模式' : '跟随系统'
    };
  }, [themeMode]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      // 清理事件监听器
      globalEventManager.removeListeners('system-theme');
    };
  }, []);

  return {
    theme: currentTheme,
    themeMode,
    updateThemeMode,
    toggleTheme,
    getThemeInfo,
    // 便捷的主题检查
    isDark: getThemeInfo().isDark,
    isLight: !getThemeInfo().isDark,
    isSystem: themeMode === 'system',
    // 提供更新状态
    isUpdating: false,
    // 提供更新统计
    updateCount: updateCountRef.current,
  };
};