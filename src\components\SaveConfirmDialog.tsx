import React from 'react';
import {
  Dialog,
  DialogSurface,
  DialogTitle,
  DialogBody,
  DialogActions,
  DialogContent,
  Button,
  Text,
  MessageBar,
  MessageBarBody,
} from '@fluentui/react-components';
import {
  Warning24Regular,
  Save24Regular,
  Dismiss24Regular,
} from '@fluentui/react-icons';

interface SaveConfirmDialogProps {
  isOpen: boolean;
  configName: string;
  onSave: () => Promise<void>;
  onDiscard: () => void;
  onCancel: () => void;
}

/**
 * 保存确认对话框组件
 * 当用户尝试关闭或切换配置文件且存在未保存变更时显示
 */
export const SaveConfirmDialog: React.FC<SaveConfirmDialogProps> = ({
  isOpen,
  configName,
  onSave,
  onDiscard,
  onCancel,
}) => {
  const [isSaving, setIsSaving] = React.useState(false);

  const handleSave = async () => {
    setIsSaving(true);
    try {
      await onSave();
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(_, data) => !data.open && onCancel()}>
      <DialogSurface>
        <DialogBody>
          <DialogTitle>
            <Warning24Regular style={{ marginRight: '8px' }} />
            保存配置文件
          </DialogTitle>
          
          <DialogContent>
            <MessageBar intent="warning">
              <MessageBarBody>
                配置文件 <strong>{configName}</strong> 的内容已发生变更，是否要保存这些更改？
              </MessageBarBody>
            </MessageBar>
            
            <Text 
              size={200} 
              style={{ 
                color: 'var(--colorNeutralForeground3)', 
                marginTop: '12px',
                display: 'block'
              }}
            >
              如果不保存，您的更改将会丢失。
            </Text>
          </DialogContent>
          
          <DialogActions>
            <Button 
              appearance="secondary" 
              onClick={onCancel}
              disabled={isSaving}
            >
              取消
            </Button>
            
            <Button 
              appearance="subtle" 
              onClick={onDiscard}
              disabled={isSaving}
              icon={<Dismiss24Regular />}
            >
              不保存
            </Button>
            
            <Button 
              appearance="primary" 
              onClick={handleSave}
              disabled={isSaving}
              icon={<Save24Regular />}
            >
              {isSaving ? '保存中...' : '保存'}
            </Button>
          </DialogActions>
        </DialogBody>
      </DialogSurface>
    </Dialog>
  );
};