{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[]", "target": 6114574821762673293, "profile": 3316208278650011218, "path": 10763286916239946207, "deps": [[3572497473143799953, "build_script_build", false, 12052662461955653564], [5986029879202738730, "log", false, 7118055830611606725], [6825153089788476225, "tauri_plugin_dialog", false, 2786971758349042662], [8256202458064874477, "dirs", false, 14294322098949673127], [8319709847752024821, "uuid", false, 16814845787692117583], [9689903380558560274, "serde", false, 8528455792048297485], [9897246384292347999, "chrono", false, 7818545561544389998], [12092653563678505622, "tauri", false, 13745668990023190455], [12103695930867503580, "env_logger", false, 9490875864084089477], [12504415026414629397, "tauri_plugin_fs", false, 6117226154364144900], [16362055519698394275, "serde_json", false, 11965284030366971334], [16702348383442838006, "tauri_plugin_opener", false, 8448843187729236045], [17531218394775549125, "tokio", false, 13633817360127531078]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\claude-switch-f54b965bd5aaa242\\dep-test-lib-claude_switch_lib", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}