import React, { useCallback } from 'react';
import { ConfigInfo, AppConfig } from '../../types/config';
import { FluentConfigList } from '../FluentConfigList';

interface ConfigListPanelProps {
  configurations: ConfigInfo[];
  selectedConfig: string | null;
  activeConfig: string | null;
  editingFiles: Set<string>;
  isLoading: boolean;
  error: string | null;
  appConfig: AppConfig | null;
  onSelect: (configName: string | null) => void;
  onEdit: (configName: string) => void;
  onRename: (configName: string) => void;
  onDelete: (configName: string) => void;
  onActivate: (configName: string) => void;
  onRefresh: () => void;
  onCreateNew: () => void;
}

/**
 * 配置列表面板组件
 * 负责显示配置文件列表和相关操作
 */
export const ConfigListPanel: React.FC<ConfigListPanelProps> = ({
  configurations,
  selectedConfig,
  activeConfig,
  editingFiles,
  isLoading,
  error,
  appConfig,
  onSelect,
  onEdit,
  onRename,
  onDelete,
  onActivate,
  onRefresh,
  onCreateNew,
}) => {
  // 处理激活配置的包装函数，添加验证逻辑
  const handleActivate = useCallback((configName: string) => {
    if (!appConfig?.target_directory) {
      // 这里的错误处理将由容器组件提供
      return;
    }

    const config = configurations.find(c => c.name === configName);
    if (!config?.is_valid) {
      // 这里的错误处理将由容器组件提供
      return;
    }

    onActivate(configName);
  }, [appConfig, configurations, onActivate]);

  return (
    <FluentConfigList
      configurations={configurations}
      selectedConfig={selectedConfig}
      activeConfig={activeConfig}
      editingFiles={editingFiles}
      isLoading={isLoading}
      error={error}
      onSelect={onSelect}
      onEdit={onEdit}
      onRename={onRename}
      onDelete={onDelete}
      onActivate={handleActivate}
      onRefresh={onRefresh}
      onCreateNew={onCreateNew}
    />
  );
};